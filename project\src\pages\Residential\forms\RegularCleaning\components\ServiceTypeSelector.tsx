import React from 'react';
import { Home, Sparkles, Truck, Construction, Calendar, Brush, GlassWater, Waves, Droplets, Flame, Sofa } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'residential_regular',
    icon: Home,
    label: 'Regular Cleaning',
    description: 'Comprehensive home cleaning service',
    features: ['All living areas', 'Kitchen & bathrooms', 'Dusting & vacuuming']
  },
  {
    id: 'residential_deep',
    icon: Sparkles,
    label: 'Deep Cleaning',
    description: 'Thorough deep cleaning service',
    features: ['Detailed cleaning', 'Hard to reach areas', 'Baseboards & trim']
  },
  {
    id: 'residential_move',
    icon: Truck,
    label: 'Move In/Out',
    description: 'Specialized moving cleaning service',
    features: ['Cabinet cleaning', 'Appliance cleaning', 'Window cleaning']
  },
  {
    id: 'construction',
    icon: Construction,
    label: 'Post Construction',
    description: 'After construction cleanup',
    features: ['Debris removal', 'Dust control', 'Surface cleaning']
  },
  {
    id: 'event',
    icon: Calendar,
    label: 'Event Cleaning',
    description: 'Pre and post event cleaning',
    features: ['Setup assistance', 'Post-event cleanup', 'Quick turnaround']
  },
  {
    id: 'carpet',
    icon: Brush,
    label: 'Carpet Cleaning',
    description: 'Professional carpet cleaning',
    features: ['Deep extraction', 'Stain removal', 'Deodorizing']
  },
  {
    id: 'upholstery',
    icon: Sofa,
    label: 'Upholstery Cleaning',
    description: 'Furniture and upholstery cleaning',
    features: ['Sofa cleaning', 'Chair cleaning', 'Stain removal']
  },
  {
    id: 'window',
    icon: GlassWater,
    label: 'Window Cleaning',
    description: 'Interior & exterior window cleaning',
    features: ['Streak-free cleaning', 'Frame cleaning', 'Screen cleaning']
  },
  {
    id: 'pressure',
    icon: Waves,
    label: 'Pressure Washing',
    description: 'Exterior surface cleaning',
    features: ['Driveway cleaning', 'Deck/patio cleaning', 'Siding cleaning']
  },
  {
    id: 'sanitization',
    icon: Droplets,
    label: 'Sanitization & Disinfection',
    description: 'Deep sanitization services',
    features: ['Surface disinfection', 'High-touch areas', 'Odor elimination']
  },
  {
    id: 'pool',
    icon: Droplets,
    label: 'Pool Cleaning (Subscription ONLY)',
    description: 'Regular pool maintenance service',
    features: ['Water testing', 'Chemical balancing', 'Filter cleaning']
  },
  {
    id: 'chimney',
    icon: Flame,
    label: 'Chimney Cleaning',
    description: 'Subscription or one-time service',
    features: ['Soot removal', 'Creosote removal', 'Safety inspection']
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="text-2xl font-semibold text-gray-900">
          Select Your Service
        </h3>
        <p className="text-gray-600 mt-2">
          Choose the type of cleaning service you need
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`group relative w-full p-6 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex flex-col space-y-4">
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg transition-colors ${
                    isSelected ? 'bg-brand-100' : 'bg-gray-50 group-hover:bg-brand-50'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      isSelected ? 'text-brand-600' : 'text-gray-600'
                    }`} />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{service.label}</h3>
                    <p className="text-sm text-gray-600">{service.description}</p>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2 pl-14">
                  {service.features.map((feature, i) => (
                    <motion.div 
                      key={i}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 + (i * 0.1) }}
                      className="flex items-center text-gray-600"
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                      <span className="text-sm">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
