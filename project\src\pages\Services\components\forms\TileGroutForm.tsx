import React from 'react';
import { Form, FormField } from './FormFields';

interface TileGroutFormProps {
  onSubmit: (data: any) => void;
}

export function TileGroutForm({ onSubmit }: TileGroutFormProps) {
  return (
    <Form onSubmit={onSubmit}>
      <FormField label="Approximate Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Tile Type" required>
        <select
          name="tileType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="ceramic">Ceramic</option>
          <option value="porcelain">Porcelain</option>
          <option value="natural-stone">Natural Stone</option>
          <option value="slate">Slate</option>
          <option value="marble">Marble</option>
        </select>
      </FormField>

      <FormField label="Grout Condition" required>
        <select
          name="groutCondition"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="light">Light Staining</option>
          <option value="moderate">Moderate Staining</option>
          <option value="heavy">Heavy Staining</option>
          <option value="damaged">Damaged/Cracked</option>
        </select>
      </FormField>

      <FormField label="Services Needed">
        <div className="space-y-2">
          {[
            'Deep Cleaning',
            'Grout Sealing',
            'Color Sealing',
            'Grout Repair',
            'Anti-Slip Treatment'
          ].map((service) => (
            <label key={service} className="flex items-center">
              <input
                type="checkbox"
                name="services"
                value={service.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{service}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Additional Notes">
        <textarea
          name="notes"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific concerns or special requirements?"
        />
      </FormField>
    </Form>
  );
}
