import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Quo<PERSON>, ChevronLeft, ChevronRight, <PERSON><PERSON><PERSON><PERSON><PERSON>, Award, ThumbsUp } from 'lucide-react';

const reviews = [
  {
    name: "<PERSON>",
    role: "Hospital Administrator",
    location: "Los Angeles, CA",
    rating: 5,
    content: "Empire Pro's medical facility cleaning service is exceptional. Their attention to sanitization protocols and detail is unmatched. They understand the unique requirements of healthcare environments.",
    image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    badge: "Healthcare Partner"
  },
  {
    name: "<PERSON>",
    role: "Corporate Facilities Director",
    location: "Chicago, IL",
    rating: 5,
    content: "Managing multiple office locations requires a reliable cleaning partner. Empire Pro consistently delivers exceptional results across all our facilities. Their standardized processes ensure quality everywhere.",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    badge: "Enterprise Client"
  },
  {
    name: "<PERSON>",
    role: "School Principal",
    location: "Miami, FL",
    rating: 5,
    content: "The safety and cleanliness of our educational facility is paramount. Empire Pro has been instrumental in maintaining a healthy learning environment for our students and staff.",
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    badge: "Education Partner"
  },
  {
    name: "David Thompson",
    role: "Restaurant Owner",
    location: "Austin, TX",
    rating: 5,
    content: "In the food service industry, cleanliness is critical. Empire Pro's team understands our high standards and consistently exceeds expectations. Their attention to detail is remarkable.",
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    badge: "Verified Business"
  },
  {
    name: "Sarah Williams",
    role: "Property Manager",
    location: "Seattle, WA",
    rating: 5,
    content: "Managing a large commercial property requires a cleaning service that can handle scale. Empire Pro's team is always reliable, professional, and thorough in their work.",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    badge: "Property Partner"
  },
  {
    name: "Michael Lee",
    role: "Manufacturing Plant Manager",
    location: "Detroit, MI",
    rating: 5,
    content: "Industrial cleaning requires specialized knowledge and equipment. Empire Pro's team has the expertise to handle our facility's unique cleaning requirements safely and effectively.",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80",
    badge: "Industrial Partner"
  }
];

export function Reviews() {
  const [currentPage, setCurrentPage] = useState(0);
  const reviewsPerPage = 3;
  const totalPages = Math.ceil(reviews.length / reviewsPerPage);

  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  const currentReviews = reviews.slice(
    currentPage * reviewsPerPage,
    (currentPage + 1) * reviewsPerPage
  );

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Trusted by Businesses Across America
          </h2>
          <motion.div
            initial={{ scaleX: 0 }}
            whileInView={{ scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="w-24 h-1 bg-brand-600 mx-auto mb-8"
          />
          
          {/* Rating Overview */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="inline-block bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-6 sm:mb-8">
            <div className="flex items-center justify-center mb-3 sm:mb-4">
              <img 
                src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg"
                alt="Google"
                className="h-6 w-6 sm:h-8 sm:w-8 mr-2 sm:mr-3"
              />
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>

            <div className="text-xl sm:text-2xl font-bold text-gray-900">
              4.9 out of 5
            </div>
            <div className="text-xs sm:text-sm text-gray-600 mt-1">Based on 500+ reviews</div>
          </motion.div>

          {/* Trust Badges */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="flex flex-wrap justify-center gap-3 sm:gap-4">
            <div className="flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-blue-50 rounded-lg sm:rounded-xl">
              <BadgeCheck className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mr-1.5 sm:mr-2" />
              <span className="text-xs sm:text-sm font-medium text-blue-700">Google Verified</span>
            </div>
            <div className="flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-green-50 rounded-lg sm:rounded-xl">
              <Award className="w-4 h-4 sm:w-5 sm:h-5 text-green-600 mr-1.5 sm:mr-2" />
              <span className="text-xs sm:text-sm font-medium text-green-700">Top Rated 2024</span>
            </div>
            <div className="flex items-center px-3 py-1.5 sm:px-4 sm:py-2 bg-purple-50 rounded-lg sm:rounded-xl">
              <ThumbsUp className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 mr-1.5 sm:mr-2" />
              <span className="text-xs sm:text-sm font-medium text-purple-700">98% Satisfaction</span>
            </div>
          </motion.div>
        </motion.div>

        <div className="relative">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {currentReviews.map((review, index) => (
              <motion.div
                key={`${review.name}-${currentPage}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="p-8">
                  <div className="flex items-center mb-6">
                    <img
                      src={review.image}
                      alt={review.name}
                      className="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <div className="flex items-center">
                        <h3 className="font-semibold text-gray-900">{review.name}</h3>
                        <BadgeCheck className="w-4 h-4 text-blue-500 ml-1" />
                      </div>
                      <p className="text-sm text-gray-600">{review.role}</p>
                      <p className="text-sm text-brand-600">{review.location}</p>
                    </div>
                  </div>

                  {review.badge && (
                    <div className="mb-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {review.badge}
                      </span>
                    </div>
                  )}

                  <div className="flex mb-4">
                    {[...Array(review.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>

                  <Quote className="w-8 h-8 text-brand-200 mb-4" />
                  <p className="text-gray-600">{review.content}</p>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="flex justify-center mt-8 space-x-2">
            {[...Array(totalPages)].map((_, i) => (
              <button
                key={i}
                onClick={() => setCurrentPage(i)}
                className={`w-2 h-2 rounded-full transition-all ${
                  currentPage === i ? 'w-6 bg-brand-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          <button
            onClick={prevPage}
            className="absolute -left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors"
          >
            <ChevronLeft className="w-6 h-6 text-gray-600" />
          </button>

          <button
            onClick={nextPage}
            className="absolute -right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors"
          >
            <ChevronRight className="w-6 h-6 text-gray-600" />
          </button>
        </div>
      </div>
    </section>
  );
}
