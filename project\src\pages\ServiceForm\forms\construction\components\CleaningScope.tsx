import React from 'react';
import { Sparkles, Target, Shield, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface CleaningScopeProps {
  scope: {
    phase: string;
    areas: string[];
    specialRequirements: string[];
  };
  onChange: (scope: any) => void;
}

export function CleaningScope({ scope, onChange }: CleaningScopeProps) {
  const phases = [
    {
      id: 'rough',
      label: 'Rough Clean',
      description: 'Initial cleanup during construction'
    },
    {
      id: 'final',
      label: 'Final Clean',
      description: 'Detailed cleaning after completion'
    },
    {
      id: 'both',
      label: 'Both Phases',
      description: 'Complete cleaning service'
    }
  ];

  const areas = [
    'Interior Surfaces',
    'Windows & Glass',
    'Floors & Carpets',
    'Walls & Ceilings',
    'HVAC Systems',
    'Light Fixtures',
    'Exterior Areas',
    'Common Areas'
  ];

  const requirements = [
    'HEPA Filtration',
    'Dust Containment',
    'Safety Compliance',
    'EPA Guidelines',
    'OSHA Standards',
    'Green Cleaning',
    'Special Equipment',
    'Debris Removal'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Sparkles className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Cleaning Scope</h3>
          <p className="text-gray-600">Define your cleaning requirements</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        {/* Cleaning Phase */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Cleaning Phase <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {phases.map((phase) => (
              <label
                key={phase.id}
                className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.phase === phase.id
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="phase"
                  value={phase.id}
                  checked={scope.phase === phase.id}
                  onChange={(e) => onChange({ ...scope, phase: e.target.value })}
                  className="sr-only"
                />
                <div className="font-medium text-gray-900 mb-1">{phase.label}</div>
                <div className="text-sm text-gray-600">{phase.description}</div>
              </label>
            ))}
          </div>
        </div>

        {/* Areas to Clean */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Areas to Clean <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {areas.map((area) => (
              <label
                key={area}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.areas.includes(area)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.areas.includes(area)}
                  onChange={(e) => {
                    const newAreas = e.target.checked
                      ? [...scope.areas, area]
                      : scope.areas.filter(a => a !== area);
                    onChange({ ...scope, areas: newAreas });
                  }}
                  className="sr-only"
                />
                <Target className={`w-5 h-5 mr-3 ${
                  scope.areas.includes(area) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{area}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Special Requirements */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Special Requirements
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {requirements.map((req) => (
              <label
                key={req}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.specialRequirements.includes(req)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.specialRequirements.includes(req)}
                  onChange={(e) => {
                    const newReqs = e.target.checked
                      ? [...scope.specialRequirements, req]
                      : scope.specialRequirements.filter(r => r !== req);
                    onChange({ ...scope, specialRequirements: newReqs });
                  }}
                  className="sr-only"
                />
                <Shield className={`w-5 h-5 mr-3 ${
                  scope.specialRequirements.includes(req) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{req}</span>
              </label>
            ))}
          </div>
        </div>

        {scope.specialRequirements.includes('Safety Compliance') && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Safety Compliance Required</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Our team will ensure all cleaning operations comply with required safety standards and regulations.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
