import React from 'react';
import { motion } from 'framer-motion';
import { 
  Warehouse, Factory, Package, Truck, 
  Building2, ShoppingBag 
} from 'lucide-react';

const industries = [
  {
    icon: Warehouse,
    title: 'Distribution Centers',
    description: 'Comprehensive cleaning for large distribution facilities',
    features: ['Loading dock areas', 'Storage spaces', 'High traffic zones']
  },
  {
    icon: Factory,
    title: 'Manufacturing Plants',
    description: 'Specialized cleaning for production facilities',
    features: ['Equipment cleaning', 'Safety compliance', 'Waste management']
  },
  {
    icon: Package,
    title: 'Storage Facilities',
    description: 'Cleaning solutions for storage spaces',
    features: ['Climate control', 'Dust prevention', 'Regular maintenance']
  },
  {
    icon: Truck,
    title: 'Logistics Centers',
    description: 'Cleaning services for logistics hubs',
    features: ['Loading areas', 'Vehicle bays', 'Office spaces']
  },
  {
    icon: Building2,
    title: 'Processing Plants',
    description: 'Specialized cleaning for processing facilities',
    features: ['Clean room standards', 'Equipment sanitization', 'Safety protocols']
  },
  {
    icon: ShoppingBag,
    title: 'Fulfillment Centers',
    description: 'Comprehensive cleaning for e-commerce facilities',
    features: ['High-volume areas', 'Package stations', 'Storage zones']
  }
];

export function Industries() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-gray-900 mb-4"
          >
            Industries We Serve
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Specialized cleaning solutions for every industrial environment
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {industries.map((industry, index) => {
            const Icon = industry.icon;
            
            return (
              <motion.div
                key={industry.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="group relative"
              >
                <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-6 
                                group-hover:scale-110 transition-transform">
                    <Icon className="w-6 h-6 text-brand-600" />
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {industry.title}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {industry.description}
                  </p>

                  <div className="space-y-2">
                    {industry.features.map((feature, i) => (
                      <div key={i} className="flex items-center text-gray-600">
                        <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
