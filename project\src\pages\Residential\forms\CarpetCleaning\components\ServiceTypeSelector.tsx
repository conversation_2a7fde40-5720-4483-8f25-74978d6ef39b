import React from 'react';
import { Brush, Sparkles, Droplets, Truck, Home } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'carpet',
    icon: Brush,
    label: 'Standard Carpet Cleaning',
    description: 'Deep cleaning for regular maintenance',
    features: ['Hot water extraction', 'Spot treatment', 'Deodorizing'],
    price: 'Starting at $89'
  },
  {
    id: 'carpet',
    icon: Sparkles,
    label: 'Deep Carpet Cleaning',
    description: 'Intensive cleaning for heavily soiled carpets',
    features: ['Pre-treatment', 'Deep extraction', 'Stain removal'],
    price: 'Starting at $120'
  },
  {
    id: 'carpet',
    icon: Droplets,
    label: 'Pet Treatment',
    description: 'Specialized cleaning for pet stains and odors',
    features: ['Enzyme treatment', 'Odor neutralization', 'Stain removal'],
    price: 'Starting at $110'
  },
  {
    id: 'move',
    icon: Truck,
    label: 'Move-In/Out Cleaning',
    description: 'Complete carpet restoration for moving',
    features: ['Full carpet cleaning', 'Stain treatment', 'Deodorizing'],
    price: '$0.70/sq ft or $130/room'
  },
  {
    id: 'area',
    icon: Home,
    label: 'Area Rug Cleaning',
    description: 'Specialized cleaning for area rugs',
    features: ['Hand cleaning', 'Fringe cleaning', 'Gentle treatment'],
    price: '$2.00/sq ft or $90/rug'
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-6">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-2"
      >
        <h3 className="text-2xl font-semibold text-gray-900">
          Select Your Carpet Cleaning Service
        </h3>
        <p className="text-gray-600 mt-2">
          Choose the type of carpet cleaning service you need
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`group relative w-full p-5 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex flex-col space-y-3">
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg transition-colors ${
                    isSelected ? 'bg-brand-100' : 'bg-gray-50 group-hover:bg-brand-50'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      isSelected ? 'text-brand-600' : 'text-gray-600'
                    }`} />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{service.label}</h3>
                    <p className="text-sm text-gray-600">{service.description}</p>
                    <p className="text-sm font-medium text-brand-600 mt-1">{service.price}</p>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2 pl-14">
                  {service.features.map((feature, i) => (
                    <motion.div 
                      key={i}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 + (i * 0.1) }}
                      className="flex items-center text-gray-600"
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                      <span className="text-sm">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
