import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  <PERSON><PERSON><PERSON>, Shield, Clock, CheckCircle, Star, 
  Droplets, Wind, Zap, Home, Building2, Brush,
  Award, Heart, Gift, ThumbsUp, Flame, Truck,
  Users, Timer, Vacuum, LayoutDashboard
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';

interface ServiceSpecificPopupProps {
  serviceType: string;
  isVisible: boolean;
}

export function ServiceSpecificPopup({ serviceType, isVisible }: ServiceSpecificPopupProps) {
  const navigate = useNavigate();

  if (!isVisible) return null;

  // Debug logging
  console.log('ServiceSpecificPopup - serviceType:', serviceType, 'isVisible:', isVisible);
  console.log('ServiceSpecificPopup - normalized serviceType:', serviceType?.toLowerCase().trim());

  const handleDashboardClick = () => {
    navigate('/accountdashboard');
  };

  const getServiceDisplayName = (type: string) => {
    const serviceName = type.replace(/[-_]/g, ' ');
    return serviceName.replace(/\b\w/g, char => char.toUpperCase());
  };

  const getServiceContent = () => {
    const normalizedServiceType = serviceType?.toLowerCase().trim();
    const displayName = getServiceDisplayName(normalizedServiceType);
    
    switch (normalizedServiceType) {
      case 'deep-cleaning':
      case 'deep':
      case 'deepcleaning':
        return {
          title: 'Deep Cleaning Confirmed! ✨',
          subtitle: `Your ${displayName} booking is confirmed!`,
          icon: <Sparkles className="w-12 h-12 text-brand-600" />,
          bgGradient: 'from-brand-100 to-accent-100',
          borderColor: 'border-brand-300',
          features: [
            {
              icon: <Sparkles className="w-6 h-6 text-brand-600" />,
              title: 'Deep Cleaning Service',
              description: 'Comprehensive deep cleaning assigned'
            },
            {
              icon: <Shield className="w-6 h-6 text-green-600" />,
              title: 'Fully Insured',
              description: 'Complete protection and peace of mind'
            },
            {
              icon: <Award className="w-6 h-6 text-yellow-600" />,
              title: 'Quality Guaranteed',
              description: '100% satisfaction guarantee'
            }
          ],
          specialMessage: 'Our professional team will provide exceptional deep cleaning service tailored to your needs.',
          tips: [
            'Clear access to cleaning areas',
            'Secure valuable items',
            'Our team brings all supplies',
            'Service completion confirmation provided'
          ]
        };

      case 'office':
      case 'commercial':
        return {
          title: 'Commercial Cleaning Confirmed! 🏢',
          subtitle: `Your ${displayName} booking is confirmed!`,
          icon: <Building2 className="w-12 h-12 text-blue-600" />,
          bgGradient: 'from-blue-100 to-cyan-100',
          borderColor: 'border-blue-300',
          features: [
            {
              icon: <Shield className="w-6 h-6 text-blue-600" />,
              title: 'Professional Standards',
              description: 'Commercial-grade cleaning protocols'
            },
            {
              icon: <Clock className="w-6 h-6 text-green-600" />,
              title: 'Flexible Scheduling',
              description: 'Work around your business hours'
            },
            {
              icon: <Award className="w-6 h-6 text-yellow-600" />,
              title: 'Certified Team',
              description: 'Trained commercial cleaning specialists'
            }
          ],
          specialMessage: 'Your workspace will be professionally cleaned to maintain a healthy, productive environment for your team.',
          tips: [
            'We can work after hours or weekends',
            'All equipment and supplies included',
            'Customized cleaning checklist available',
            'Regular service discounts available'
          ]
        };

      case 'carpet':
      case 'carpet-cleaning':
      case 'carpetcleaning':
        return {
          title: 'Carpet Cleaning Confirmed! 🧽',
          subtitle: `Your ${displayName} booking is confirmed!`,
          icon: <Brush className="w-12 h-12 text-emerald-600" />,
          bgGradient: 'from-emerald-50 to-green-100',
          borderColor: 'border-emerald-300',
          features: [
            {
              icon: <Truck className="w-6 h-6 text-blue-600" />,
              title: 'Truck-Mounted Equipment',
              description: 'Powerful extraction removes deep-set dirt and allergens'
            },
            {
              icon: <Droplets className="w-6 h-6 text-cyan-600" />,
              title: 'Advanced Stain Treatment',
              description: 'Specialized solutions for pet stains, wine, coffee & more'
            },
            {
              icon: <Wind className="w-6 h-6 text-purple-600" />,
              title: 'Quick-Dry Technology',
              description: 'Carpets dry in 2-4 hours, not days'
            },
            {
              icon: <Shield className="w-6 h-6 text-green-600" />,
              title: 'Safe & Eco-Friendly',
              description: 'Child and pet-safe cleaning solutions'
            }
          ],
          specialMessage: 'Get ready for carpets that look, feel, and smell like new! Our professional-grade equipment and techniques will restore your carpets to their original beauty while extending their lifespan.',
          tips: [
            'Vacuum thoroughly before our arrival',
            'Move lightweight furniture and breakables',
            'Point out any specific stains or problem areas',
            'Plan for 2-4 hours drying time',
            'Keep pets and children off carpets while drying',
            'We\'ll provide protective booties for walking on damp areas'
          ]
        };

      case 'chimney-cleaning':
      case 'chimney':
        return {
          title: 'Chimney Cleaning Confirmed! 🔥',
          subtitle: `Your ${displayName} booking is confirmed!`,
          icon: <Sparkles className="w-12 h-12 text-red-600" />,
          bgGradient: 'from-red-100 to-orange-100',
          borderColor: 'border-red-300',
          features: [
            {
              icon: <Shield className="w-6 h-6 text-red-600" />,
              title: 'Safety Inspection',
              description: 'CSIA certified safety inspection included'
            },
            {
              icon: <Sparkles className="w-6 h-6 text-orange-600" />,
              title: 'Professional Cleaning',
              description: 'Complete chimney cleaning and debris removal'
            },
            {
              icon: <Wind className="w-6 h-6 text-blue-600" />,
              title: 'Carbon Monoxide Testing',
              description: 'Detection of dangerous gas leaks'
            }
          ],
          specialMessage: 'Your chimney will be professionally cleaned and inspected for maximum safety, protecting your family from fire hazards and carbon monoxide.',
          tips: [
            'Clear access to fireplace area',
            'Remove decorative items from mantle',
            'Our team brings all safety equipment',
            'Safety report provided after service'
          ]
        };

      default:
        return {
          title: 'Service Confirmed! ✅',
          subtitle: `Your ${displayName} booking is confirmed!`,
          icon: <CheckCircle className="w-12 h-12 text-brand-600" />,
          bgGradient: 'from-brand-100 to-accent-100',
          borderColor: 'border-brand-300',
          features: [
            {
              icon: <Star className="w-6 h-6 text-brand-600" />,
              title: 'Professional Service',
              description: 'Expert cleaning team assigned'
            },
            {
              icon: <Shield className="w-6 h-6 text-green-600" />,
              title: 'Fully Insured',
              description: 'Complete protection and peace of mind'
            },
            {
              icon: <Award className="w-6 h-6 text-yellow-600" />,
              title: 'Quality Guaranteed',
              description: '100% satisfaction guarantee'
            }
          ],
          specialMessage: 'Our professional team will provide exceptional cleaning service tailored to your needs.',
          tips: [
            'Clear access to cleaning areas',
            'Secure valuable items',
            'Our team brings all supplies',
            'Service completion confirmation provided'
          ]
        };
    }
  };

  const content = getServiceContent();

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: 10 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={`bg-gradient-to-br ${content.bgGradient} border-2 ${content.borderColor} rounded-3xl shadow-2xl p-8 mb-8`}
    >
      {/* Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-white shadow-lg mb-4">
          {content.icon}
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          {content.title}
        </h2>
        <p className="text-lg text-gray-700">
          {content.subtitle}
        </p>
      </div>

      {/* Special Message */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 mb-8">
        <div className="flex items-start gap-3">
          <Heart className="w-6 h-6 text-red-500 flex-shrink-0 mt-1" />
          <p className="text-gray-800 leading-relaxed">
            {content.specialMessage}
          </p>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        {content.features.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.15, delay: index * 0.05 }}
            className="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-sm"
          >
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                {feature.icon}
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-1">
                  {feature.title}
                </h4>
                <p className="text-sm text-gray-600">
                  {feature.description}
                </p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Preparation Tips */}
      <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6">
        <div className="flex items-center gap-2 mb-4">
          <Gift className="w-6 h-6 text-orange-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Preparation Tips for Best Results
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {content.tips.map((tip, index) => (
            <div key={index} className="flex items-start gap-2">
              <ThumbsUp className="w-4 h-4 text-green-600 flex-shrink-0 mt-1" />
              <span className="text-sm text-gray-700">{tip}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Dashboard Button */}
      <div className="text-center mt-8">
        <Button
          size="lg"
          onClick={handleDashboardClick}
          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:shadow-lg transition-shadow"
        >
          <LayoutDashboard className="w-5 h-5 mr-2" />
          View Your Dashboard
        </Button>
      </div>
    </motion.div>
  );
} 
