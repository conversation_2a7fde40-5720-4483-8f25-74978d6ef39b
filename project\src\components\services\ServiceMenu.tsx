import React from 'react';
import { 
  Home, Sparkles, Package, Droplets, 
  Construction, Shield, Wind, Sofa,
  CheckCircle, Star, Clock, ArrowRight
} from 'lucide-react';
import { Button } from '../ui/Button';

interface Service {
  id: string;
  title: string;
  description: string;
  price: string;
  icon: React.ComponentType<any>;
  features: string[];
  popular?: boolean;
}

const services: Service[] = [
  {
    id: 'regular',
    title: 'Regular Cleaning',
    description: 'Keep your home consistently clean with weekly or monthly service',
    price: '$80',
    icon: Home,
    features: ['All rooms cleaned', 'Kitchen & bathroom deep clean', 'Flexible scheduling'],
    popular: true
  },
  {
    id: 'deep',
    title: 'Deep Cleaning',
    description: 'Comprehensive cleaning that reaches every corner of your home',
    price: '$150',
    icon: Sparkles,
    features: ['Inside appliances', 'Baseboards & fixtures', 'Cabinet interiors'],
    popular: true
  },
  {
    id: 'carpet',
    title: 'Carpet Cleaning',
    description: 'Professional carpet care to remove stains and refresh floors',
    price: '$120',
    icon: Package,
    features: ['Steam cleaning', 'Stain removal', 'Quick dry process']
  },
  {
    id: 'window',
    title: 'Window Cleaning',
    description: 'Crystal clear windows inside and out',
    price: '$90',
    icon: Droplets,
    features: ['Interior & exterior', 'Screen cleaning', 'Streak-free finish']
  },
  {
    id: 'construction',
    title: 'Post-Construction',
    description: 'Specialized cleaning after renovation work',
    price: '$200',
    icon: Construction,
    features: ['Dust removal', 'Debris cleanup', 'Final inspection']
  },
  {
    id: 'sanitization',
    title: 'Sanitization',
    description: 'Health-focused cleaning with EPA-approved disinfectants',
    price: '$100',
    icon: Shield,
    features: ['EPA disinfectants', 'High-touch surfaces', 'Health certified']
  }
];

export function ServiceMenu() {
  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* Header */}
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Cleaning Services</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Professional cleaning solutions designed to make your life easier and your home healthier.
        </p>
        
        {/* Trust Badges */}
        <div className="flex flex-wrap items-center justify-center gap-6 mt-8">
          <div className="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-full">
            <Shield className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-green-800">Licensed & Insured</span>
          </div>
          <div className="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-full">
            <Clock className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">Same-Day Available</span>
          </div>
          <div className="flex items-center space-x-2 bg-yellow-50 px-4 py-2 rounded-full">
            <Star className="w-4 h-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">4.9/5 Rating</span>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        {services.map((service) => {
          const Icon = service.icon;
          return (
            <div
              key={service.id}
              className="bg-white rounded-2xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 relative"
            >
              {service.popular && (
                <div className="absolute -top-3 -right-3 bg-blue-500 text-white text-xs font-bold px-3 py-1 rounded-full">
                  Popular
                </div>
              )}
              
              <div className="flex items-center space-x-4 mb-4">
                <div className="bg-brand-100 p-3 rounded-xl">
                  <Icon className="w-6 h-6 text-brand-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{service.title}</h3>
                  <div className="text-xl font-bold text-brand-600">
                    {service.price}
                    <span className="text-sm font-normal text-gray-500 ml-1">starting</span>
                  </div>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4">{service.description}</p>
              
              <div className="space-y-2 mb-6">
                {service.features.map((feature) => (
                  <div key={feature} className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="text-sm text-gray-600">{feature}</span>
                  </div>
                ))}
              </div>
              
              <Button 
                className="w-full"
                onClick={() => window.location.href = `/residential/${service.id}`}
              >
                Book Now
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          );
        })}
      </div>

      {/* Bottom CTA */}
      <div className="bg-brand-50 rounded-2xl p-8 text-center">
        <h3 className="text-2xl font-bold text-gray-900 mb-3">Need a Custom Solution?</h3>
        <p className="text-gray-600 mb-6 max-w-xl mx-auto">
          Don't see exactly what you need? We can create a custom cleaning plan just for you.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Button 
            size="lg"
            onClick={() => window.location.href = '/contact'}
          >
            Get Custom Quote
          </Button>
          <Button 
            size="lg"
            variant="outline"
            onClick={() => window.location.href = 'tel:+1234567890'}
          >
            Call Us Now
          </Button>
        </div>
      </div>
    </div>
  );
}
