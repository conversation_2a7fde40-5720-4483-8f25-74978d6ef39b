import React from 'react';
import { GlassWater, Maximize2, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface WindowDetailsProps {
  details: {
    windowCount: number;
    cleaningType: string;
    specialtyWindows: string[];
    screenCleaning: boolean;
    trackCleaning: boolean;
    sillCleaning: boolean;
  };
  onChange: (details: any) => void;
}

export function WindowDetails({ details, onChange }: WindowDetailsProps) {
  const cleaningTypes = [
    { value: 'interior', label: 'Interior Only' },
    { value: 'exterior', label: 'Exterior Only' },
    { value: 'both', label: 'Interior & Exterior' }
  ];

  const specialtyWindowTypes = [
    'Skylights',
    'French Panes',
    'Bay Windows',
    'Garden Windows',
    'Arched Windows',
    'Transom Windows',
    'Storm Windows',
    'Sliding Glass Doors'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <GlassWater className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Window Details</h3>
          <p className="text-gray-600">Tell us about your windows</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              How many windows need cleaning? <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              value={details.windowCount || ''}
              onChange={(e) => onChange({ ...details, windowCount: Number(e.target.value) })}
              min="1"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What type of cleaning do you need? <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-3 gap-3">
              {cleaningTypes.map((type) => (
                <label
                  key={type.value}
                  className={`flex flex-col items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                    details.cleaningType === type.value
                      ? 'border-brand-500 bg-brand-50'
                      : 'border-gray-200 hover:border-brand-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="cleaningType"
                    value={type.value}
                    checked={details.cleaningType === type.value}
                    onChange={(e) => onChange({ ...details, cleaningType: e.target.value })}
                    className="sr-only"
                  />
                  <GlassWater className={`w-5 h-5 mb-1 ${
                    details.cleaningType === type.value ? 'text-brand-600' : 'text-gray-400'
                  }`} />
                  <span className="text-sm text-center">{type.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Do you have any hard-to-reach or specialty windows?
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {specialtyWindowTypes.map((type) => (
              <label
                key={type}
                className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  details.specialtyWindows.includes(type)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.specialtyWindows.includes(type)}
                  onChange={(e) => {
                    const newTypes = e.target.checked
                      ? [...details.specialtyWindows, type]
                      : details.specialtyWindows.filter(t => t !== type);
                    onChange({ ...details, specialtyWindows: newTypes });
                  }}
                  className="sr-only"
                />
                <Maximize2 className={`w-5 h-5 mr-2 ${
                  details.specialtyWindows.includes(type) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm">{type}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Additional Services
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <label
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                details.screenCleaning
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="checkbox"
                checked={details.screenCleaning}
                onChange={(e) => onChange({ ...details, screenCleaning: e.target.checked })}
                className="sr-only"
              />
              <GlassWater className={`w-5 h-5 mr-3 ${
                details.screenCleaning ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-gray-700">Screen Cleaning</span>
            </label>

            <label
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                details.trackCleaning
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="checkbox"
                checked={details.trackCleaning}
                onChange={(e) => onChange({ ...details, trackCleaning: e.target.checked })}
                className="sr-only"
              />
              <GlassWater className={`w-5 h-5 mr-3 ${
                details.trackCleaning ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-gray-700">Track Cleaning</span>
            </label>

            <label
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                details.sillCleaning
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="checkbox"
                checked={details.sillCleaning}
                onChange={(e) => onChange({ ...details, sillCleaning: e.target.checked })}
                className="sr-only"
              />
              <GlassWater className={`w-5 h-5 mr-3 ${
                details.sillCleaning ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-gray-700">Sill Cleaning</span>
            </label>
          </div>
        </div>

        {details.specialtyWindows.includes('Skylights') && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Skylight Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Skylights may require special equipment and safety measures. Our team will assess accessibility during service.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
