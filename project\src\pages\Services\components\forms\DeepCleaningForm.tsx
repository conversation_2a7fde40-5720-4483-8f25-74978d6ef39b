import React from 'react';
import { Form, FormField } from './FormFields';

interface DeepCleaningFormProps {
  onSubmit: (data: any) => void;
}

export function DeepCleaningForm({ onSubmit }: DeepCleaningFormProps) {
  return (
    <Form onSubmit={onSubmit}>
      <FormField label="Approximate Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Frequency" required>
        <select
          name="frequency"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="one-time">One-time Service</option>
          <option value="monthly">Monthly</option>
          <option value="quarterly">Quarterly</option>
          <option value="semi-annual">Semi-Annual</option>
          <option value="annual">Annual</option>
        </select>
      </FormField>

      <FormField label="Areas of Focus">
        <div className="space-y-2">
          {[
            'Kitchen/Break Room',
            'Bathrooms',
            'High Dusting',
            'Baseboards',
            'Light Fixtures',
            'HVAC Vents',
            'Window Tracks'
          ].map((area) => (
            <label key={area} className="flex items-center">
              <input
                type="checkbox"
                name="focusAreas"
                value={area.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{area}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Special Requirements">
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="ecoFriendly"
              className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
            />
            <span className="ml-2 text-gray-700">Eco-friendly Products Only</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="allergySensitive"
              className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
            />
            <span className="ml-2 text-gray-700">Allergy-Sensitive Cleaning</span>
          </label>
        </div>
      </FormField>

      <FormField label="Additional Notes">
        <textarea
          name="notes"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific areas of concern or special instructions?"
        />
      </FormField>
    </Form>
  );
}
