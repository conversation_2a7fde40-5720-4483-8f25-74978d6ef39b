import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const photos = [
  {
    url: 'https://images.unsplash.com/photo-1604328698692-f76ea9498e76?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80',
    alt: 'Industrial warehouse cleaning',
    caption: 'Industrial & Warehouses',
    description: 'Specialized cleaning for manufacturing and storage facilities',
    link: '/industrial'
  },
  {
    url: 'https://images.unsplash.com/photo-1631217868264-e5b90bb7e133?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80',
    alt: 'Medical facility cleaning',
    caption: 'Healthcare Facilities',
    description: 'Hospital-grade sanitization and sterilization',
    link: '/service-form/medical'
  },
  {
    url: 'https://images.unsplash.com/photo-**********-701939374585?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80',
    alt: 'Educational facility cleaning',
    caption: 'Educational Institutions',
    description: 'Safe and thorough cleaning for schools and universities',
    link: '/service-form/education'
  },
  {
    url: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80',
    alt: 'Commercial office cleaning',
    caption: 'Corporate Offices',
    description: 'Premium cleaning for professional environments',
    link: '/service-form/office'
  }
];

export function PhotoGallery() {
  const navigate = useNavigate();

  return (
    <section className="py-16 sm:py-24 lg:py-32 bg-gradient-to-b from-white to-brand-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16 lg:mb-20">
          <span className="text-brand-600 font-semibold uppercase tracking-wider">Our Expertise</span>
          <h2 className="mt-3 text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900">
            Industry-Specific Solutions
          </h2>
          <p className="mt-4 sm:mt-6 text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            Delivering specialized cleaning services across diverse environments
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 lg:gap-10">
          {photos.map((photo, index) => (
            <motion.div
              key={photo.caption}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
              className="group relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer"
              onClick={() => navigate(photo.link)}
            >
              <div className="aspect-w-16 aspect-h-9 sm:aspect-h-10 md:aspect-h-9">
                <img
                  src={photo.url}
                  alt={photo.alt}
                  className="object-cover w-full h-full transform group-hover:scale-105 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
                <div className="absolute inset-0 p-4 sm:p-6 lg:p-8 flex flex-col justify-end text-white">
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold mb-2 sm:mb-3">{photo.caption}</h3>
                  <p className="text-brand-50 text-sm sm:text-base lg:text-lg">{photo.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
