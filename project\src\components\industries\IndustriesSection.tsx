import React from 'react';
import { IndustryCard } from './IndustryCard';
import { industriesData } from './data/industries';

export function IndustriesSection() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-14 lg:mb-16">
          <span className="text-brand-600 font-semibold">Industries</span>
          <h2 className="mt-2 text-2xl sm:text-3xl font-bold text-gray-900">
            Specialized Cleaning Solutions
          </h2>
          <p className="mt-4 text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto">
            Tailored cleaning services for every industry's unique requirements
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {industriesData.map((industry) => (
            <IndustryCard key={industry.id} industry={industry} />
          ))}
        </div>
      </div>
    </section>
  );
}
