import React from 'react';
import { Droplets, AlertCircle, Clock } from 'lucide-react';
import { motion } from 'framer-motion';

interface PoolDetailsProps {
  details: {
    poolSize: string;
    poolType: string;
    poolShape: string;
    depth: string;
    issues: string[];
    lastServiced: string;
  };
  onChange: (details: any) => void;
}

export function PoolDetails({ details, onChange }: PoolDetailsProps) {
  const poolSizes = [
    'Small (up to 10,000 gallons)',
    'Medium (10,000-20,000 gallons)',
    'Large (20,000-30,000 gallons)',
    'Extra Large (30,000+ gallons)',
    'Not Sure'
  ];

  const poolTypes = [
    'In-ground Concrete',
    'In-ground Vinyl',
    'In-ground Fiberglass',
    'Above-ground',
    'Saltwater',
    'Chlorine',
    'Infinity Pool',
    'Lap Pool'
  ];

  const poolShapes = [
    'Rectangle',
    'Oval',
    'Kidney',
    'Free-form',
    'L-shape',
    'Geometric',
    'Round',
    'Other'
  ];

  const poolIssues = [
    'Algae',
    'Cloudy Water',
    'Debris',
    'Stains',
    'Scaling',
    'Chemical Imbalance',
    'Equipment Issues',
    'Green Water'
  ];

  const lastServicedOptions = [
    'Within last week',
    'Within last month',
    'Within last 3 months',
    '3-6 months ago',
    '6+ months ago',
    'Never serviced',
    'Unknown'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Droplets className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Pool Details</h3>
          <p className="text-gray-600">Tell us about your pool</p>
        </div>
      </motion.div>

      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-6">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-blue-600 mr-2 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-800">Subscription Service</h4>
            <p className="mt-1 text-sm text-blue-700">
              Pool cleaning is available as a subscription service only. We offer weekly, bi-weekly, and monthly maintenance plans.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What size is your pool? <span className="text-red-500">*</span>
            </label>
            <select
              value={details.poolSize}
              onChange={(e) => onChange({ ...details, poolSize: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select pool size</option>
              {poolSizes.map((size) => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What type of pool is it? <span className="text-red-500">*</span>
            </label>
            <select
              value={details.poolType}
              onChange={(e) => onChange({ ...details, poolType: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select pool type</option>
              {poolTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What shape is your pool?
            </label>
            <select
              value={details.poolShape}
              onChange={(e) => onChange({ ...details, poolShape: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            >
              <option value="">Select pool shape</option>
              {poolShapes.map((shape) => (
                <option key={shape} value={shape}>{shape}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What is the average depth?
            </label>
            <input
              type="text"
              value={details.depth}
              onChange={(e) => onChange({ ...details, depth: e.target.value })}
              placeholder="e.g., 3-8 feet, 5 feet, etc."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Are there specific issues? <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {poolIssues.map((issue) => (
              <label
                key={issue}
                className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  details.issues.includes(issue)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.issues.includes(issue)}
                  onChange={(e) => {
                    const newIssues = e.target.checked
                      ? [...details.issues, issue]
                      : details.issues.filter(i => i !== issue);
                    onChange({ ...details, issues: newIssues });
                  }}
                  className="sr-only"
                />
                <AlertCircle className={`w-5 h-5 mr-2 ${
                  details.issues.includes(issue) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm">{issue}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            When was the pool last serviced? <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={details.lastServiced}
              onChange={(e) => onChange({ ...details, lastServiced: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select option</option>
              {lastServicedOptions.map((option) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        </div>

        {details.issues.includes('Algae') && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Algae Treatment</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Algae treatment may require multiple visits and specialized chemicals. Our technician will assess the severity during the first visit.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
