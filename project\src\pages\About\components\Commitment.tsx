import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle } from 'lucide-react';

const commitments = [
  'Customized cleaning plans',
  'Monthly service reports',
  'Real-time monitoring',
  'Quality assurance checks',
  'Eco-friendly products',
  'Trained professionals'
];

export function Commitment() {
  return (
    <section className="py-24 bg-brand-600">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-white mb-6">Our Commitment</h2>
          <p className="text-xl text-brand-100 max-w-2xl mx-auto">
            Delivering exceptional service through our comprehensive approach
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {commitments.map((commitment, index) => (
            <motion.div
              key={commitment}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center space-x-4 bg-white/10 backdrop-blur-sm p-6 rounded-xl"
            >
              <CheckCircle className="w-6 h-6 text-brand-200 flex-shrink-0" />
              <span className="text-white font-medium">{commitment}</span>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
