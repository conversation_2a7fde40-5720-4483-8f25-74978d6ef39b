import React from 'react';
import type { LucideIcon } from 'lucide-react';

interface ServiceCardProps {
  service: {
    icon: LucideIcon;
    name: string;
    description: string;
  };
}

export function ServiceCard({ service }: ServiceCardProps) {
  const Icon = service.icon;
  
  return (
    <a
      href="#"
      className="group flex flex-col items-center p-4 rounded-lg hover:bg-white hover:shadow-md transition-all"
    >
      <div className="rounded-full bg-brand-50 p-3 group-hover:bg-brand-100 transition-colors">
        <Icon className="w-6 h-6 text-brand-600" />
      </div>
      <h3 className="mt-4 font-medium text-gray-900">{service.name}</h3>
      <p className="mt-1 text-sm text-gray-500">{service.description}</p>
    </a>
  );
}
