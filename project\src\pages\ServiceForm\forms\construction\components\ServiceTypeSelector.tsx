import React from 'react';
import { Building2, Construction, Hammer, Brush, Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'new-construction',
    icon: Building2,
    label: 'New Construction',
    description: 'Final cleaning for newly constructed buildings',
    features: ['Debris removal', 'Surface cleaning', 'Window cleaning', 'Floor finishing']
  },
  {
    id: 'renovation',
    icon: Construction,
    label: 'Renovation',
    description: 'Post-renovation cleanup and debris removal',
    features: ['Dust removal', 'Paint cleanup', 'Floor protection', 'HVAC cleaning']
  },
  {
    id: 'remodel',
    icon: Hammer,
    label: 'Remodeling',
    description: 'Cleanup after remodeling projects',
    features: ['Material removal', 'Surface prep', 'Detail cleaning', 'Final inspection']
  },
  {
    id: 'addition',
    icon: Brush,
    label: 'Addition',
    description: 'Cleaning for new building additions',
    features: ['Integration cleaning', 'Dust control', 'Surface finishing', 'Area prep']
  },
  {
    id: 'restoration',
    icon: Sparkles,
    label: 'Restoration',
    description: 'Post-restoration cleaning services',
    features: ['Damage cleanup', 'Surface restoration', 'Odor removal', 'Sanitization']
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="text-2xl font-semibold text-gray-900">
          Select Project Type
        </h3>
        <p className="text-gray-600 mt-2">
          Choose your construction project type for specialized cleaning
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`group relative w-full p-6 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex flex-col space-y-4">
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg transition-colors ${
                    isSelected ? 'bg-brand-100' : 'bg-gray-50 group-hover:bg-brand-50'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      isSelected ? 'text-brand-600' : 'text-gray-600'
                    }`} />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{service.label}</h3>
                    <p className="text-sm text-gray-600">{service.description}</p>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2 pl-14">
                  {service.features.map((feature, i) => (
                    <motion.div 
                      key={i}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 + (i * 0.1) }}
                      className="flex items-center text-gray-600"
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                      <span className="text-sm">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
