import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PaymentSuccessPopup } from '../components/PaymentSuccessPopup';
import { PaymentNotificationProvider } from '../hooks/usePaymentNotification';

// Mock the usePaymentNotification hook
const mockHideSuccessPopup = vi.fn();
const mockOnPaymentSuccess = vi.fn();

vi.mock('../hooks/usePaymentNotification', () => ({
  PaymentNotificationProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  usePaymentNotification: () => ({
    showSuccessPopup: true,
    hideSuccessPopup: mockHideSuccessPopup,
    onPaymentSuccess: mockOnPaymentSuccess,
  }),
}));

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h2: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('PaymentSuccessPopup', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.location.pathname
    Object.defineProperty(window, 'location', {
      value: { pathname: '/some-page' },
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders the popup with correct content', () => {
    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    expect(screen.getByText('Booking Confirmed!')).toBeInTheDocument();
    expect(screen.getByText(/Your booking has been confirmed!/)).toBeInTheDocument();
    expect(screen.getByText(/We'll send you a confirmation email shortly and will contact you to schedule your service/)).toBeInTheDocument();
    expect(screen.getByText('Thank you for choosing our service!')).toBeInTheDocument();
  });

  it('has a close button that works', () => {
    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    const closeButton = screen.getByLabelText('Close popup');
    expect(closeButton).toBeInTheDocument();

    fireEvent.click(closeButton);
    expect(mockHideSuccessPopup).toHaveBeenCalledTimes(1);
  });

  it('closes when clicking outside the popup', () => {
    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    // Find the backdrop (the outer div with the backdrop styling)
    const backdrop = screen.getByRole('button', { hidden: true }) || 
                    document.querySelector('[class*="backdrop-blur"]');
    
    if (backdrop) {
      fireEvent.click(backdrop);
      expect(mockHideSuccessPopup).toHaveBeenCalledTimes(1);
    }
  });

  it('closes when pressing ESC key', async () => {
    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    fireEvent.keyDown(document, { key: 'Escape' });
    
    await waitFor(() => {
      expect(mockHideSuccessPopup).toHaveBeenCalledTimes(1);
    });
  });

  it('does not show on ThankYou page', () => {
    // Mock being on the ThankYou page
    Object.defineProperty(window, 'location', {
      value: { pathname: '/thankyou' },
      writable: true,
    });

    const { container } = render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    // The popup should not be visible
    expect(container.firstChild).toBeEmptyDOMElement();
  });

  it('has proper accessibility attributes', () => {
    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    const closeButton = screen.getByLabelText('Close popup');
    expect(closeButton).toHaveAttribute('aria-label', 'Close popup');
  });

  it('auto-hides after 8 seconds', async () => {
    vi.useFakeTimers();
    
    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    // Fast-forward 8 seconds
    vi.advanceTimersByTime(8000);

    await waitFor(() => {
      expect(mockHideSuccessPopup).toHaveBeenCalledTimes(1);
    });

    vi.useRealTimers();
  });

  it('shows development test button in development mode', () => {
    // Mock development environment
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    expect(screen.getByText('Test Popup')).toBeInTheDocument();

    // Restore original environment
    process.env.NODE_ENV = originalEnv;
  });

  it('does not show development test button in production', () => {
    // Mock production environment
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    render(
      <PaymentNotificationProvider>
        <PaymentSuccessPopup />
      </PaymentNotificationProvider>
    );

    expect(screen.queryByText('Test Popup')).not.toBeInTheDocument();

    // Restore original environment
    process.env.NODE_ENV = originalEnv;
  });
});
