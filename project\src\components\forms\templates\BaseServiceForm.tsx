import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, Calendar, Clock, CheckCircle, 
  Shield, Star, ArrowRight, Building2,
  Users, DollarSign, Heart,
  ChevronRight, ChevronLeft, Gift, Zap, Info
} from 'lucide-react';
import { Button } from '../../ui/Button';

interface BaseFormData {
  // Property Info
  propertyType: string;
  propertySize: string;
  
  // Service-specific details (to be extended)
  serviceSpecific: Record<string, any>;
  
  // Scheduling
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  
  // Add-ons
  addOns: string[];
  
  // Contact Info
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  
  // Marketing
  howDidYouHear: string;
  newsletter: boolean;
}

interface ServiceConfig {
  serviceName: string;
  serviceIcon: React.ReactNode;
  serviceDescription: string;
  steps: Array<{
    title: string;
    subtitle: string;
    icon: React.ReactNode;
  }>;
  propertyTypes: Array<{
    id: string;
    name: string;
    icon: React.ReactNode;
    description: string;
    details: string;
    time: string;
  }>;
  serviceOptions: Array<{
    id: string;
    name: string;
    description: string;
    price: string;
    basePrice: number;
    popular?: boolean;
  }>;
  addOnServices: Array<{
    id: string;
    name: string;
    description: string;
    price: number;
    icon: React.ReactNode;
    recommended?: boolean;
  }>;
  calculatePrice: (formData: BaseFormData) => number;
  benefits: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
  }>;
  specialOffer: {
    title: string;
    discount: string;
    description: string;
    urgency: string;
  };
}

interface BaseServiceFormProps {
  config: ServiceConfig;
  customSteps?: React.ReactNode[];
}

const BaseServiceForm: React.FC<BaseServiceFormProps> = ({ config, customSteps }) => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPriceDetails, setShowPriceDetails] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<BaseFormData>({
    propertyType: '',
    propertySize: '',
    serviceSpecific: {},
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    howDidYouHear: '',
    newsletter: false
  });

  // Enhanced time slots with conversion psychology
  const timeSlots = [
    { 
      id: 'morning', 
      name: '8:00 AM - 11:00 AM', 
      label: 'Morning',
      available: true,
      slots: 4,
      urgency: 'Good availability',
      urgencyColor: 'text-green-600'
    },
    { 
      id: 'midday', 
      name: '11:00 AM - 2:00 PM', 
      label: 'Midday',
      available: true, 
      popular: true,
      slots: 1,
      urgency: 'Only 1 slot left!',
      urgencyColor: 'text-orange-600'
    },
    { 
      id: 'afternoon', 
      name: '2:00 PM - 5:00 PM', 
      label: 'Afternoon',
      available: true,
      slots: 2,
      urgency: 'Limited availability',
      urgencyColor: 'text-yellow-600'
    },
    { 
      id: 'evening', 
      name: '5:00 PM - 7:00 PM', 
      label: 'Evening',
      available: false,
      slots: 0,
      urgency: 'Fully booked',
      urgencyColor: 'text-red-600'
    }
  ];

  // Step validation
  const isStepValid = (step: number) => {
    switch (step) {
      case 0:
        return formData.propertyType && formData.propertySize;
      case 1:
        return formData.preferredDate && formData.preferredTime;
      case 2:
        return true; // Add-ons are optional
      case 3:
        return formData.firstName && formData.lastName && formData.email && 
               formData.phone && formData.address && formData.city && formData.zipCode;
      default:
        return false;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!isStepValid(3)) return;
    
    setIsSubmitting(true);
    // Simulate API call
    setTimeout(() => {
      navigate('/thank-you');
    }, 1500);
  };

  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Get calendar dates for current month
  const getCalendarDates = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    const dates = [];
    
    const startPadding = firstDay.getDay();
    for (let i = 0; i < startPadding; i++) {
      dates.push(null);
    }
    
    for (let day = 1; day <= lastDay.getDate(); day++) {
      dates.push(new Date(year, month, day));
    }
    
    return dates;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                {config.serviceIcon}
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Book {config.serviceName}</h1>
                <p className="text-sm text-gray-600">{config.serviceDescription}</p>
              </div>
            </div>
            
            {/* Enhanced Trust badges */}
            <div className="hidden md:flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-brand-600" />
                <span className="text-sm font-medium">Fully Insured & Bonded</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium">4.9★ (2,847 reviews)</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium">100% Satisfaction Guarantee</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {config.steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex items-center">
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: currentStep === index ? 1.1 : 1,
                      backgroundColor: currentStep >= index ? '#638907' : '#e5e7eb'
                    }}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white transition-all`}
                  >
                    {currentStep > index ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className={currentStep >= index ? 'text-white' : 'text-gray-600'}>
                        {index + 1}
                      </span>
                    )}
                  </motion.div>
                  <div className="ml-3 hidden sm:block">
                    <p className={`text-sm font-medium ${currentStep >= index ? 'text-brand-600' : 'text-gray-600'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500">{step.subtitle}</p>
                  </div>
                </div>
                {index < config.steps.length - 1 && (
                  <div className={`hidden md:block w-12 lg:w-24 h-0.5 mx-4 ${
                    currentStep > index ? 'bg-brand-500' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form Steps */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              
              {/* Step 0: Service Details */}
              {currentStep === 0 && !customSteps?.[0] && (
                <motion.div
                  key="step0"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Tell us about your property</h2>
                    <p className="text-gray-600">Help us prepare the right service for you</p>
                  </div>

                  {/* Property Type */}
                  <div className="mb-8">
                    <label className="block text-lg font-semibold text-gray-900 mb-4">Property Type</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {config.propertyTypes.map((type) => (
                        <motion.button
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setFormData({ ...formData, propertyType: type.id })}
                          className={`p-5 rounded-2xl border-2 transition-all text-left ${
                            formData.propertyType === type.id
                              ? 'border-brand-500 bg-brand-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          <div className="flex items-start gap-4">
                            <div className={`p-3 rounded-xl transition-colors ${
                              formData.propertyType === type.id
                                ? 'bg-brand-100 text-brand-600'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {type.icon}
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 text-lg">{type.name}</h3>
                              <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                              <div className="mt-3 space-y-1">
                                <div className="text-xs text-gray-600">{type.details}</div>
                                <div className="text-xs text-brand-600 font-medium">{type.time}</div>
                              </div>
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Service Options */}
                  <div className="mb-8">
                    <label className="block text-lg font-semibold text-gray-900 mb-4">Service Option</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {config.serviceOptions.map((option) => (
                        <motion.button
                          key={option.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setFormData({ ...formData, propertySize: option.id })}
                          className={`relative p-5 rounded-2xl border-2 transition-all text-left ${
                            formData.propertySize === option.id
                              ? 'border-brand-500 bg-brand-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          <div className="text-center">
                            <h3 className="font-semibold text-gray-900 text-lg">{option.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                            <div className="text-lg font-bold text-brand-600 mt-3">{option.price}</div>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Continue Button */}
                  <Button
                    onClick={() => setCurrentStep(1)}
                    disabled={!isStepValid(0)}
                    className="w-full"
                    size="lg"
                  >
                    Continue
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>
                </motion.div>
              )}

              {/* Custom Steps */}
              {customSteps?.[currentStep]}

            </AnimatePresence>
          </div>

          {/* Enhanced Conversion Sidebar */}
          <div className="lg:sticky lg:top-32 h-fit space-y-6">
            {/* Price Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl shadow-soft p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Price Summary</h3>
                <div className="flex items-center gap-1 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                  <Star className="w-3 h-3 fill-current" />
                  <span className="font-medium">Best Value</span>
                </div>
              </div>
              
              {formData.propertySize ? (
                <>
                  <div className="space-y-3 mb-4">
                    <div className="flex items-baseline justify-between">
                      <span className="text-3xl font-bold text-gray-900">${config.calculatePrice(formData)}</span>
                      <div className="text-right">
                        <span className="text-gray-600">/service</span>
                        <p className="text-xs text-green-600 font-medium">Professional service</p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                  <p className="text-gray-500">Complete form to see pricing</p>
                </div>
              )}
            </motion.div>

            {/* Service Benefits */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gradient-to-br from-brand-50 to-accent-50 rounded-2xl p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Heart className="w-5 h-5 text-red-500" />
                Why Choose Professional Service?
              </h3>
              <div className="space-y-3">
                {config.benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-4 h-4 text-green-600 mt-0.5">
                      {benefit.icon}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{benefit.title}</p>
                      <p className="text-sm text-gray-600">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Special Offer */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="bg-gradient-to-br from-orange-50 to-red-50 border border-orange-200 rounded-2xl p-6"
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-3">
                  <Gift className="w-6 h-6 text-orange-600" />
                  <h3 className="text-lg font-bold text-orange-900">{config.specialOffer.title}</h3>
                </div>
                <p className="text-2xl font-bold text-orange-600 mb-2">{config.specialOffer.discount}</p>
                <p className="text-sm text-orange-800 mb-4">{config.specialOffer.description}</p>
                <div className="flex items-center justify-center gap-2 text-xs text-orange-700">
                  <Clock className="w-3 h-3" />
                  <span>{config.specialOffer.urgency}</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BaseServiceForm; 
