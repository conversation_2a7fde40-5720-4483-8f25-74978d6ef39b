import React from 'react';
import { motion } from 'framer-motion';
import { Spark<PERSON>, <PERSON>pray, Brush, ArrowR<PERSON>, Shield, Star } from 'lucide-react';
import { Button } from '../../../components/ui/Button';

export function ServicesHero() {
  const handleExploreClick = () => {
    const servicesSection = document.getElementById('services-grid');
    servicesSection?.scrollIntoView({ behavior: 'smooth' });
  };

  // Floating icons animation variants
  const floatingIconVariants = {
    initial: { opacity: 0, y: 50 },
    animate: (index: number) => ({
      opacity: 0.15,
      y: [0, -20, 0],
      transition: {
        y: {
          repeat: Infinity,
          duration: 3,
          delay: index * 0.5,
          ease: "easeInOut"
        }
      }
    })
  };

  // Trust badges data
  const trustBadges = [
    { icon: Shield, text: 'Licensed & Insured' },
    { icon: Star, text: '4.9/5 Rating' },
    { icon: Sparkles, text: 'Satisfaction Guaranteed' }
  ];

  return (
    <section className="relative min-h-[90vh] flex items-center pt-20">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-900/95 via-brand-800/90 to-brand-700/85" />
        
        {/* Animated Wave Background */}
        <div className="absolute inset-0">
          <div className="absolute bottom-0 left-0 right-0 h-64">
            <div className="absolute inset-0 w-[200%] animate-wave">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-white/5">
                <path d="M0,50 Q25,60 50,50 Q75,40 100,50 L100,100 L0,100 Z" />
              </svg>
            </div>
            <div className="absolute inset-0 w-[200%] animate-wave-slow">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-white/3">
                <path d="M0,60 Q25,50 50,60 Q75,70 100,60 L100,100 L0,100 Z" />
              </svg>
            </div>
          </div>
        </div>
        
        {/* Floating Icons */}
        {[Sparkles, Spray, Brush].map((Icon, index) => (
          <motion.div
            key={index}
            custom={index}
            variants={floatingIconVariants}
            initial="initial"
            animate="animate"
            className="absolute"
            style={{
              left: `${20 + (index * 30)}%`,
              top: `${30 + (index * 20)}%`
            }}
          >
            <Icon className="w-16 h-16 text-white" />
          </motion.div>
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-flex items-center justify-center p-3 bg-white/10 backdrop-blur-sm rounded-xl mb-6"
            >
              <Sparkles className="w-6 h-6 text-white" />
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6 leading-tight"
            >
              Professional Cleaning
              <span className="block text-brand-200">Solutions</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-xl md:text-2xl text-brand-100 mb-8"
            >
              Comprehensive cleaning services tailored to your business needs
            </motion.p>

            {/* Trust Badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              {trustBadges.map((badge, index) => (
                <motion.div
                  key={badge.text}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 + (index * 0.1) }}
                  className="flex items-center bg-white/10 backdrop-blur-sm rounded-full px-4 py-2"
                >
                  <badge.icon className="w-4 h-4 text-brand-200 mr-2" />
                  <span className="text-sm text-white">{badge.text}</span>
                </motion.div>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex flex-col sm:flex-row items-center justify-center gap-4"
            >
              <Button
                size="lg"
                onClick={handleExploreClick}
                className="bg-white text-brand-600 hover:bg-brand-50 w-full sm:w-auto"
              >
                Explore Services
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white/10 w-full sm:w-auto"
              >
                Request a Quote
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Decorative Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-gray-50 to-transparent" />
    </section>
  );
}
