import React from 'react';
import { <PERSON>, Clock, HeartHandshake } from 'lucide-react';

const features = [
  {
    icon: Shield,
    text: '100% Secure Booking'
  },
  {
    icon: Clock,
    text: '24/7 Customer Support'
  },
  {
    icon: HeartHandshake,
    text: 'Satisfaction Guaranteed'
  }
];

export function FormFooter() {
  return (
    <footer className="bg-white/80 backdrop-blur-sm py-6 mt-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {features.map(({ icon: Icon, text }) => (
            <div
              key={text}
              className="flex items-center justify-center space-x-2 text-gray-600"
            >
              <Icon className="w-5 h-5 text-green-600" />
              <span>{text}</span>
            </div>
          ))}
        </div>
      </div>
    </footer>
  );
}
