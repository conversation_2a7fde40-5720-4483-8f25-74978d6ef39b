import React from 'react';
import { Brush, Calendar, Users, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface CarpetDetailsProps {
  details: {
    carpetType: string;
    carpetAge: string;
    carpetColor: string;
    carpetCondition: string;
    trafficLevel: string;
    lastCleaning: string;
  };
  onChange: (details: any) => void;
}

export function CarpetDetails({ details, onChange }: CarpetDetailsProps) {
  const carpetTypes = [
    { value: 'nylon', label: 'Nylon' },
    { value: 'polyester', label: 'Polyester' },
    { value: 'wool', label: 'Wool' },
    { value: 'olefin', label: 'Olefin/Polypropylene' },
    { value: 'triexta', label: 'Triexta' },
    { value: 'blended', label: 'Blended Fibers' },
    { value: 'unknown', label: 'Unknown' }
  ];

  const carpetAges = [
    { value: 'less-than-1', label: 'Less than 1 year' },
    { value: '1-3', label: '1-3 years' },
    { value: '4-6', label: '4-6 years' },
    { value: '7-10', label: '7-10 years' },
    { value: 'more-than-10', label: 'More than 10 years' },
    { value: 'unknown', label: 'Unknown' }
  ];

  const carpetColors = [
    { value: 'light', label: 'Light (White, Beige, Light Gray)' },
    { value: 'medium', label: 'Medium (Tan, Medium Gray, Light Blue)' },
    { value: 'dark', label: 'Dark (Dark Brown, Navy, Black)' },
    { value: 'patterned', label: 'Patterned/Multi-colored' }
  ];

  const carpetConditions = [
    { value: 'excellent', label: 'Excellent - Like new' },
    { value: 'good', label: 'Good - Minor wear' },
    { value: 'fair', label: 'Fair - Visible wear and some stains' },
    { value: 'poor', label: 'Poor - Heavy wear and multiple stains' }
  ];

  const trafficLevels = [
    { value: 'light', label: 'Light - Rarely used areas' },
    { value: 'moderate', label: 'Moderate - Regular use' },
    { value: 'heavy', label: 'Heavy - High traffic areas' },
    { value: 'extreme', label: 'Extreme - Constant traffic' }
  ];

  const lastCleaningOptions = [
    { value: 'less-than-6-months', label: 'Less than 6 months ago' },
    { value: '6-12-months', label: '6-12 months ago' },
    { value: '1-2-years', label: '1-2 years ago' },
    { value: 'more-than-2-years', label: 'More than 2 years ago' },
    { value: 'never', label: 'Never professionally cleaned' },
    { value: 'unknown', label: 'Unknown' }
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Brush className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Carpet Details</h3>
          <p className="text-gray-600">Tell us about your carpet</p>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Carpet Type <span className="text-red-500">*</span>
          </label>
          <select
            value={details.carpetType}
            onChange={(e) => onChange({ ...details, carpetType: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select carpet type</option>
            {carpetTypes.map((type) => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Carpet Age <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={details.carpetAge}
              onChange={(e) => onChange({ ...details, carpetAge: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select carpet age</option>
              {carpetAges.map((age) => (
                <option key={age.value} value={age.value}>{age.label}</option>
              ))}
            </select>
          </div>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Carpet Color
          </label>
          <select
            value={details.carpetColor}
            onChange={(e) => onChange({ ...details, carpetColor: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
            <option value="">Select carpet color</option>
            {carpetColors.map((color) => (
              <option key={color.value} value={color.value}>{color.label}</option>
            ))}
          </select>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Carpet Condition <span className="text-red-500">*</span>
          </label>
          <select
            value={details.carpetCondition}
            onChange={(e) => onChange({ ...details, carpetCondition: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select carpet condition</option>
            {carpetConditions.map((condition) => (
              <option key={condition.value} value={condition.value}>{condition.label}</option>
            ))}
          </select>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Traffic Level <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Users className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={details.trafficLevel}
              onChange={(e) => onChange({ ...details, trafficLevel: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select traffic level</option>
              {trafficLevels.map((level) => (
                <option key={level.value} value={level.value}>{level.label}</option>
              ))}
            </select>
          </div>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Last Professional Cleaning
          </label>
          <select
            value={details.lastCleaning}
            onChange={(e) => onChange({ ...details, lastCleaning: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
            <option value="">Select last cleaning timeframe</option>
            {lastCleaningOptions.map((option) => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </motion.div>
      </div>

      {details.carpetCondition === 'poor' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg"
        >
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Carpet Condition Notice</h4>
              <p className="mt-1 text-sm text-yellow-700">
                For heavily worn or stained carpets, our technicians may need to use specialized treatments. 
                This might affect the final price and service duration. We'll discuss options during our assessment.
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
