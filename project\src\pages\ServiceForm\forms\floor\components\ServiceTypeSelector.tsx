import React from 'react';
import { G<PERSON>, <PERSON>, Spark<PERSON>, Shield, Brush } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'hardwood',
    icon: Brush,
    label: 'Hardwood Restoration',
    description: 'Refinishing and restoration for hardwood floors',
    features: ['Deep cleaning', 'Scratch repair', 'Refinishing']
  },
  {
    id: 'marble',
    icon: Sparkles,
    label: 'Marble & Stone',
    description: 'Polishing and restoration for natural stone',
    features: ['Diamond polishing', 'Sealing', 'Stain removal']
  },
  {
    id: 'vinyl',
    icon: Grid,
    label: 'Vinyl/VCT',
    description: 'Strip, wax, and restoration for vinyl floors',
    features: ['Stripping', 'Waxing', 'Buffing']
  },
  {
    id: 'concrete',
    icon: Hammer,
    label: 'Concrete Polishing',
    description: 'Grinding and polishing for concrete surfaces',
    features: ['Surface prep', 'Polishing', 'Sealing']
  },
  {
    id: 'terrazzo',
    icon: Shield,
    label: 'Terrazzo Restoration',
    description: 'Specialized restoration for terrazzo floors',
    features: ['Grinding', 'Polishing', 'Sealing']
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-4">
          <Grid className="w-6 h-6 text-brand-600" />
        </div>
        <h3 className="text-2xl font-semibold text-gray-900">
          Select Floor Type
        </h3>
        <p className="text-gray-600 mt-2">
          Choose your floor type for specialized restoration services
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`group relative w-full p-6 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex flex-col space-y-4">
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg transition-colors ${
                    isSelected ? 'bg-brand-100' : 'bg-gray-50 group-hover:bg-brand-50'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      isSelected ? 'text-brand-600' : 'text-gray-600'
                    }`} />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 mb-1">{service.label}</h3>
                    <p className="text-sm text-gray-600">{service.description}</p>
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-2 pl-14">
                  {service.features.map((feature, i) => (
                    <motion.div 
                      key={i}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 + (i * 0.1) }}
                      className="flex items-center text-gray-600"
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                      <span className="text-sm">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
