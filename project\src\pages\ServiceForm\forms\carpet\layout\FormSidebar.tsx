import React from 'react';
import { OrderSummary } from '../components/OrderSummary';
import { WhyChooseUs } from '../components/WhyChooseUs';
import { ServiceGuarantee } from '../components/ServiceGuarantee';
import type { FormData } from '../types';

interface FormSidebarProps {
  formData: FormData;
}

export function FormSidebar({ formData }: FormSidebarProps) {
  return (
    <div className="space-y-6 lg:sticky lg:top-8">
      <OrderSummary formData={formData} />
      <WhyChooseUs />
      <ServiceGuarantee />
    </div>
  );
}
