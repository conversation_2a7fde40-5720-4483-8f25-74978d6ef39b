import React from 'react';
import type { LucideIcon } from 'lucide-react';
import { ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

interface ServiceCardProps {
  service: {
    id: string;
    icon: LucideIcon;
    title: string;
    description: string;
  };
  onBook: (serviceId: string) => void;
}

export function ServiceCard({ service, onBook }: ServiceCardProps) {
  const Icon = service.icon;
  const navigate = useNavigate();
  
  const handleInteraction = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`/solutions#${service.id}`);
    onBook(service.id);
  };

  return (
    <motion.div
      initial={false}
      whileHover={{ 
        y: -4,
        transition: {
          type: "spring",
          stiffness: 300,
          damping: 15
        }
      }}
      whileTap={{ 
        scale: 0.98,
        transition: {
          type: "spring",
          stiffness: 500,
          damping: 10
        }
      }}
      className="relative h-full"
    >
      <button
        onClick={handleInteraction}
        onTouchEnd={handleInteraction}
        className="w-full h-full text-left bg-white rounded-xl p-4 sm:p-6 
                 shadow-sm hover:shadow-lg active:shadow-md
                 transition-all duration-300
                 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2
                 touch-manipulation"
        role="button"
        tabIndex={0}
      >
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0 rounded-lg bg-brand-50 p-3 
                         transition-colors group-hover:bg-brand-100">
            <Icon className="w-6 h-6 text-brand-600" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">
              {service.title}
            </h3>
            <p className="text-gray-600 line-clamp-2 mb-2">
              {service.description}
            </p>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-100 flex items-center justify-between">
          <span className="font-medium text-brand-600">
            Learn More
          </span>
          <ArrowRight className="w-5 h-5 text-brand-600" />
        </div>
      </button>
    </motion.div>
  );
}
