import React from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ArrowRight, Home } from 'lucide-react';
import { Button } from '../../ui/Button';

interface FormNavigationProps {
  onBack: () => void;
  onNext: () => void;
  onGoToFirstStep?: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  canGoNext: boolean;
  isSubmitting?: boolean;
  nextButtonText?: string;
  backButtonText?: string;
  showGoToFirstButton?: boolean;
  className?: string;
}

export function FormNavigation({
  onBack,
  onNext,
  onGoToFirstStep,
  isFirstStep,
  isLastStep,
  canGoNext,
  isSubmitting = false,
  nextButtonText,
  backButtonText,
  showGoToFirstButton = false,
  className = ''
}: FormNavigationProps) {
  const getNextButtonText = () => {
    if (nextButtonText) return nextButtonText;
    if (isLastStep) return isSubmitting ? 'Submitting...' : 'Submit Request';
    return 'Continue';
  };

  const getBackButtonText = () => {
    if (backButtonText) return backButtonText;
    if (isFirstStep) return 'Back to Home';
    return 'Back';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex items-center justify-between pt-6 border-t border-gray-200 ${className}`}
    >
      {/* Left side - Back button and optional "Go to First" */}
      <div className="flex items-center gap-3">
        <Button
          variant="outline"
          onClick={onBack}
          disabled={isSubmitting}
          className="flex items-center gap-2"
          size="lg"
        >
          {isFirstStep ? (
            <Home className="w-4 h-4" />
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
          {getBackButtonText()}
        </Button>

        {/* Optional "Go to First Step" button for non-first steps */}
        {showGoToFirstButton && !isFirstStep && onGoToFirstStep && (
          <Button
            variant="ghost"
            onClick={onGoToFirstStep}
            disabled={isSubmitting}
            className="text-sm text-gray-500 hover:text-gray-700"
            size="sm"
          >
            Start Over
          </Button>
        )}
      </div>

      {/* Right side - Next/Submit button */}
      <Button
        onClick={onNext}
        disabled={!canGoNext || isSubmitting}
        className="flex items-center gap-2"
        size="lg"
      >
        {getNextButtonText()}
        {!isLastStep && <ArrowRight className="w-4 h-4" />}
      </Button>
    </motion.div>
  );
} 
