import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Star } from 'lucide-react';
import { GoogleSearchEngine } from './GoogleSearchEngine';

export function EnhancedHeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Floating Glass Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full backdrop-blur-xl border border-white/10"
        />
        <motion.div
          animate={{
            x: [0, -80, 0],
            y: [0, 60, 0],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/3 right-1/4 w-24 h-24 bg-gradient-to-br from-emerald-400/10 to-blue-600/10 rounded-2xl backdrop-blur-xl border border-white/10"
        />
        <motion.div
          animate={{
            x: [0, 50, 0],
            y: [0, -80, 0],
            rotate: [0, -90, 0],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/3 left-1/3 w-20 h-20 bg-gradient-to-br from-pink-400/10 to-orange-600/10 rounded-xl backdrop-blur-xl border border-white/10"
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          {/* Floating Service Icons */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[
              { icon: Sparkles, position: 'top-12 left-12', delay: 0 },
              { icon: Shield, position: 'top-20 right-16', delay: 0.5 },
              { icon: Clock, position: 'bottom-20 left-20', delay: 1 },
              { icon: Star, position: 'bottom-16 right-12', delay: 1.5 },
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ 
                  opacity: [0, 0.6, 0],
                  scale: [0, 1.2, 0],
                  rotate: [0, 360]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: item.delay,
                  ease: "easeInOut"
                }}
                className={`absolute ${item.position} w-12 h-12 bg-white/5 backdrop-blur-lg rounded-full border border-white/20 flex items-center justify-center`}
              >
                <item.icon className="w-6 h-6 text-white/60" />
              </motion.div>
            ))}
          </div>

          {/* Main Hero Content */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: 'easeOut' }}
            className="mb-12"
          >
            <motion.h1 
              className="text-6xl md:text-7xl lg:text-8xl font-bold text-white leading-tight mb-6"
              style={{ textShadow: '0 4px 30px rgba(0,0,0,0.4)' }}
            >
              <motion.span
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2, duration: 0.8 }}
                className="block"
              >
                Professional
              </motion.span>
              <motion.span
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4, duration: 0.8 }}
                className="block bg-gradient-to-r from-emerald-400 via-blue-400 to-purple-400 bg-clip-text text-transparent"
              >
                Cleaning Services
              </motion.span>
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="text-xl md:text-2xl text-white/80 max-w-3xl mx-auto mb-8"
            >
              Transform your space with our expert cleaning solutions. Trusted by thousands across the region.
            </motion.p>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="flex flex-wrap items-center justify-center gap-8 mb-12"
            >
              {[
                { icon: Shield, label: "Fully Insured" },
                { icon: Star, label: "4.9/5 Rating" },
                { icon: Clock, label: "Same Day Service" },
              ].map((item) => (
                <motion.div
                  key={item.label}
                  whileHover={{ scale: 1.05, y: -2 }}
                  className="flex items-center gap-2 bg-white/5 backdrop-blur-lg rounded-full px-4 py-2 border border-white/10"
                >
                  <item.icon className="w-5 h-5 text-emerald-400" />
                  <span className="text-white/90 text-sm font-medium">{item.label}</span>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Enhanced Search Engine */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
            <GoogleSearchEngine />
          </motion.div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2, duration: 0.8 }}
            className="mt-8 text-center"
          >
            <p className="text-white/60 text-sm">
              Get instant quotes • Book online • Available 7 days a week
            </p>
          </motion.div>
        </div>
      </div>

      {/* Background Gradient Mesh */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-900/20 via-blue-900/20 to-purple-900/20 pointer-events-none" />
    </section>
  );
} 
