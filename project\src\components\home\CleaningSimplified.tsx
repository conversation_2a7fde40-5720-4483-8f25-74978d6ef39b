import React from 'react';
import { <PERSON>, Leaf, Clock, CheckCircle } from 'lucide-react';
import { Button } from '../ui/Button';

const features = [
  {
    icon: Shield,
    title: 'Reliable Professionals',
    description: 'Our vetted and experienced cleaners ensure top-quality service every time.'
  },
  {
    icon: Leaf,
    title: 'Eco-Friendly Products',
    description: 'We use 100% non-toxic and sustainable cleaning solutions.'
  },
  {
    icon: Clock,
    title: 'Flexible Scheduling',
    description: 'Book services at a time that works best for you.'
  },
  {
    icon: CheckCircle,
    title: 'Satisfaction Guaranteed',
    description: 'Your happiness is our priority. We will not rest until you are satisfied.'
  }
];

interface CleaningSimplifiedProps {
  onBook: () => void;
}

export function CleaningSimplified({ onBook }: CleaningSimplifiedProps) {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-b from-brand-100 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-14 lg:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900">
            Cleaning Simplified
          </h2>
          <p className="mt-4 text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-2">
            Experience the difference with our professional cleaning services
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          {features.map((feature, index) => (
            <div
              key={feature.title}
              className="group bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 h-full flex flex-col"
              style={{
                animationDelay: `${index * 150}ms`,
                animation: 'fadeIn 0.6s ease-out forwards'
              }}
            >
              <div className="rounded-xl bg-brand-100 p-3 sm:p-4 inline-flex group-hover:bg-brand-200 transition-colors">
                <feature.icon className="w-6 h-6 sm:w-8 sm:h-8 text-brand-600" />
              </div>
              <h3 className="mt-4 sm:mt-6 text-lg sm:text-xl font-semibold text-gray-900">
                {feature.title}
              </h3>
              <p className="mt-3 sm:mt-4 text-sm sm:text-base text-gray-600 leading-relaxed flex-1">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        <div className="mt-12 sm:mt-16 lg:mt-20 bg-gradient-to-r from-brand-600 to-brand-700 rounded-2xl shadow-xl p-6 sm:p-8 lg:p-12 text-center">
          <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white">
            Ready to Experience Cleaner Spaces?
          </h3>
          <Button
            size="lg"
            onClick={onBook}
            className="mt-6 sm:mt-8 bg-white text-brand-600 hover:bg-brand-50 w-full sm:w-auto"
          >
            Get a Free Quote
          </Button>
        </div>
      </div>
    </section>
  );
}
