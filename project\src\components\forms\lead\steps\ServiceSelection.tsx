import React from 'react';
import { LayoutDashboard, Users, Bath, Coffee, Maximize2, Brush } from 'lucide-react';
import { ServiceCard } from '../ui/ServiceCard';

const services = [
  {
    id: 'desks',
    icon: LayoutDashboard,
    label: 'Desks',
    description: 'Individual workspace cleaning'
  },
  {
    id: 'conference',
    icon: Users,
    label: 'Conference Rooms',
    description: 'Meeting space sanitization'
  },
  {
    id: 'restrooms',
    icon: Bath,
    label: 'Restrooms',
    description: 'Bathroom cleaning & maintenance'
  },
  {
    id: 'breakrooms',
    icon: Coffee,
    label: 'Break Rooms',
    description: 'Kitchen & break area cleaning'
  },
  {
    id: 'windows',
    icon: Maximize2, // Changed from WindowMaximize to Maximize2
    label: 'Windows',
    description: 'Interior & exterior windows'
  },
  {
    id: 'carpets',
    icon: Brush,
    label: 'Carpets',
    description: 'Deep carpet cleaning'
  }
];

interface ServiceSelectionProps {
  selected: string[];
  onChange: (services: string[]) => void;
}

export function ServiceSelection({ selected, onChange }: ServiceSelectionProps) {
  const toggleService = (id: string) => {
    const newSelected = selected.includes(id)
      ? selected.filter(s => s !== id)
      : [...selected, id];
    onChange(newSelected);
  };

  return (
    <div>
      <h2 className="text-2xl font-medium mb-6">Select Services</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {services.map((service) => (
          <ServiceCard
            key={service.id}
            service={service}
            selected={selected.includes(service.id)}
            onClick={() => toggleService(service.id)}
          />
        ))}
      </div>
    </div>
  );
}
