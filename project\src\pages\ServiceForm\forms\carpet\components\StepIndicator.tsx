import React from 'react';
import { Check } from 'lucide-react';

interface StepIndicatorProps {
  steps: string[];
  currentStep: number;
}

export function StepIndicator({ steps, currentStep }: StepIndicatorProps) {
  return (
    <div className="relative px-4">
      <div className="absolute top-5 left-0 right-0 h-1 bg-gray-200">
        <div 
          className="h-full bg-brand-500 transition-all duration-500"
          style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
        />
      </div>

      <div className="relative flex justify-between">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;

          return (
            <div 
              key={step} 
              className="flex flex-col items-center"
            >
              <div 
                className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                  isCompleted ? 'bg-brand-600' : 
                  isCurrent ? 'bg-white border-2 border-brand-600' : 
                  'bg-gray-100'
                }`}
              >
                {isCompleted ? (
                  <Check className="w-6 h-6 text-white" />
                ) : (
                  <span className={`text-base font-medium ${
                    isCurrent ? 'text-brand-600' : 'text-gray-400'
                  }`}>
                    {index + 1}
                  </span>
                )}
              </div>
              <span className={`mt-4 text-sm font-medium whitespace-nowrap ${
                isCompleted ? 'text-brand-600' :
                isCurrent ? 'text-gray-900' :
                'text-gray-400'
              }`}>
                {step}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
