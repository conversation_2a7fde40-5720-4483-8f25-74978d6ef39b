import React from 'react';
import { Building2, Building, GraduationCap, Stethoscope, ShoppingBag, Warehouse } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'standard',
    icon: Building2,
    label: 'Standard Office Cleaning',
    description: 'Regular maintenance cleaning for office spaces'
  },
  {
    id: 'executive',
    icon: Building,
    label: 'Executive Office Package',
    description: 'Premium cleaning for high-end office spaces'
  },
  {
    id: 'medical',
    icon: Stethoscope,
    label: 'Medical Office Cleaning',
    description: 'Specialized cleaning for medical facilities'
  },
  {
    id: 'corporate',
    icon: Building2,
    label: 'Corporate Package',
    description: 'Full-service cleaning for large corporate offices'
  },
  {
    id: 'small-business',
    icon: ShoppingBag,
    label: 'Small Business Package',
    description: 'Affordable cleaning for small offices'
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">Select Your Office Cleaning Package</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`group relative w-full p-6 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className={`p-3 rounded-lg transition-colors ${
                  isSelected ? 'bg-brand-100' : 'bg-gray-50 group-hover:bg-brand-50'
                }`}>
                  <Icon className={`w-6 h-6 ${
                    isSelected ? 'text-brand-600' : 'text-gray-600'
                  }`} />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">{service.label}</h3>
                  <p className="text-sm text-gray-600">{service.description}</p>
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
