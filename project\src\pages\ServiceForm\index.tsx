import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { OfficeCleaningForm } from './forms/office/OfficeCleaningForm';
import { CarpetCleaningForm } from './forms/carpet/CarpetCleaningForm';
import { WindowCleaningForm } from './forms/window/WindowCleaningForm';
import { PostConstructionForm } from './forms/construction/PostConstructionForm';
import { SanitizationForm } from './forms/sanitization/SanitizationForm';
import { TileGroutForm } from './forms/tile/TileGroutForm';
import { PressureWashingForm } from './forms/pressure/PressureWashingForm';
import { FloorRestorationForm } from './forms/floor/FloorRestorationForm';
import { DeepCleaningForm } from './forms/deep/DeepCleaningForm';
import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';

export function ServiceFormPage() {
  const { serviceId } = useParams();
  const navigate = useNavigate();

  const handleBack = () => {
    navigate('/solutions');
  };

  const renderForm = () => {
    switch (serviceId) {
      case 'office':
        return <OfficeCleaningForm onBack={handleBack} />;
      case 'carpet':
        return <CarpetCleaningForm onBack={handleBack} />;
      case 'window':
        return <WindowCleaningForm onBack={handleBack} />;
      case 'construction':
        // Redirect to the brand-aligned residential form
        navigate('/residential/postconstruction');
        return null;
      case 'sanitization':
        return <SanitizationForm onBack={handleBack} />;
      case 'tile':
        return <TileGroutForm onBack={handleBack} />;
      case 'pressure':
        return <PressureWashingForm onBack={handleBack} />;
      case 'floor':
        return <FloorRestorationForm onBack={handleBack} />;
      case 'deep':
        // Redirect to the brand-aligned residential form
        navigate('/residential/deep');
        return null;
      default:
        navigate('/solutions');
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="py-24 sm:py-32">
        <div className="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12">
          {renderForm()}
        </div>
      </main>
      <Footer />
    </div>
  );
}
