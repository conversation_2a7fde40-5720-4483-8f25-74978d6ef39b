import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SlideTransitionProps {
  children: React.ReactNode;
  direction?: 1 | -1;
}

export function SlideTransition({ children, direction = 1 }: SlideTransitionProps) {
  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={React.Children.count(children)}
        initial={{ opacity: 0, x: 30 * direction }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -30 * direction }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30,
          duration: 0.5
        }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  );
}
