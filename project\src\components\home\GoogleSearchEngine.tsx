import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Search, MapPin, Home, Building, Sparkles, Shield, Construction, Wind, Droplets, Trash2, Star } from 'lucide-react';
import { navigateToServiceWithPropertyType, selectBestService } from '../../lib/utils/navigation';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  serviceType: string;
  propertyType: string;
  price: string;
  rating: number;
  availability: string;
  route: string;
  icon: any;
  popular?: boolean;
  emergency?: boolean;
  keywords: string[];
  tags: string[];
  category: string;
  subCategory: string;
  searchScore?: number;
  relevanceBoost?: number;
  seasonalBoost?: number;
  crossPlatform?: boolean;
}



export function GoogleSearchEngine() {
  const navigate = useNavigate();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const [zipCode, setZipCode] = useState('');
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  const serviceDatabase: SearchResult[] = [
    { id: 'regular-cleaning', title: 'Regular House Cleaning', description: 'Weekly, bi-weekly, or monthly house cleaning service', serviceType: 'regular', propertyType: 'residential', price: 'From $89', rating: 4.9, availability: 'Available today', route: '/residential/regular', icon: Home, popular: true, keywords: ['house cleaning', 'maid service', 'home cleaning'], tags: ['recurring', 'affordable'], category: 'Cleaning', subCategory: 'Routine', relevanceBoost: 1.2 },
    { id: 'deep-cleaning-residential', title: 'Deep Cleaning Service', description: 'Thorough one-time deep cleaning for your entire home', serviceType: 'deep', propertyType: 'residential', price: 'From $149', rating: 4.8, availability: 'Available tomorrow', route: '/residential/deep', icon: Sparkles, popular: true, crossPlatform: true, keywords: ['deep clean', 'spring cleaning', 'thorough cleaning'], tags: ['intensive', 'one-time'], category: 'Cleaning', subCategory: 'Specialized', relevanceBoost: 1.3, seasonalBoost: 1.1 },
    { id: 'office-cleaning', title: 'Office Cleaning Service', description: 'Daily, weekly, or monthly commercial office cleaning', serviceType: 'office', propertyType: 'commercial', price: 'From $199', rating: 4.8, availability: 'Flexible scheduling', route: '/service-form/office', icon: Building, popular: true, keywords: ['office cleaning', 'commercial cleaning', 'workplace cleaning'], tags: ['professional', 'corporate'], category: 'Commercial', subCategory: 'Office', relevanceBoost: 1.2 },
    { id: 'sanitization', title: 'Sanitization Service', description: 'Disinfect and sanitize your home or office.', serviceType: 'sanitization', propertyType: 'residential', price: 'From $129', rating: 4.9, availability: 'Available today', route: '/residential/sanitization', icon: Shield, keywords: ['sanitize', 'disinfect', 'germs'], tags: ['health', 'safety'], category: 'Cleaning', subCategory: 'Specialized' },
    { id: 'post-construction', title: 'Post-Construction Cleaning', description: 'Clean up after renovations or new builds.', serviceType: 'construction', propertyType: 'commercial', price: 'From $299', rating: 4.7, availability: '2-day notice', route: '/residential/post-construction', icon: Construction, keywords: ['construction', 'renovation', 'dust'], tags: ['new build', 'remodel'], category: 'Cleaning', subCategory: 'Specialized' },
    { id: 'window-cleaning', title: 'Window Cleaning', description: 'Crystal clear interior and exterior windows.', serviceType: 'window', propertyType: 'residential', price: 'From $99', rating: 4.8, availability: 'Available this week', route: '/residential/window-cleaning', icon: Wind, keywords: ['windows', 'glass', 'panes'], tags: ['streak-free', 'exterior'], category: 'Cleaning', subCategory: 'Exterior' },
    { id: 'carpet-cleaning', title: 'Carpet Cleaning', description: 'Deep clean and refresh your carpets.', serviceType: 'carpet', propertyType: 'residential', price: 'From $79', rating: 4.8, availability: 'Available tomorrow', route: '/residential/carpet-cleaning', icon: Droplets, keywords: ['carpet', 'rugs', 'stains'], tags: ['steam clean', 'shampoo'], category: 'Cleaning', subCategory: 'Flooring' },
    { id: 'move-out-cleaning', title: 'Move-Out Cleaning', description: 'Ensure you get your deposit back.', serviceType: 'move-out', propertyType: 'residential', price: 'From $199', rating: 4.9, availability: 'Flexible scheduling', route: '/residential/move-out', icon: Trash2, keywords: ['move out', 'end of lease', 'tenancy'], tags: ['deposit', 'empty home'], category: 'Cleaning', subCategory: 'Specialized' },
  ];

  useEffect(() => {
    const saved = localStorage.getItem('searchHistory');
    if (saved) setSearchHistory(JSON.parse(saved));
    
    const savedZip = localStorage.getItem('userZipCode');
    if (savedZip) setZipCode(savedZip);
  }, []);

  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }
    
    const normalizedQuery = searchQuery.toLowerCase().trim();
    const queryTokens = normalizedQuery.split(/\s+/).filter(token => token.length > 1);
    
    const calculateRelevanceScore = (service: SearchResult): number => {
      let totalScore = 0;
      
      const titleMatch = service.title.toLowerCase().includes(normalizedQuery) ? 40 : 0;
      totalScore += titleMatch;
      
      const descriptionMatch = service.description.toLowerCase().includes(normalizedQuery) ? 25 : 0;
      totalScore += descriptionMatch;
      
      const keywordMatch = service.keywords.some(kw => queryTokens.some(token => kw.includes(token))) ? 20 : 0;
      totalScore += keywordMatch;
      
      const categoryMatch = `${service.category} ${service.subCategory}`.toLowerCase().includes(normalizedQuery) ? 10 : 0;
      totalScore += categoryMatch;
      
      return totalScore;
    };
    
    const searchResults = serviceDatabase
      .map(service => ({ ...service, searchScore: calculateRelevanceScore(service) }))
      .filter(service => service.searchScore && service.searchScore > 20)
      .sort((a, b) => (b.searchScore ?? 0) - (a.searchScore ?? 0))
      .slice(0, 10);

    setResults(searchResults);
    setShowResults(searchResults.length > 0);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
        e.preventDefault();
      setSelectedIndex(prev => (prev + 1) % results.length);
    } else if (e.key === 'ArrowUp') {
        e.preventDefault();
      setSelectedIndex(prev => (prev - 1 + results.length) % results.length);
    } else if (e.key === 'Enter') {
      if (selectedIndex !== -1) {
        navigate(results[selectedIndex].route);
        } else {
        const bestMatch = selectBestService(results.map(r => r.id), { propertyType: 'residential' });
        if (bestMatch) {
          const service = results.find(r => r.id === bestMatch);
          if (service) {
            navigateToServiceWithPropertyType(navigate, service.id, 'residential');
          }
        }
      }
    }
  };

  return (
    <div 
      ref={searchRef} 
      className={`relative max-w-2xl mx-auto transition-all duration-300 ease-in-out ${showResults && (query.length > 0 || searchHistory.length > 0) ? 'mb-[26rem]' : 'mb-0'}`}
      onFocus={() => setShowResults(true)}
    >
      <motion.div
        className="relative rounded-2xl border border-white/20 backdrop-blur-lg shadow-2xl shadow-black/20"
        style={{ background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)' }}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, ease: 'easeOut' }}
      >
        <div className="flex items-center p-2">
          <div className="flex items-center pl-4 pr-2 text-white/60">
            <MapPin size={20} />
          </div>
            <input
              ref={inputRef}
              type="text"
            value={zipCode}
            onChange={(e) => setZipCode(e.target.value)}
              onKeyDown={handleKeyDown}
            placeholder="Zip Code"
            className="w-28 bg-transparent text-white placeholder-white/50 focus:outline-none"
          />
          <div className="w-px h-6 bg-white/20 mx-2" />
            <input
              type="text"
            value={query}
            onChange={(e) => {
              setQuery(e.target.value);
              performSearch(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            placeholder="What service do you need?"
            className="flex-grow bg-transparent text-white placeholder-white/50 focus:outline-none"
          />
          <motion.button
            onClick={() => performSearch(query)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="ml-4 p-3 rounded-xl bg-emerald-500 hover:bg-emerald-600 transition-colors shadow-lg"
          >
            <Search size={22} className="text-white" />
                          </motion.button>
              </div>
            </motion.div>

        <AnimatePresence>
        {showResults && (query.length > 0 || searchHistory.length > 0) && (
            <motion.div
              ref={resultsRef}
            initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute top-full mt-2 w-full rounded-2xl border border-white/10 bg-black/20 backdrop-blur-2xl shadow-2xl shadow-black/30 overflow-hidden"
          >
            {/* Search Results */}
            {results.length > 0 && (
              <div>
                <div className="px-4 py-3 text-sm text-white/70 border-b border-white/10">
                  {results.length} service{results.length !== 1 ? 's' : ''} found
                </div>
                <div className="p-2">
                {results.map((result, index) => {
                  const Icon = result.icon;
                  return (
                      <motion.div
                      key={result.id}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        onClick={() => {
                          const newHistory = [query, ...searchHistory.filter(h => h !== query)].slice(0, 5);
                          setSearchHistory(newHistory);
                          localStorage.setItem('searchHistory', JSON.stringify(newHistory));
                          localStorage.setItem('userZipCode', zipCode);
                          
                          navigateToServiceWithPropertyType(navigate, result.serviceType, result.propertyType);
                          setShowResults(false);
                        }}
                        className={`p-3 rounded-lg cursor-pointer transition-colors flex items-center gap-4 hover:bg-white/5 ${
                          selectedIndex === index ? 'bg-white/10' : ''
                        }`}
                      >
                        <div className="w-11 h-11 rounded-lg bg-white/5 flex-shrink-0 flex items-center justify-center border border-white/10">
                          <Icon className="w-5 h-5 text-emerald-300" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="text-white font-medium text-base">{result.title}</h3>
                            <span className="font-medium text-emerald-400 text-sm">{result.price}</span>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-white/60 mt-1">
                            <span className="flex items-center gap-1.5">
                              <Star className="w-4 h-4 text-yellow-400" />
                              {result.rating}
                            </span>
                            <span>{result.availability}</span>
                          </div>
                        </div>
                      </motion.div>
                  );
                })}
                </div>
              </div>
            )}

            {/* Search History */}
            {query.length === 0 && searchHistory.length > 0 && (
              <div>
                <div className="px-4 py-3 text-sm text-white/70 border-b border-white/10">Recent Searches</div>
                <div className="p-2">
                  {searchHistory.map((item, index) => (
                    <motion.div
                      key={item}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      onClick={() => {
                        setQuery(item);
                        performSearch(item);
                      }}
                      className="p-3 rounded-lg cursor-pointer hover:bg-white/5 transition-colors text-white/80 text-base"
                    >
                      {item}
                    </motion.div>
                  ))}
                </div>
                </div>
              )}
              
            {/* No Results */}
            {query.length > 0 && results.length === 0 && (
              <div className="p-8 text-center">
                <div className="text-white/70 text-base mb-2">No services found for "{query}"</div>
                <div className="text-white/50 text-sm">
                  Try "house cleaning", "office", or "deep clean"
                </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
    </div>
  );
} 
