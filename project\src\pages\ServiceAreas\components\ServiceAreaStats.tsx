import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Building2, Users, Star } from 'lucide-react';

const stats = [
  {
    icon: MapPin,
    value: '8+',
    label: 'States Covered',
    color: 'bg-blue-500'
  },
  {
    icon: Building2,
    value: '1,400+',
    label: 'Buildings Serviced',
    color: 'bg-green-500'
  },
  {
    icon: Users,
    value: '250+',
    label: 'Happy Clients',
    color: 'bg-purple-500'
  },
  {
    icon: Star,
    value: '4.9',
    label: 'Average Rating',
    color: 'bg-yellow-500'
  }
];

export function ServiceAreaStats() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-brand-50 to-brand-100/50 rounded-2xl transform -rotate-2 group-hover:rotate-0 transition-transform" />
                <div className="relative bg-white p-8 rounded-2xl shadow-lg group-hover:shadow-xl transition-all">
                  <div className={`p-3 ${stat.color} bg-opacity-10 rounded-xl w-fit mb-4`}>
                    <Icon className={`w-6 h-6 ${stat.color.replace('bg-', 'text-')}`} />
                  </div>
                  <div className="text-4xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
