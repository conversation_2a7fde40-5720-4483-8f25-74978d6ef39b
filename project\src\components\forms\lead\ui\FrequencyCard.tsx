import React from 'react';
import { Check } from 'lucide-react';
import type { FrequencyOption } from '../types';

interface FrequencyCardProps {
  frequency: FrequencyOption;
  selected: boolean;
  onClick: () => void;
}

export function FrequencyCard({ frequency, selected, onClick }: FrequencyCardProps) {
  const Icon = frequency.icon;

  return (
    <button
      onClick={onClick}
      className={`relative w-full p-6 rounded-xl transition-all duration-200 ${
        selected
          ? 'bg-green-50 border-2 border-green-500 shadow-md'
          : 'bg-white border border-gray-200 hover:border-green-300 hover:scale-105'
      }`}
      aria-pressed={selected}
    >
      {selected && (
        <div className="absolute top-3 right-3">
          <Check className="w-5 h-5 text-green-500" />
        </div>
      )}

      <div className="flex items-center space-x-4">
        <div className="p-3 rounded-full bg-green-100">
          <Icon className="w-6 h-6 text-green-600" />
        </div>
        <div className="flex-1 text-left">
          <h3 className="text-lg font-medium mb-1">{frequency.label}</h3>
          <p className="text-sm text-gray-600">{frequency.description}</p>
        </div>
      </div>
    </button>
  );
}
