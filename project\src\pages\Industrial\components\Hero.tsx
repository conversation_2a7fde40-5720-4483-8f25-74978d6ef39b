import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Warehouse, Shield, Star, Clock, ArrowRight } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { ServiceMenu } from '../../../components/services/ServiceMenu';

export function Hero() {
  const [showServiceMenu, setShowServiceMenu] = useState(false);

  return (
    <section className="relative min-h-[90vh] flex items-center pt-20">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80"
          alt="Industrial warehouse interior"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-brand-900/95 via-brand-800/90 to-brand-700/85" />
      </div>

      {/* Content */}
      <div className="relative z-10 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              className="inline-flex items-center justify-center p-3 bg-brand-500/20 backdrop-blur-sm rounded-xl mb-6"
            >
              <Warehouse className="w-6 h-6 text-white" />
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6 leading-tight"
            >
              Industrial & Warehouse
              <span className="block text-brand-200">Cleaning Solutions</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-xl text-brand-100 mb-8"
            >
              Specialized cleaning services for manufacturing facilities, warehouses, and industrial spaces
            </motion.p>

            {/* Trust Badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              {[
                { icon: Shield, text: 'OSHA Compliant' },
                { icon: Star, text: '4.9/5 Rating' },
                { icon: Clock, text: '24/7 Service' }
              ].map((badge, index) => (
                <motion.div
                  key={badge.text}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 + (index * 0.1) }}
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 flex items-center space-x-2
                           border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <badge.icon className="w-4 h-4 text-brand-100" />
                  <span className="text-sm text-white">{badge.text}</span>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex flex-col sm:flex-row items-center justify-center gap-4"
            >
              <Button
                size="lg"
                onClick={() => setShowServiceMenu(true)}
                className="bg-white text-brand-600 hover:bg-brand-50 w-full sm:w-auto"
              >
                Get Free Quote
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white/10 w-full sm:w-auto"
                onClick={() => window.location.href = '/contact'}
              >
                Contact Us
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Service Menu */}
      <ServiceMenu
        isOpen={showServiceMenu}
        onClose={() => setShowServiceMenu(false)}
        onServiceSelect={(serviceId) => {
          setShowServiceMenu(false);
          window.location.href = `/service-form/${serviceId}`;
        }}
      />
    </section>
  );
}
