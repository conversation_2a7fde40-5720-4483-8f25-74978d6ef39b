import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { ServiceSelection } from './steps/ServiceSelection';
import { FrequencySelection } from './steps/FrequencySelection';
import { AdditionalServices } from './steps/AdditionalServices';
import { OfficeDetails } from './steps/OfficeDetails';
import { ScheduleSelection } from './steps/ScheduleSelection';
import { ContactInfo } from './steps/ContactInfo';
import { Summary } from './steps/Summary';
import { ProgressBar } from './ui/ProgressBar';
import { initialFormData } from './types/form';
import type { FormData } from './types';

interface LeadFormProps {
  onBack: () => void;
}

const steps = [
  'Services',
  'Frequency',
  'Additional',
  'Office',
  'Schedule',
  'Contact',
  'Review'
];

export function LeadForm({ onBack }: LeadFormProps) {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>(initialFormData);

  const nextStep = () => setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  const prevStep = () => setCurrentStep((prev) => Math.max(prev - 1, 0));

  const handleSubmit = async () => {
    navigate('/thank-you', { state: { formData } });
  };

  return (
    <div className="min-h-screen bg-brand-100">
      <main className="relative pt-24 pb-12 px-4">
        <div className="max-w-3xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8">
            <ProgressBar 
              steps={steps} 
              currentStep={currentStep}
              color="#93C572"
            />
            
            <div className="mt-8 min-h-[400px]">
              <div className="transition-opacity duration-300">
                {currentStep === 0 && (
                  <ServiceSelection
                    selected={formData.services}
                    onChange={(services) => setFormData({ ...formData, services })}
                  />
                )}
                {currentStep === 1 && (
                  <FrequencySelection
                    selected={formData.frequency}
                    onChange={(frequency) => setFormData({ ...formData, frequency })}
                  />
                )}
                {currentStep === 2 && (
                  <AdditionalServices
                    selected={formData.additionalServices}
                    onChange={(additionalServices) => setFormData({ ...formData, additionalServices })}
                  />
                )}
                {currentStep === 3 && (
                  <OfficeDetails
                    details={formData.officeDetails}
                    onChange={(officeDetails) => setFormData({ ...formData, officeDetails })}
                  />
                )}
                {currentStep === 4 && (
                  <ScheduleSelection
                    schedule={formData.schedule}
                    onChange={(schedule) => setFormData({ ...formData, schedule })}
                  />
                )}
                {currentStep === 5 && (
                  <ContactInfo
                    contact={formData.contact}
                    onChange={(contact) => setFormData({ ...formData, contact })}
                  />
                )}
                {currentStep === 6 && (
                  <Summary
                    formData={formData}
                    onSubmit={handleSubmit}
                  />
                )}
              </div>

              <div className="mt-8 flex justify-between">
                {currentStep > 0 && (
                  <button
                    onClick={prevStep}
                    className="px-6 py-2 text-gray-600 hover:text-gray-900 transition-colors flex items-center touch-manipulation"
                  >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                  </button>
                )}
                {currentStep < steps.length - 1 && (
                  <button
                    onClick={nextStep}
                    className="ml-auto px-6 py-2 bg-brand-500 text-white rounded-lg hover:bg-brand-600 
                             transition-all hover:shadow-lg transform hover:-translate-y-0.5
                             touch-manipulation"
                  >
                    Continue
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
