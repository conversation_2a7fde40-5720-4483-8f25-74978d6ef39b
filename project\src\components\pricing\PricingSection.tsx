import React from 'react';
import { PricingCard } from './PricingCard';

const plans = [
  {
    name: 'Basic',
    price: '209',
    description: 'Perfect for small offices',
    features: [
      'Weekly cleaning service',
      'Basic sanitization',
      'Trash removal',
      'Vacuum and mopping',
      'Window sill cleaning'
    ]
  },
  {
    name: 'Professional',
    price: '419',
    description: 'Most popular for medium offices',
    features: [
      'Everything in Basic',
      'Deep carpet cleaning',
      'Restroom sanitization',
      'Kitchen cleaning',
      'Window cleaning',
      'Air vent cleaning'
    ],
    highlighted: true
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    description: 'For large offices and buildings',
    features: [
      'Everything in Professional',
      'Custom cleaning schedule',
      'Dedicated cleaning team',
      'Emergency cleaning service',
      '24/7 support',
      'Monthly deep cleaning'
    ]
  }
];

export function PricingSection() {
  return (
    <section id="pricing" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <span className="text-brand-600 font-semibold">Pricing</span>
          <h2 className="mt-2 text-3xl font-bold text-gray-900">
            Simple, Transparent Pricing
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            Choose the perfect plan for your business needs
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan) => (
            <PricingCard key={plan.name} plan={plan} />
          ))}
        </div>
      </div>
    </section>
  );
}
