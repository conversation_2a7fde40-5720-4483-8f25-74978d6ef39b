import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Calendar, Clock, MapPin, Users, Building, Sun, ChevronDown } from 'lucide-react';
import { supabase } from '../../../lib/supabase/client';
import { useAuth } from '../../../lib/auth/AuthProvider';
import { calculatePrice, type PricingInput } from '../../../lib/services/pricingService';
import { normalizeServiceType, getServiceDisplayName as getRegistryServiceDisplayName } from '../../../lib/services/serviceTypeRegistry';


type BookingStatus = 'Upcoming' | 'Completed';
type PropertyType = 'House' | 'Apartment' | 'Office' | 'Commercial';
type PaymentStatus = 'Paid' | 'Pending' | 'Refunded';

interface Booking {
  id: string;
  bookingId: string;
  service: string;
  serviceIcon: React.ElementType;
  date: string;
  time: string;
  status: BookingStatus;
  address: string;
  price: number;
  estimatedDuration: string;
  teamSize: number;
  specialInstructions: string;
  addOns: string[];
  priority: string;
  squareFootage: string;
  rooms: string[];
  // Enhanced details
  propertyType: PropertyType;
  bedrooms: number;
  bathrooms: number;
  floors: number;
  petFriendly: boolean;
  pets: string[];
  accessInstructions: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  customerNotes: string;
  cleaningFrequency: string;
  lastCleaned: string;
  paymentStatus: PaymentStatus;
  paymentMethod: string;
  customerName: string;
  customerPhone: string;
  customerEmail: string;
  preferredProducts?: string[];
  allergies?: string[];
  parkingInstructions?: string;
  securitySystem?: boolean;
  keyLocation?: string;
  suppliesProvided?: boolean;
  focusAreas?: string[];
  avoidAreas?: string[];
}



interface BookingsDisplayProps {
  newBooking?: {
    id?: number | string;
    type?: string;
    serviceType?: string;
    totalPrice?: number;
    estimate?: number;
    status?: string;
    date?: string;
    time?: string;
    preferredDate?: string;
    preferredTime?: string;
    address?: string;
    city?: string;
    zipCode?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    propertyType?: string;
    bedrooms?: string;
    bathrooms?: string;
    frequency?: string;
    specialInstructions?: string;
    addOns?: string[];
    contact?: {
      name?: string;
      email?: string;
      phone?: string;
    };
    message?: string;
  };
}

export function BookingsDisplay({ newBooking }: BookingsDisplayProps) {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [showNewBookingNotification, setShowNewBookingNotification] = useState(false);

  useEffect(() => {
    // Show notification for new booking
    if (newBooking) {
      setShowNewBookingNotification(true);
      // Hide notification after 5 seconds
      setTimeout(() => setShowNewBookingNotification(false), 5000);
    }
  }, [newBooking]);

  useEffect(() => {
    const fetchBookings = async () => {
      if (!user) {
        console.log('No user found, skipping booking fetch');
        return;
      }
      
      console.log('Fetching bookings for user:', user.id);
      
      try {
        const { data, error } = await supabase!
          .from('booking_forms')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching bookings:', error);
          console.error('Error details:', error.details);
          console.error('Error message:', error.message);
          console.error('Error code:', error.code);
          return;
        }

        console.log('Fetched bookings from database:', data);
        console.log('Number of bookings found:', data?.length || 0);
        
        // Debug: Show structure of first booking if any exist
        if (data && data.length > 0) {
          console.log('Sample booking structure:', data[0]);
          console.log('Service type from DB:', data[0].service_type);
          console.log('Service display name:', getRegistryServiceDisplayName(data[0].service_type));
          console.log('Service details:', data[0].service_details);
          console.log('Property details:', data[0].property_details);
          console.log('Contact details:', data[0].contact);
        }

        // Transform database data to match UI format
        const transformedBookings: Booking[] = data?.map((booking) => {
          // Extract data from JSONB fields since that's where we store everything
          const serviceDetails = booking.service_details || {};
          const propertyDetails = booking.property_details || {};
          const contactDetails = booking.contact || {};
          const scheduleDetails = booking.schedule || {};

          // Determine payment status
          let paymentStatus: PaymentStatus = 'Pending';
          if (booking.payment_status === 'paid' || booking.status === 'confirmed') {
            paymentStatus = 'Paid';
          } else if (booking.status === 'cancelled') {
            paymentStatus = 'Refunded';
          }

          return {
            id: booking.id,
            bookingId: `EP-${booking.id.slice(0, 6).toUpperCase()}`,
            service: getRegistryServiceDisplayName(normalizeServiceType(booking.service_type)),
            serviceIcon: getServiceIcon(normalizeServiceType(booking.service_type)),
            date: new Date(booking.created_at).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            time: new Date(booking.created_at).toLocaleTimeString('en-US', {
              hour: 'numeric',
              minute: '2-digit'
            }),
            status: booking.status === 'completed' ? 'Completed' : 'Upcoming',
            address: propertyDetails.address || `${propertyDetails.city || ''}, ${propertyDetails.zipCode || ''}`.trim() || 'Address not provided',
            price: calculateBookingPrice(booking, serviceDetails, propertyDetails),
            estimatedDuration: '2-4 hours',
            teamSize: 2,
            specialInstructions: serviceDetails.specialInstructions || '',
            addOns: serviceDetails.addOns || [],
            priority: 'Standard',
            squareFootage: '2,500 sq ft',
            rooms: [],
            // Enhanced details
            propertyType: (propertyDetails.type as PropertyType) || 'House',
            bedrooms: parseInt(propertyDetails.bedrooms || '0') || 0,
            bathrooms: parseFloat(propertyDetails.bathrooms || '0') || 0,
            floors: 1,
            petFriendly: false,
            pets: [],
            accessInstructions: serviceDetails.specialInstructions || '',
            customerNotes: serviceDetails.specialInstructions || '',
            cleaningFrequency: serviceDetails.frequency || scheduleDetails.frequency || 'One-time',
            lastCleaned: 'Unknown',
            paymentStatus: paymentStatus,
            paymentMethod: 'Credit Card',
            customerName: `${contactDetails.firstName || ''} ${contactDetails.lastName || ''}`.trim(),
            customerPhone: contactDetails.phone || '',
            customerEmail: contactDetails.email || '',
          };
        }) || [];

        // Add new booking to the list if it exists
        if (newBooking) {
          const newBookingTransformed: Booking = {
            id: newBooking.id?.toString() || Date.now().toString(),
            bookingId: `EP-${(newBooking.id?.toString() || Date.now().toString()).slice(0, 6).toUpperCase()}`,
            service: newBooking.type || newBooking.serviceType || 'Regular House Cleaning',
            serviceIcon: Building,
            date: newBooking.preferredDate || newBooking.date || new Date().toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            }),
            time: newBooking.preferredTime || newBooking.time || new Date().toLocaleTimeString('en-US', { 
              hour: 'numeric', 
              minute: '2-digit' 
            }),
            status: 'Upcoming',
            address: newBooking.address || `${newBooking.city || ''}, ${newBooking.zipCode || ''}`.trim(),
            price: newBooking.totalPrice || newBooking.estimate || 0,
            estimatedDuration: '2-4 hours',
            teamSize: 2,
            specialInstructions: newBooking.specialInstructions || '',
            addOns: newBooking.addOns || [],
            priority: 'Standard',
            squareFootage: '2,500 sq ft',
            rooms: [],
            propertyType: (newBooking.propertyType as PropertyType) || 'House',
            bedrooms: parseInt(newBooking.bedrooms || '0') || 0,
            bathrooms: parseFloat(newBooking.bathrooms || '0') || 0,
            floors: 1,
            petFriendly: false,
            pets: [],
            accessInstructions: newBooking.specialInstructions || '',
            customerNotes: newBooking.specialInstructions || '',
            cleaningFrequency: newBooking.frequency || 'One-time',
            lastCleaned: 'Unknown',
            paymentStatus: 'Pending',
            paymentMethod: 'Credit Card',
            customerName: `${newBooking.firstName || ''} ${newBooking.lastName || ''}`.trim() || newBooking.contact?.name || '',
            customerPhone: newBooking.phone || newBooking.contact?.phone || '',
            customerEmail: newBooking.email || newBooking.contact?.email || '',
          };
          transformedBookings.unshift(newBookingTransformed);
        }

        console.log('Transformed bookings:', transformedBookings);
        console.log('Number of transformed bookings:', transformedBookings.length);
        
        if (transformedBookings.length > 0) {
          console.log('Sample transformed booking:', transformedBookings[0]);
        }
        
        setBookings(transformedBookings);
      } catch (error) {
        console.error('Error loading bookings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBookings();
  }, [user, newBooking]);

  const getServiceDisplayName = (serviceType: string) => {
    const serviceMap: Record<string, string> = {
      // Residential services
      'residential_regular': 'Regular House Cleaning',
      'residential_deep': 'Deep House Cleaning',
      'residential_move': 'Move-in/Move-out Cleaning',
      'residential': 'Residential Cleaning', // Fallback

      // Commercial services
      'office': 'Office Cleaning',
      'waste-management': 'Waste Management',
      'construction': 'Post-Construction',
      'sanitization': 'Sanitization',

      // Specialized services
      'carpet': 'Carpet Cleaning',
      'window': 'Window Cleaning',
      'deep': 'Deep Cleaning',
      'tile': 'Tile Cleaning',
      'pressure': 'Pressure Washing',
      'floor': 'Floor Restoration',
      'pool': 'Pool Cleaning'
    };
    return serviceMap[serviceType] || serviceType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Helper function to calculate booking price
  const calculateBookingPrice = (booking: any, serviceDetails: any, propertyDetails: any): number => {
    try {
      // First try to use stored price
      if (serviceDetails.totalPrice && serviceDetails.totalPrice > 0) {
        return serviceDetails.totalPrice;
      }

      // If no stored price, calculate using pricing service
      const normalizedServiceType = normalizeServiceType(booking.service_type || 'residential_regular');

      console.log('Calculating price for booking:', {
        originalServiceType: booking.service_type,
        normalizedServiceType: normalizedServiceType,
        storedPrice: serviceDetails.totalPrice
      });

      // Estimate square footage from property details
      const estimateSquareFootage = (): number => {
        if (propertyDetails.squareFootage) return propertyDetails.squareFootage;
        if (propertyDetails.size) {
          const sizeMap: Record<string, number> = {
            'studio': 500, 'small': 800, 'medium': 1200, 'large': 1800, 'xlarge': 2500
          };
          return sizeMap[propertyDetails.size] || 1200;
        }
        // Estimate based on bedrooms/bathrooms
        const bedrooms = parseInt(propertyDetails.bedrooms || '0') || 0;
        const bathrooms = parseInt(propertyDetails.bathrooms || '0') || 0;
        return Math.max((bedrooms * 300) + (bathrooms * 200), 800);
      };

      const pricingInput: PricingInput = {
        serviceType: normalizedServiceType,
        propertySize: estimateSquareFootage(),
        frequency: serviceDetails.frequency || 'onetime',
        addOns: serviceDetails.addOns || [],
        customOptions: {
          bedrooms: parseInt(propertyDetails.bedrooms || '0') || 0,
          bathrooms: parseInt(propertyDetails.bathrooms || '0') || 0,
          propertyType: propertyDetails.type || 'house'
        }
      };

      const pricingResult = calculatePrice(pricingInput);
      return Math.round(pricingResult.total);

    } catch (error) {
      console.error('Error calculating booking price:', error);
      // Fallback to a reasonable default based on service type
      const serviceType = booking.service_type || 'residential_regular';
      if (serviceType.includes('deep')) return 180;
      if (serviceType.includes('move')) return 200;
      if (serviceType.includes('office')) return 150;
      return 120; // Default residential regular
    }
  };

  const getServiceIcon = (serviceType: string) => {
    const iconMap: Record<string, React.ElementType> = {
      // Residential services
      'residential_regular': Sun,
      'residential_deep': Sun,
      'residential_move': Sun,
      'residential': Sun, // Fallback

      // Commercial services
      'office': Building,
      'waste-management': Building,
      'construction': Users,
      'sanitization': Building,

      // Specialized services
      'carpet': Building,
      'window': Building,
      'deep': Sun,
      'tile': Building,
      'pressure': Building,
      'floor': Building,
      'pool': Building
    };
    return iconMap[serviceType] || Building;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-white text-lg">Loading bookings...</div>
      </div>
    );
  }

  const upcomingBookings = bookings.filter(b => b.status === 'Upcoming');
  const pastBookings = bookings.filter(b => b.status === 'Completed');

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-4 md:space-y-6 pt-12"
    >
      {/* New Booking Success Notification */}
      <AnimatePresence>
        {showNewBookingNotification && newBooking && (
          <motion.div
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            className="mb-4 md:mb-6 p-3 md:p-4 bg-green-500/10 border border-green-400/30 rounded-xl"
          >
            <div className="flex items-center gap-2 md:gap-3">
              <div className="w-6 h-6 md:w-8 md:h-8 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                <Calendar className="w-3 h-3 md:w-4 md:h-4 text-green-400" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="text-green-400 font-semibold text-sm md:text-base">Booking Confirmed!</h3>
                <p className="text-green-300 text-xs md:text-sm">{newBooking.message}</p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <header className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 md:mb-8">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl md:text-3xl font-bold text-white">Your Bookings</h1>
          <p className="text-gray-400 mt-1 text-sm md:text-base">Manage your upcoming and past cleaning services.</p>
        </div>
        <button 
          onClick={() => navigate('/residential')}
          className="relative group bg-gradient-to-r from-white/5 via-emerald-500/5 to-white/5 backdrop-blur-2xl border border-white/10 text-white px-4 md:px-5 py-2 md:py-2.5 rounded-lg text-sm font-medium hover:bg-gradient-to-r hover:from-emerald-500/10 hover:via-emerald-400/15 hover:to-emerald-500/10 hover:border-emerald-400/20 hover:text-emerald-100 transition-all duration-200 hover:shadow-lg hover:shadow-emerald-500/10 w-full sm:w-auto"
        >
          {/* Subtle green glass shine */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-emerald-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
          
          <span className="relative z-10">Book New Service</span>
        </button>
      </header>

      <div className="space-y-8 md:space-y-12">
        <section>
          <h2 className="text-lg md:text-xl font-semibold text-white mb-3 md:mb-4">Upcoming</h2>
          <div className="space-y-4 md:space-y-6">
            {upcomingBookings.length > 0 ? (
              upcomingBookings.map((booking, index) => (
                <BookingCard key={booking.id} booking={booking} index={index} />
              ))
            ) : (
              <p className="text-gray-500 text-sm md:text-base">No upcoming bookings.</p>
            )}
          </div>
        </section>
        
        <section>
          <h2 className="text-lg md:text-xl font-semibold text-white mb-3 md:mb-4">History</h2>
          <div className="space-y-4 md:space-y-6">
            {pastBookings.map((booking, index) => (
              <BookingCard key={booking.id} booking={booking} index={index} isPast />
            ))}
          </div>
        </section>
      </div>
    </motion.div>
  );
}

function BookingCard({ booking, index, isPast = false }: { booking: Booking; index: number, isPast?: boolean }) {
  const [isExpanded, setExpanded] = useState(false);
  const ServiceIcon = booking.serviceIcon;

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        delay: index * 0.1,
        duration: 0.4,
        ease: 'easeOut'
      }
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'text-red-400 bg-red-500/20';
      case 'Standard': return 'text-blue-400 bg-blue-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getPaymentStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case 'Paid': return 'text-green-400 bg-green-500/20';
      case 'Pending': return 'text-yellow-400 bg-yellow-500/20';
      case 'Refunded': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  return (
    <motion.div 
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover={{ 
        y: -1,
        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.15)",
        transition: { duration: 0.15, ease: [0.4, 0, 0.2, 1] }
      }}
      transition={{ type: 'spring', stiffness: 300, damping: 20 }}
      className={`bg-white/[0.04] backdrop-blur-[40px] border border-white/30 rounded-2xl shadow-2xl shadow-black/50 overflow-hidden relative ${isPast && 'opacity-70'} will-change-transform backface-visibility-hidden perspective-1000`}
      style={{ 
        transform: 'translateZ(0)',
        WebkitFontSmoothing: 'antialiased',
        MozOsxFontSmoothing: 'grayscale'
      }}
    >
      {/* Ultra-clear liquid glass overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/[0.08] via-white/[0.02] to-transparent pointer-events-none" />
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/[0.01] to-white/[0.04] pointer-events-none" />
      
      <div className="p-4 md:p-6 relative z-10">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          <div className="flex items-center gap-3 md:gap-4 min-w-0 flex-1">
            <div className="w-10 h-10 md:w-12 md:h-12 bg-white/[0.08] backdrop-blur-[30px] border border-white/40 rounded-lg flex items-center justify-center shadow-lg shadow-white/10 flex-shrink-0">
              <ServiceIcon className="w-5 h-5 md:w-6 md:h-6 text-emerald-400" />
            </div>
            <div className="min-w-0 flex-1">
              <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-1">
                <h3 className="text-lg md:text-xl font-bold text-white truncate">{booking.service}</h3>
                <div className="flex gap-1 sm:gap-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full backdrop-blur-[25px] border border-white/30 ${getPriorityColor(booking.priority)} bg-white/[0.06] whitespace-nowrap`}>
                    {booking.priority}
                  </span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full backdrop-blur-[25px] border border-white/30 ${getPaymentStatusColor(booking.paymentStatus)} bg-white/[0.06] whitespace-nowrap`}>
                    {booking.paymentStatus}
                  </span>
                </div>
              </div>
              <p className="text-xs md:text-sm text-gray-400 truncate">{booking.customerName} • {booking.propertyType}</p>
            </div>
          </div>
          <div className="text-left sm:text-right flex-shrink-0">
            <p className="text-xl md:text-2xl font-bold text-white">${booking.price.toFixed(2)}</p>
            <p className="text-gray-500 text-xs">Service Cost</p>
          </div>
        </div>

        <div className="mt-4 md:mt-6 pt-3 md:pt-4 border-t border-white/20 grid grid-cols-1 sm:grid-cols-2 gap-2 md:gap-4 text-gray-400 text-sm">
          <InfoItem icon={Calendar} text={booking.date} />
          <InfoItem icon={Clock} text={booking.time} />
          <div className="sm:col-span-2">
            <InfoItem icon={MapPin} text={booking.address} />
          </div>
        </div>
      </div>
      
      {!isPast && (
        <>
          <div 
            className="bg-white/[0.02] backdrop-blur-[35px] px-4 md:px-6 py-2.5 md:py-3 flex justify-center items-center cursor-pointer hover:bg-white/[0.06] transition-all duration-300 border-t border-white/25"
            onClick={() => setExpanded(!isExpanded)}
          >
            <span className="text-xs md:text-sm font-medium text-gray-300 mr-2">View Details</span>
            <motion.div animate={{ rotate: isExpanded ? 180 : 0 }}>
              <ChevronDown className="w-3 h-3 md:w-4 md:h-4 text-gray-400" />
            </motion.div>
          </div>

          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="overflow-hidden"
              >
                <div className="p-4 md:p-6 bg-white/[0.03] backdrop-blur-[45px] border-t border-white/25 relative">
                  {/* Ultra-clear liquid glass background layers */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/[0.06] via-transparent to-white/[0.02] pointer-events-none" />
                  <div className="absolute inset-0 bg-gradient-to-t from-white/[0.01] via-transparent to-white/[0.03] pointer-events-none" />
                  
                  <div className="relative z-10">
                    {/* Clean Header */}
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-6 md:mb-8">
                      <h3 className="text-base md:text-lg font-medium text-white">Details</h3>
                      <span className="text-xs text-gray-400 font-mono bg-white/[0.05] backdrop-blur-[30px] px-2 py-1 rounded border border-white/35 shadow-lg shadow-white/5 w-fit">
                        {booking.bookingId}
                      </span>
                    </div>

                    {/* Minimal Grid Layout */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
                      {/* Left Column */}
                      <div className="space-y-6">
                        {/* Service Info */}
                        <div>
                          <h4 className="text-sm font-medium text-white mb-4">Service</h4>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-400">Duration</span>
                              <span className="text-white">{booking.estimatedDuration}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Team Size</span>
                              <span className="text-white">{booking.teamSize} cleaners</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Property Size</span>
                              <span className="text-white">{booking.squareFootage}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Payment</span>
                              <span className={`${booking.paymentStatus === 'Paid' ? 'text-green-400' : 'text-yellow-400'}`}>
                                {booking.paymentStatus}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Customer */}
                        <div>
                          <h4 className="text-sm font-medium text-white mb-4">Customer</h4>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-400">Name</span>
                              <span className="text-white">{booking.customerName}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Phone</span>
                              <span className="text-white">{booking.customerPhone}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Email</span>
                              <span className="text-white truncate ml-4">{booking.customerEmail}</span>
                            </div>
                          </div>
                        </div>

                        {/* Property */}
                        <div>
                          <h4 className="text-sm font-medium text-white mb-4">Property</h4>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-400">Type</span>
                              <span className="text-white">{booking.propertyType}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Bedrooms</span>
                              <span className="text-white">{booking.bedrooms}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Bathrooms</span>
                              <span className="text-white">{booking.bathrooms}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Right Column */}
                      <div className="space-y-6">
                        {/* Areas */}
                        <div>
                          <h4 className="text-sm font-medium text-white mb-4">Areas</h4>
                          <div className="flex flex-wrap gap-2">
                            {booking.rooms.map((room, idx) => (
                              <span key={idx} className="px-2 py-1 bg-white/[0.05] backdrop-blur-[25px] text-gray-300 rounded text-xs border border-white/35 shadow-md shadow-white/5">
                                {room}
                              </span>
                            ))}
                          </div>
                        </div>

                        {/* Add-ons */}
                        {booking.addOns && booking.addOns.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-white mb-4">Add-ons</h4>
                            <div className="space-y-2">
                              {booking.addOns.map((addon, idx) => (
                                <div key={idx} className="text-sm text-gray-300">
                                  • {addon}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Focus Areas */}
                        {booking.focusAreas && booking.focusAreas.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-white mb-4">Focus Areas</h4>
                            <div className="space-y-2">
                              {booking.focusAreas.map((area, idx) => (
                                <div key={idx} className="text-sm text-gray-300">
                                  • {area}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Instructions */}
                        <div>
                          <h4 className="text-sm font-medium text-white mb-4">Instructions</h4>
                          <div className="space-y-3">
                            <p className="text-sm text-gray-300 leading-relaxed">
                              {booking.accessInstructions}
                            </p>
                            {booking.customerNotes && (
                              <p className="text-sm text-gray-300 leading-relaxed border-l-2 border-white/20 pl-3">
                                {booking.customerNotes}
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Allergies */}
                        {booking.allergies && booking.allergies.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-red-400 mb-4">Allergies</h4>
                            <div className="flex flex-wrap gap-2">
                              {booking.allergies.map((allergy, idx) => (
                                <span key={idx} className="px-2 py-1 bg-red-500/[0.08] backdrop-blur-[25px] text-red-300 rounded text-xs border border-red-400/40 shadow-md shadow-red-500/10">
                                  {allergy}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Ultra-clear liquid glass action buttons */}
                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2 sm:gap-3 mt-6 md:mt-8 pt-4 md:pt-6 border-t border-white/20">
                      <button className="px-3 md:px-4 py-2 text-xs md:text-sm text-gray-400 hover:text-white transition-all duration-300 bg-white/[0.04] backdrop-blur-[30px] rounded border border-white/35 hover:border-white/50 hover:bg-white/[0.08] shadow-lg shadow-white/5">
                        Reschedule
                      </button>
                      <button className="px-3 md:px-4 py-2 text-xs md:text-sm text-gray-400 hover:text-white transition-all duration-300 bg-white/[0.04] backdrop-blur-[30px] rounded border border-white/35 hover:border-white/50 hover:bg-white/[0.08] shadow-lg shadow-white/5">
                        Contact
                      </button>
                      <button className="px-3 md:px-4 py-2 text-xs md:text-sm text-gray-400 hover:text-white transition-all duration-300 bg-white/[0.04] backdrop-blur-[30px] rounded border border-white/35 hover:border-white/50 hover:bg-white/[0.08] shadow-lg shadow-white/5">
                        Support
                      </button>
                      <button className="px-3 md:px-4 py-2 text-xs md:text-sm text-red-400 hover:text-red-300 transition-all duration-300 bg-red-500/[0.04] backdrop-blur-[30px] rounded border border-red-400/40 hover:border-red-400/60 hover:bg-red-500/[0.08] shadow-lg shadow-red-500/10">
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </>
      )}
    </motion.div>
  );
}

function InfoItem({ icon: Icon, text }: { icon: React.ElementType, text: string }) {
  return (
    <div className="flex items-center hover:bg-white/[0.04] p-2 md:p-3 rounded-xl transition-all duration-300 hover:backdrop-blur-[25px] hover:border hover:border-white/35 hover:shadow-lg hover:shadow-white/5 group relative overflow-hidden">
      {/* Ultra-clear hover glass effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/[0.03] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      <div className="w-6 h-6 md:w-8 md:h-8 bg-white/[0.06] backdrop-blur-[25px] border border-white/35 rounded-lg flex items-center justify-center mr-2 md:mr-3 flex-shrink-0 shadow-md shadow-white/5 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-white/[0.08] via-transparent to-transparent" />
        <Icon className="w-3 h-3 md:w-4 md:h-4 text-emerald-400 relative z-10" />
      </div>
      <span className="truncate text-gray-200 group-hover:text-white transition-colors duration-300 relative z-10 text-sm md:text-base">{text}</span>
    </div>
  );
}


 
