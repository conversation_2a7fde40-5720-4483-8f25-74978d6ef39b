import React from 'react';
import { Calendar } from 'lucide-react';

interface DatePickerProps {
  selected: Date | null;
  onChange: (date: Date) => void;
  minDate: Date;
}

export function DatePicker({ selected, onChange, minDate }: DatePickerProps) {
  return (
    <div className="relative">
      <div className="relative">
        <input
          type="date"
          value={selected?.toISOString().split('T')[0] || ''}
          min={minDate.toISOString().split('T')[0]}
          onChange={(e) => onChange(new Date(e.target.value))}
          className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
        />
        <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
      </div>
    </div>
  );
}
