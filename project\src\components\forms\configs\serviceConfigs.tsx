import React from 'react';
import { 
  Home, Sparkles, Calendar, Clock, CheckCircle, 
  Shield, Star, ArrowRight, Building2,
  Users, DollarSign, Heart, Brush, GlassWater,
  ChevronRight, ChevronLeft, Gift, Zap, Info,
  HomeIcon, Building, Castle, Warehouse, Droplets,
  Wind, Flame, Waves, Car, Sofa, Spray, Sun, 
  Eye, FlameKindling, Scissors, Wrench
} from 'lucide-react';

// Carpet Cleaning Configuration
export const carpetCleaningConfig = {
  serviceName: 'Carpet Cleaning',
  serviceIcon: <Brush className="w-8 h-8 text-brand-600" />,
  serviceDescription: 'Professional deep cleaning for all carpet types',
  steps: [
    {
      title: 'Carpet Details',
      subtitle: 'Tell us about your carpets',
      icon: <Brush className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date and timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Enhance Service',
      subtitle: 'Add protective treatments',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ],
  propertyTypes: [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Studio to 3BR',
      details: 'Low-pile carpet typical',
      time: '1-3 hours'
    },
    { 
      id: 'house', 
      name: 'House', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Single family home',
      details: 'Mixed carpet types',
      time: '2-5 hours'
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: <Building className="w-6 h-6" />, 
      description: 'Multi-level home',
      details: 'Stairs included',
      time: '3-5 hours'
    }
  ],
  serviceOptions: [
    { 
      id: '1-room', 
      name: '1 Room', 
      description: 'Living room or bedroom',
      price: 'From $89',
      basePrice: 89
    },
    { 
      id: '2-rooms', 
      name: '2 Rooms', 
      description: 'Living room + 1 bedroom',
      price: 'From $159',
      basePrice: 159,
      popular: true
    },
    { 
      id: '3-rooms', 
      name: '3 Rooms', 
      description: 'Common areas + bedrooms',
      price: 'From $219',
      basePrice: 219
    },
    { 
      id: '4-rooms', 
      name: '4 Rooms', 
      description: 'Whole home coverage',
      price: 'From $279',
      basePrice: 279
    },
    { 
      id: '5-plus-rooms', 
      name: '5+ Rooms', 
      description: 'Large home/custom',
      price: 'From $339',
      basePrice: 339
    }
  ],
  addOnServices: [
    { 
      id: 'stain-protection', 
      name: 'Scotchgard Protection', 
      description: 'Stain & spill resistance',
      price: 35, 
      icon: <Shield className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'pet-odor', 
      name: 'Pet Odor Treatment', 
      description: 'Enzyme-based odor elimination',
      price: 45, 
      icon: <Sparkles className="w-5 h-5" /> 
    },
    { 
      id: 'furniture-moving', 
      name: 'Furniture Moving', 
      description: 'We move & replace furniture',
      price: 25, 
      icon: <Home className="w-5 h-5" /> 
    }
  ],
  calculatePrice: (formData: any) => {
    const roomPrices: Record<string, number> = {
      '1-room': 89,
      '2-rooms': 159,
      '3-rooms': 219,
      '4-rooms': 279,
      '5-plus-rooms': 339
    };
    const addOnPrices: Record<string, number> = {
      'stain-protection': 35,
      'pet-odor': 45,
      'furniture-moving': 25
    };
    const basePrice = roomPrices[formData.propertySize] || 159;
    const addOnTotal = formData.addOns?.reduce((total: number, addon: string) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;
    return Math.round(basePrice + addOnTotal);
  },
  benefits: [
    {
      icon: <CheckCircle className="w-4 h-4" />,
      title: 'Deep Dirt Extraction',
      description: 'Removes embedded dirt & allergens'
    },
    {
      icon: <Shield className="w-4 h-4" />,
      title: 'Extends Carpet Life',
      description: 'Professional care preserves investment'
    },
    {
      icon: <Sparkles className="w-4 h-4" />,
      title: 'Healthier Home',
      description: 'Eliminates bacteria & dust mites'
    }
  ],
  specialOffer: {
    title: 'Carpet Special',
    discount: '15% OFF',
    description: 'First-time carpet cleaning + free stain assessment',
    urgency: 'Book this week • Limited availability'
  }
};

// Window Cleaning Configuration
export const windowCleaningConfig = {
  serviceName: 'Window Cleaning',
  serviceIcon: <GlassWater className="w-8 h-8 text-brand-600" />,
  serviceDescription: 'Crystal clear windows, inside and out',
  steps: [
    {
      title: 'Window Details',
      subtitle: 'Tell us about your windows',
      icon: <GlassWater className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date and timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Add-on Services',
      subtitle: 'Screen & frame cleaning',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ],
  propertyTypes: [
    { 
      id: 'house', 
      name: 'House', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Single/multi-family home',
      details: 'Standard residential windows',
      time: '1-4 hours'
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Apartment or condo unit',
      details: 'Interior focus, limited exterior',
      time: '1-2 hours'
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: <Building className="w-6 h-6" />, 
      description: 'Multi-level attached home',
      details: 'Multiple levels, some restrictions',
      time: '2-3 hours'
    }
  ],
  serviceOptions: [
    { 
      id: '1-10', 
      name: '1-10 Windows', 
      description: 'Small home/apartment',
      price: 'From $89',
      basePrice: 89
    },
    { 
      id: '11-20', 
      name: '11-20 Windows', 
      description: 'Average home',
      price: 'From $159',
      basePrice: 159,
      popular: true
    },
    { 
      id: '21-30', 
      name: '21-30 Windows', 
      description: 'Large home',
      price: 'From $229',
      basePrice: 229
    },
    { 
      id: '31-40', 
      name: '31-40 Windows', 
      description: 'Very large home',
      price: 'From $299',
      basePrice: 299
    },
    { 
      id: '40-plus', 
      name: '40+ Windows', 
      description: 'Estate/commercial',
      price: 'From $369',
      basePrice: 369
    }
  ],
  addOnServices: [
    { 
      id: 'screen-cleaning', 
      name: 'Screen Cleaning', 
      description: 'Remove & clean all screens',
      price: 25, 
      icon: <Shield className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'frame-cleaning', 
      name: 'Frame & Track Cleaning', 
      description: 'Deep clean window frames',
      price: 20, 
      icon: <Sparkles className="w-5 h-5" /> 
    },
    { 
      id: 'sill-cleaning', 
      name: 'Window Sill Cleaning', 
      description: 'Interior & exterior sills',
      price: 15, 
      icon: <Home className="w-5 h-5" /> 
    }
  ],
  calculatePrice: (formData: any) => {
    const windowPrices: Record<string, number> = {
      '1-10': 89,
      '11-20': 159,
      '21-30': 229,
      '31-40': 299,
      '40-plus': 369
    };
    const addOnPrices: Record<string, number> = {
      'screen-cleaning': 25,
      'frame-cleaning': 20,
      'sill-cleaning': 15
    };
    const basePrice = windowPrices[formData.propertySize] || 159;
    const addOnTotal = formData.addOns?.reduce((total: number, addon: string) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;
    return Math.round(basePrice + addOnTotal);
  },
  benefits: [
    {
      icon: <CheckCircle className="w-4 h-4" />,
      title: 'Streak-Free Shine',
      description: 'Professional squeegee technique'
    },
    {
      icon: <Shield className="w-4 h-4" />,
      title: 'Safety First',
      description: 'Fully insured high-level work'
    },
    {
      icon: <Eye className="w-4 h-4" />,
      title: 'Better Views',
      description: 'Maximize natural light & curb appeal'
    }
  ],
  specialOffer: {
    title: 'Spring Special',
    discount: '20% OFF',
    description: 'First window cleaning + free screen cleaning',
    urgency: 'Book this month • Perfect weather'
  }
};

// Pressure Washing Configuration
export const pressureWashingConfig = {
  serviceName: 'Pressure Washing',
  serviceIcon: <Droplets className="w-8 h-8 text-brand-600" />,
  serviceDescription: 'High-pressure cleaning for exterior surfaces',
  steps: [
    {
      title: 'Surface Details',
      subtitle: 'Tell us what needs cleaning',
      icon: <Droplets className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date and timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Additional Services',
      subtitle: 'Sealing & protection',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ],
  propertyTypes: [
    { 
      id: 'house', 
      name: 'House', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Driveway, patio, siding',
      details: 'Most common surfaces',
      time: '2-4 hours'
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Balcony, entryway',
      details: 'Limited exterior access',
      time: '1-2 hours'
    },
    { 
      id: 'commercial', 
      name: 'Small Commercial', 
      icon: <Building className="w-6 h-6" />, 
      description: 'Storefront, walkways',
      details: 'Business exterior cleaning',
      time: '2-6 hours'
    }
  ],
  serviceOptions: [
    { 
      id: 'basic', 
      name: 'Basic Package', 
      description: 'Driveway OR patio/deck',
      price: 'From $129',
      basePrice: 129
    },
    { 
      id: 'standard', 
      name: 'Standard Package', 
      description: 'Driveway + patio/deck',
      price: 'From $199',
      basePrice: 199,
      popular: true
    },
    { 
      id: 'premium', 
      name: 'Premium Package', 
      description: 'Driveway + patio + walkways',
      price: 'From $269',
      basePrice: 269
    },
    { 
      id: 'complete', 
      name: 'Complete Package', 
      description: 'All exterior surfaces',
      price: 'From $349',
      basePrice: 349
    }
  ],
  addOnServices: [
    { 
      id: 'deck-sealing', 
      name: 'Deck Sealing', 
      description: 'Protect & preserve wood',
      price: 75, 
      icon: <Shield className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'concrete-sealing', 
      name: 'Concrete Sealing', 
      description: 'Stain protection for driveways',
      price: 65, 
      icon: <Sparkles className="w-5 h-5" /> 
    },
    { 
      id: 'gutter-cleaning', 
      name: 'Gutter Cleaning', 
      description: 'Clear debris from gutters',
      price: 85, 
      icon: <Home className="w-5 h-5" /> 
    }
  ],
  calculatePrice: (formData: any) => {
    const packagePrices: Record<string, number> = {
      'basic': 129,
      'standard': 199,
      'premium': 269,
      'complete': 349
    };
    const addOnPrices: Record<string, number> = {
      'deck-sealing': 75,
      'concrete-sealing': 65,
      'gutter-cleaning': 85
    };
    const basePrice = packagePrices[formData.propertySize] || 199;
    const addOnTotal = formData.addOns?.reduce((total: number, addon: string) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;
    return Math.round(basePrice + addOnTotal);
  },
  benefits: [
    {
      icon: <CheckCircle className="w-4 h-4" />,
      title: 'Instant Curb Appeal',
      description: 'Dramatic before/after results'
    },
    {
      icon: <Shield className="w-4 h-4" />,
      title: 'Surface Protection',
      description: 'Prevents long-term damage'
    },
    {
      icon: <Droplets className="w-4 h-4" />,
      title: 'Eco-Friendly Methods',
      description: 'Biodegradable cleaning solutions'
    }
  ],
  specialOffer: {
    title: 'Spring Cleaning',
    discount: '25% OFF',
    description: 'First pressure wash + free surface assessment',
    urgency: 'Perfect season for exterior cleaning'
  }
};

// Chimney Cleaning Configuration
export const chimneyCleaningConfig = {
  serviceName: 'Chimney Cleaning',
  serviceIcon: <Flame className="w-8 h-8 text-brand-600" />,
  serviceDescription: 'Professional chimney inspection & cleaning',
  steps: [
    {
      title: 'Chimney Details',
      subtitle: 'Fireplace & chimney info',
      icon: <Flame className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date and timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Safety Services',
      subtitle: 'Inspection & repairs',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ],
  propertyTypes: [
    { 
      id: 'house', 
      name: 'House', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Single family home',
      details: 'Standard fireplace access',
      time: '2-3 hours'
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: <Building className="w-6 h-6" />, 
      description: 'Attached home',
      details: 'May have access restrictions',
      time: '2-4 hours'
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Condo with fireplace',
      details: 'Building coordination required',
      time: '1-2 hours'
    }
  ],
  serviceOptions: [
    { 
      id: 'cleaning-only', 
      name: 'Cleaning Only', 
      description: 'Standard chimney cleaning',
      price: 'From $179',
      basePrice: 179
    },
    { 
      id: 'cleaning-inspection', 
      name: 'Cleaning + Inspection', 
      description: 'Cleaning plus safety inspection',
      price: 'From $249',
      basePrice: 249,
      popular: true
    },
    { 
      id: 'full-service', 
      name: 'Full Service', 
      description: 'Clean, inspect, minor repairs',
      price: 'From $329',
      basePrice: 329
    }
  ],
  addOnServices: [
    { 
      id: 'cap-inspection', 
      name: 'Chimney Cap Inspection', 
      description: 'Check & repair chimney cap',
      price: 45, 
      icon: <Shield className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'damper-check', 
      name: 'Damper Check', 
      description: 'Inspect & lubricate damper',
      price: 35, 
      icon: <Wrench className="w-5 h-5" /> 
    },
    { 
      id: 'creosote-removal', 
      name: 'Heavy Creosote Removal', 
      description: 'Extra cleaning for heavy buildup',
      price: 85, 
      icon: <Flame className="w-5 h-5" /> 
    }
  ],
  calculatePrice: (formData: any) => {
    const servicePrices: Record<string, number> = {
      'cleaning-only': 179,
      'cleaning-inspection': 249,
      'full-service': 329
    };
    const addOnPrices: Record<string, number> = {
      'cap-inspection': 45,
      'damper-check': 35,
      'creosote-removal': 85
    };
    const basePrice = servicePrices[formData.propertySize] || 249;
    const addOnTotal = formData.addOns?.reduce((total: number, addon: string) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;
    return Math.round(basePrice + addOnTotal);
  },
  benefits: [
    {
      icon: <CheckCircle className="w-4 h-4" />,
      title: 'Fire Safety',
      description: 'Prevent dangerous chimney fires'
    },
    {
      icon: <Shield className="w-4 h-4" />,
      title: 'Carbon Monoxide Prevention',
      description: 'Ensure proper ventilation'
    },
    {
      icon: <Flame className="w-4 h-4" />,
      title: 'Efficient Burning',
      description: 'Better airflow = better fires'
    }
  ],
  specialOffer: {
    title: 'Fall Safety Special',
    discount: '20% OFF',
    description: 'First chimney service + free safety inspection',
    urgency: 'Book before winter • Limited certified technicians'
  }
};

// Pool Cleaning Configuration  
export const poolCleaningConfig = {
  serviceName: 'Pool Cleaning',
  serviceIcon: <Waves className="w-8 h-8 text-brand-600" />,
  serviceDescription: 'Professional pool maintenance & cleaning',
  steps: [
    {
      title: 'Pool Details',
      subtitle: 'Tell us about your pool',
      icon: <Waves className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose frequency & timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Maintenance Services',
      subtitle: 'Chemical balancing & equipment',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ],
  propertyTypes: [
    { 
      id: 'residential', 
      name: 'Residential Pool', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Backyard swimming pool',
      details: 'Standard residential maintenance',
      time: '1-2 hours'
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo Pool', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Shared community pool',
      details: 'Building management coordination',
      time: '2-3 hours'
    },
    { 
      id: 'spa', 
      name: 'Spa/Hot Tub', 
      icon: <Droplets className="w-6 h-6" />, 
      description: 'Hot tub or spa',
      details: 'Specialized small water body care',
      time: '0.5-1 hour'
    }
  ],
  serviceOptions: [
    { 
      id: 'one-time', 
      name: 'One-Time Cleaning', 
      description: 'Single cleaning service',
      price: 'From $129',
      basePrice: 129
    },
    { 
      id: 'weekly', 
      name: 'Weekly Service', 
      description: 'Regular weekly maintenance',
      price: 'From $89/week',
      basePrice: 89,
      popular: true
    },
    { 
      id: 'bi-weekly', 
      name: 'Bi-Weekly Service', 
      description: 'Every two weeks',
      price: 'From $109/visit',
      basePrice: 109
    },
    { 
      id: 'monthly', 
      name: 'Monthly Service', 
      description: 'Once per month',
      price: 'From $149/visit',
      basePrice: 149
    }
  ],
  addOnServices: [
    { 
      id: 'chemical-balancing', 
      name: 'Chemical Balancing', 
      description: 'pH & chlorine adjustment',
      price: 35, 
      icon: <Sparkles className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'filter-cleaning', 
      name: 'Filter Cleaning', 
      description: 'Clean & maintain filters',
      price: 25, 
      icon: <Shield className="w-5 h-5" /> 
    },
    { 
      id: 'equipment-check', 
      name: 'Equipment Check', 
      description: 'Pump & equipment inspection',
      price: 45, 
      icon: <Wrench className="w-5 h-5" /> 
    }
  ],
  calculatePrice: (formData: any) => {
    const servicePrices: Record<string, number> = {
      'one-time': 129,
      'weekly': 89,
      'bi-weekly': 109,
      'monthly': 149
    };
    const addOnPrices: Record<string, number> = {
      'chemical-balancing': 35,
      'filter-cleaning': 25,
      'equipment-check': 45
    };
    const basePrice = servicePrices[formData.propertySize] || 89;
    const addOnTotal = formData.addOns?.reduce((total: number, addon: string) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;
    return Math.round(basePrice + addOnTotal);
  },
  benefits: [
    {
      icon: <CheckCircle className="w-4 h-4" />,
      title: 'Crystal Clear Water',
      description: 'Professional skimming & vacuuming'
    },
    {
      icon: <Shield className="w-4 h-4" />,
      title: 'Chemical Balance',
      description: 'Safe, properly balanced water'
    },
    {
      icon: <Waves className="w-4 h-4" />,
      title: 'Equipment Longevity',
      description: 'Extend pool system lifespan'
    }
  ],
  specialOffer: {
    title: 'Summer Ready',
    discount: '30% OFF',
    description: 'First month of weekly service + free water testing',
    urgency: 'Pool season starting • Book early'
  }
};

// Upholstery Cleaning Configuration
export const upholsteryCleaningConfig = {
  serviceName: 'Upholstery Cleaning',
  serviceIcon: <Sofa className="w-8 h-8 text-brand-600" />,
  serviceDescription: 'Professional furniture & upholstery cleaning',
  steps: [
    {
      title: 'Furniture Details',
      subtitle: 'Tell us about your furniture',
      icon: <Sofa className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date and timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Protection Services',
      subtitle: 'Fabric protection & treatment',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ],
  propertyTypes: [
    { 
      id: 'residential', 
      name: 'Residential', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Home furniture cleaning',
      details: 'Living room, bedroom furniture',
      time: '2-4 hours'
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Apartment furniture',
      details: 'Compact living spaces',
      time: '1-2 hours'
    },
    { 
      id: 'office', 
      name: 'Small Office', 
      icon: <Building className="w-6 h-6" />, 
      description: 'Office furniture cleaning',
      details: 'Reception, office chairs',
      time: '1-3 hours'
    }
  ],
  serviceOptions: [
    { 
      id: '1-piece', 
      name: '1 Piece', 
      description: 'Single chair or loveseat',
      price: 'From $79',
      basePrice: 79
    },
    { 
      id: '2-3-pieces', 
      name: '2-3 Pieces', 
      description: 'Sofa + chair combination',
      price: 'From $149',
      basePrice: 149,
      popular: true
    },
    { 
      id: '4-5-pieces', 
      name: '4-5 Pieces', 
      description: 'Full living room set',
      price: 'From $219',
      basePrice: 219
    },
    { 
      id: '6-plus-pieces', 
      name: '6+ Pieces', 
      description: 'Multiple rooms or large set',
      price: 'From $299',
      basePrice: 299
    }
  ],
  addOnServices: [
    { 
      id: 'fabric-protection', 
      name: 'Fabric Protection', 
      description: 'Stain & spill resistance treatment',
      price: 45, 
      icon: <Shield className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'odor-treatment', 
      name: 'Odor Treatment', 
      description: 'Pet & smoke odor elimination',
      price: 35, 
      icon: <Sparkles className="w-5 h-5" /> 
    },
    { 
      id: 'leather-conditioning', 
      name: 'Leather Conditioning', 
      description: 'Moisturize & protect leather',
      price: 55, 
      icon: <Sofa className="w-5 h-5" /> 
    }
  ],
  calculatePrice: (formData: any) => {
    const piecePrices: Record<string, number> = {
      '1-piece': 79,
      '2-3-pieces': 149,
      '4-5-pieces': 219,
      '6-plus-pieces': 299
    };
    const addOnPrices: Record<string, number> = {
      'fabric-protection': 45,
      'odor-treatment': 35,
      'leather-conditioning': 55
    };
    const basePrice = piecePrices[formData.propertySize] || 149;
    const addOnTotal = formData.addOns?.reduce((total: number, addon: string) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;
    return Math.round(basePrice + addOnTotal);
  },
  benefits: [
    {
      icon: <CheckCircle className="w-4 h-4" />,
      title: 'Deep Clean Extraction',
      description: 'Remove embedded dirt & allergens'
    },
    {
      icon: <Shield className="w-4 h-4" />,
      title: 'Fabric Protection',
      description: 'Extend furniture lifespan'
    },
    {
      icon: <Sofa className="w-4 h-4" />,
      title: 'Restore Appearance',
      description: 'Refresh colors & texture'
    }
  ],
  specialOffer: {
    title: 'Furniture Refresh',
    discount: '20% OFF',
    description: 'First upholstery cleaning + free fabric protection',
    urgency: 'Spring cleaning season • Book now'
  }
};

// Sanitization Configuration
export const sanitizationConfig = {
  serviceName: 'Sanitization',
  serviceIcon: <Spray className="w-8 h-8 text-brand-600" />,
  serviceDescription: 'Professional disinfection & sanitization',
  steps: [
    {
      title: 'Sanitization Needs',
      subtitle: 'Tell us about your space',
      icon: <Spray className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date and timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Treatment Options',
      subtitle: 'Deep disinfection & air treatment',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ],
  propertyTypes: [
    { 
      id: 'residential', 
      name: 'Residential', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Home sanitization',
      details: 'Living spaces, bedrooms, bathrooms',
      time: '2-4 hours'
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Apartment sanitization',
      details: 'Compact living space treatment',
      time: '1-2 hours'
    },
    { 
      id: 'office', 
      name: 'Small Office', 
      icon: <Building className="w-6 h-6" />, 
      description: 'Office space sanitization',
      details: 'Workstations, common areas',
      time: '1-3 hours'
    }
  ],
  serviceOptions: [
    { 
      id: 'basic', 
      name: 'Basic Sanitization', 
      description: 'High-touch surface treatment',
      price: 'From $99',
      basePrice: 99
    },
    { 
      id: 'standard', 
      name: 'Standard Service', 
      description: 'Comprehensive surface treatment',
      price: 'From $169',
      basePrice: 169,
      popular: true
    },
    { 
      id: 'deep', 
      name: 'Deep Sanitization', 
      description: 'Full space disinfection',
      price: 'From $249',
      basePrice: 249
    },
    { 
      id: 'medical-grade', 
      name: 'Medical Grade', 
      description: 'Hospital-level disinfection',
      price: 'From $329',
      basePrice: 329
    }
  ],
  addOnServices: [
    { 
      id: 'air-purification', 
      name: 'Air Purification', 
      description: 'UV air treatment system',
      price: 65, 
      icon: <Wind className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'follow-up', 
      name: 'Follow-up Treatment', 
      description: '48-hour follow-up service',
      price: 45, 
      icon: <Sparkles className="w-5 h-5" /> 
    },
    { 
      id: 'certification', 
      name: 'Sanitization Certificate', 
      description: 'Official completion certificate',
      price: 25, 
      icon: <Shield className="w-5 h-5" /> 
    }
  ],
  calculatePrice: (formData: any) => {
    const servicePrices: Record<string, number> = {
      'basic': 99,
      'standard': 169,
      'deep': 249,
      'medical-grade': 329
    };
    const addOnPrices: Record<string, number> = {
      'air-purification': 65,
      'follow-up': 45,
      'certification': 25
    };
    const basePrice = servicePrices[formData.propertySize] || 169;
    const addOnTotal = formData.addOns?.reduce((total: number, addon: string) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;
    return Math.round(basePrice + addOnTotal);
  },
  benefits: [
    {
      icon: <CheckCircle className="w-4 h-4" />,
      title: '99.9% Germ Elimination',
      description: 'Hospital-grade disinfectants'
    },
    {
      icon: <Shield className="w-4 h-4" />,
      title: 'EPA Approved Products',
      description: 'Safe for family & pets'
    },
    {
      icon: <Spray className="w-4 h-4" />,
      title: 'Long-lasting Protection',
      description: 'Antimicrobial barrier coating'
    }
  ],
  specialOffer: {
    title: 'Health & Safety',
    discount: '25% OFF',
    description: 'First sanitization + free air quality assessment',
    urgency: 'Protect your family • Book today'
  }
}; 
