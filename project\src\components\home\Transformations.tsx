import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Star } from 'lucide-react';
import { Button } from '../ui/Button';

const transformations = [
  {
    title: 'Office Deep Clean',
    before: 'https://images.unsplash.com/photo-1517697471339-4aa32003c11a?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80',
    after: 'https://images.unsplash.com/photo-1497366811353-6870744d04b2?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80',
    stats: '99.9% bacteria reduction'
  },
  {
    title: 'Carpet Restoration',
    before: 'https://images.unsplash.com/photo-1562772186-88ce4cd340c7?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80',
    after: 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80', 
    stats: '100% stain removal'
  },
  {
    title: 'Window Excellence',
    before: 'https://images.unsplash.com/photo-1577396682724-00ea71a4bd27?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80',
    after: 'https://images.unsplash.com/photo-1518481852452-9415f48f3251?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80',
    stats: 'Crystal clear results'
  }
];

export function Transformations() {
  return (
    <section className="py-24 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-flex items-center justify-center p-3 bg-brand-50 rounded-xl mb-4"
          >
            <Sparkles className="w-6 h-6 text-brand-600" />
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-4xl font-bold text-gray-900 mb-6"
          >
            See the Difference
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Experience our transformative cleaning solutions in action
          </motion.p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {transformations.map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
              className="group relative bg-white rounded-2xl shadow-lg overflow-hidden"
            >
              {/* Before/After Container */}
              <div className="relative h-64 overflow-hidden">
                {/* After Image */}
                <motion.div
                  className="absolute inset-0"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src={item.after}
                    alt="After cleaning"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-green-500 text-white text-sm font-medium px-3 py-1 rounded-full">
                    After
                  </div>
                </motion.div>
                
                {/* Before Image */}
                <motion.div
                  className="absolute inset-0"
                  initial={{ opacity: 1 }}
                  whileHover={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src={item.before}
                    alt="Before cleaning"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-gray-500 text-white text-sm font-medium px-3 py-1 rounded-full">
                    Before
                  </div>
                </motion.div>
              </div>

              {/* Content */}
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {item.title}
                </h3>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-brand-600" />
                    <span className="text-sm text-gray-600">{item.stats}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>
              </div>

              {/* Hover Instructions */}
              <div className="absolute inset-0 flex items-center justify-center bg-black/50 text-white opacity-0 group-hover:opacity-100 transition-opacity">
                <p className="font-medium">Hover to see the transformation</p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <Button size="lg" className="group">
            Book Your Transformation
            <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
