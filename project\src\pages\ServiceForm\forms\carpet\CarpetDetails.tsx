import React from 'react';
import { FormField } from '../../components/FormFields';

export function CarpetDetails() {
  return (
    <div className="space-y-6">
      <FormField label="Carpet Material" required>
        <select
          name="carpetMaterial"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="">Select material type</option>
          <option value="nylon">Nylon</option>
          <option value="polyester">Polyester</option>
          <option value="wool">Wool</option>
          <option value="olefin">Olefin</option>
          <option value="blend">Blend</option>
        </select>
      </FormField>

      <FormField label="Carpet Age">
        <select
          name="carpetAge"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="">Select carpet age</option>
          <option value="0-2">0-2 years</option>
          <option value="3-5">3-5 years</option>
          <option value="6-10">6-10 years</option>
          <option value="10+">10+ years</option>
        </select>
      </FormField>
    </div>
  );
}
