import React from 'react';

interface LogoProps {
  className?: string;
  textColor?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
}

export function Logo({ 
  className = '', 
  textColor = 'text-white', 
  size = 'md',
  showText = false
}: LogoProps) {
  const sizes = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12',
    xl: 'h-16'
  };

  return (
    <div className={`flex items-center ${className}`}>
      <img 
        src="/ep-logo.png"
        alt="Empire Pro Cleaning" 
        className={`${sizes[size]} w-auto`}
      />
      {showText && (
        <span className={`ml-2 text-xl font-bold ${textColor}`}>
          Empire Pro
        </span>
      )}
    </div>
  );
}
