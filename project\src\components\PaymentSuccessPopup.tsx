import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCir<PERSON>, Clock, X } from 'lucide-react';
import { usePaymentNotification } from '../hooks/usePaymentNotification';

export const PaymentSuccessPopup: React.FC = () => {
  const { showSuccessPopup, hideSuccessPopup, onPaymentSuccess } = usePaymentNotification();

  // Don't show popup if user is on ThankYou page (it has its own success component)
  const isOnThankYouPage = window.location.pathname === '/thankyou';
  const shouldShowPopup = showSuccessPopup && !isOnThankYouPage;

  useEffect(() => {
    if (showSuccessPopup) {
      // Auto-hide after 8 seconds (longer time to read the message)
      const timer = setTimeout(() => {
        hideSuccessPopup();
      }, 8000);

      return () => clearTimeout(timer);
    }
  }, [showSuccessPopup, hideSuccessPopup]);

  // Add manual trigger for testing
  useEffect(() => {
    const handleManualTrigger = (event: CustomEvent) => {
      onPaymentSuccess();
    };

    window.addEventListener('paymentSuccess', handleManualTrigger as EventListener);
    
    return () => {
      window.removeEventListener('paymentSuccess', handleManualTrigger as EventListener);
    };
  }, [onPaymentSuccess]);

  return (
    <>
      {/* Development Test Button - Only show in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-[200]">
          <button
            onClick={() => {
              onPaymentSuccess();
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium"
          >
            Test Popup
          </button>
        </div>
      )}

      <AnimatePresence>
        {shouldShowPopup && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-[100] flex items-center justify-center p-2 sm:p-4 lg:p-6"
            onClick={hideSuccessPopup}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 20 }}
              className="bg-white rounded-xl sm:rounded-2xl shadow-2xl p-4 sm:p-6 lg:p-8 max-w-xs sm:max-w-md lg:max-w-lg w-full mx-2 sm:mx-4 text-center relative"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={hideSuccessPopup}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>

              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6"
              >
                <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 text-green-600" />
              </motion.div>
              
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-lg sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2 sm:mb-3"
              >
                Payment Successful!
              </motion.h2>
              
              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-sm sm:text-base lg:text-lg text-gray-600 mb-4 sm:mb-6 leading-relaxed px-2"
              >
                Your booking has been confirmed! We'll send you a confirmation email shortly and contact you to schedule your service.
              </motion.p>
              
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex items-center justify-center gap-2 text-xs sm:text-sm lg:text-base text-gray-500"
              >
                <Clock className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5" />
                <span>Thank you for choosing our service!</span>
              </motion.div>


            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
