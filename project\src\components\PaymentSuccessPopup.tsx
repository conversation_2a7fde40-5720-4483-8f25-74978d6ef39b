import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, Clock, X } from 'lucide-react';
import { usePaymentNotification } from '../hooks/usePaymentNotification';

export const PaymentSuccessPopup: React.FC = () => {
  const { showSuccessPopup, hideSuccessPopup, onPaymentSuccess } = usePaymentNotification();

  // Don't show popup if user is on ThankYou page (it has its own success component)
  const isOnThankYouPage = window.location.pathname === '/thankyou';
  const shouldShowPopup = showSuccessPopup && !isOnThankYouPage;

  useEffect(() => {
    if (showSuccessPopup) {
      // Auto-hide after 8 seconds (longer time to read the message)
      const timer = setTimeout(() => {
        hideSuccessPopup();
      }, 8000);

      // Add ESC key listener
      const handleEscKey = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          hideSuccessPopup();
        }
      };

      document.addEventListener('keydown', handleEscKey);

      return () => {
        clearTimeout(timer);
        document.removeEventListener('keydown', handleEscKey);
      };
    }
  }, [showSuccessPopup, hideSuccessPopup]);

  // Add manual trigger for testing
  useEffect(() => {
    const handleManualTrigger = (event: CustomEvent) => {
      onPaymentSuccess();
    };

    window.addEventListener('paymentSuccess', handleManualTrigger as EventListener);
    
    return () => {
      window.removeEventListener('paymentSuccess', handleManualTrigger as EventListener);
    };
  }, [onPaymentSuccess]);

  return (
    <>
      {/* Development Test Button - Only show in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-[200]">
          <button
            onClick={() => {
              onPaymentSuccess();
            }}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium"
          >
            Test Popup
          </button>
        </div>
      )}

      <AnimatePresence>
        {shouldShowPopup && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-md z-[9999] flex items-center justify-center p-4 sm:p-6 lg:p-8"
            onClick={hideSuccessPopup}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 20 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="bg-white rounded-2xl sm:rounded-3xl shadow-2xl border border-gray-100 p-6 sm:p-8 lg:p-10 max-w-sm sm:max-w-md lg:max-w-lg w-full mx-4 text-center relative overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={hideSuccessPopup}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
                aria-label="Close popup"
              >
                <X className="w-5 h-5 sm:w-6 sm:h-6" />
              </button>

              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-50 to-emerald-50 rounded-full blur-3xl opacity-30" />
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-50 to-indigo-50 rounded-full blur-2xl opacity-20" />

              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 relative z-10"
              >
                <CheckCircle className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 text-green-600" />
              </motion.div>
              
              <motion.h2
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-3 sm:mb-4 relative z-10"
              >
                Booking Confirmed!
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-sm sm:text-base lg:text-lg text-gray-600 mb-6 sm:mb-8 leading-relaxed px-2 sm:px-4 relative z-10"
              >
                Your booking has been confirmed! We'll send you a confirmation email shortly and will contact you to schedule your service.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex items-center justify-center gap-2 text-xs sm:text-sm lg:text-base text-gray-500 relative z-10"
              >
                <Clock className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6" />
                <span>Thank you for choosing our service!</span>
              </motion.div>


            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
