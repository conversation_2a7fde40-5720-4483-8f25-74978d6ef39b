import React from 'react';
import { Link } from 'react-router-dom';
import { Facebook, Instagram, Phone, Mail, Clock, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { Logo } from '../ui/Logo';

const companyInfo = {
  phone: '(*************',
  email: '<EMAIL>',
  hours: 'Available 24/7',
  social: {
    facebook: 'https://www.facebook.com/empireprocleanings/',
    instagram: 'https://www.instagram.com/empireprocleaning/',
  }
};

const footerLinks = [
  {
    title: 'Company',
    links: [
      { label: 'About Us', href: '/about' },
      { label: 'Solutions', href: '/solutions' },
      { label: 'Contact Us', href: '/contact' }
    ]
  },
  {
    title: 'Service Areas',
    links: [
      { label: 'New York', href: '/service-areas#ny' },
      { label: 'New Jersey', href: '/service-areas#nj' },
      { label: 'Pennsylvania', href: '/service-areas#pa' },
      { label: 'View All Locations', href: '/service-areas' }
    ]
  },
  {
    title: 'Legal',
    links: [
      { label: 'Privacy Policy', href: '/privacy' },
      { label: 'Terms of Service', href: '/terms' }
    ]
  }
];

export function Footer() {
  return (
    <footer className="bg-white/5 backdrop-blur-xl border-t border-white/10 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="space-y-6 lg:col-span-1">
            <Logo size="lg" className="mb-6" textColor="text-white" showText={true} />
            <ul className="space-y-4">
              <li className="flex items-center text-white/80 group">
                <Phone className="w-5 h-5 mr-3 flex-shrink-0 text-emerald-400 group-hover:text-emerald-300 transition-colors" />
                <a href={`tel:${companyInfo.phone}`} className="hover:text-emerald-300 transition-colors">
                  {companyInfo.phone}
                </a>
              </li>
              <li className="flex items-center text-white/80 group">
                <Mail className="w-5 h-5 mr-3 flex-shrink-0 text-emerald-400 group-hover:text-emerald-300 transition-colors" />
                <a href={`mailto:${companyInfo.email}`} className="hover:text-emerald-300 transition-colors">
                  {companyInfo.email}
                </a>
              </li>
              <li className="flex items-center text-white/80">
                <Clock className="w-5 h-5 mr-3 flex-shrink-0 text-emerald-400" />
                <span>{companyInfo.hours}</span>
              </li>
            </ul>
          </div>

          {/* Footer Links */}
          {footerLinks.map((section) => (
            <div key={section.title}>
              <h4 className="text-lg font-semibold text-white mb-6">{section.title}</h4>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.label}>
                    <Link
                      to={link.href}
                      className="group flex items-center text-white/80 hover:text-emerald-300 transition-colors"
                    >
                      <ArrowRight className="w-4 h-4 mr-3 text-emerald-400 opacity-50 group-hover:opacity-100 group-hover:translate-x-1 transition-all" />
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="mt-16 pt-8 border-t border-white/10">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p className="text-white/60 text-sm">
              © 2025 Empire Pro Cleaning. All rights reserved.
            </p>
            <div className="flex items-center space-x-6">
              {[
                { icon: Facebook, href: companyInfo.social.facebook, label: 'Facebook' },
                { icon: Instagram, href: companyInfo.social.instagram, label: 'Instagram' },
              ].map(({ icon: Icon, href, label }) => (
                <motion.a
                  key={label}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  className="text-white/60 hover:text-emerald-400 transition-colors"
                  aria-label={label}
                >
                  <Icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
