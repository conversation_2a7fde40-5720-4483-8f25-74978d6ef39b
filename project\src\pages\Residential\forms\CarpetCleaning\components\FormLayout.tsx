import React from 'react';
import { FormHeader } from './FormHeader';
import { PricingEstimate } from './PricingEstimate';
import type { FormData } from '../types';

interface FormLayoutProps {
  children: React.ReactNode;
  formData: FormData;
  onBack: () => void;
}

export function FormLayout({ children, formData, onBack }: FormLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <FormHeader onBack={onBack} />
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-6">
              {children}
            </div>
          </div>
          
          <div className="lg:col-span-1">
            <div className="sticky top-8 space-y-6">
              <PricingEstimate formData={formData} />
              
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Why Choose Us</h3>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="w-8 h-8 rounded-full bg-brand-100 flex items-center justify-center mr-3 flex-shrink-0">
                      <span className="text-brand-600 font-semibold">1</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Experienced Technicians</h4>
                      <p className="text-sm text-gray-600">Our professionals have years of experience and certification.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-8 h-8 rounded-full bg-brand-100 flex items-center justify-center mr-3 flex-shrink-0">
                      <span className="text-brand-600 font-semibold">2</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Eco-Friendly Products</h4>
                      <p className="text-sm text-gray-600">Safe for your family, pets, and the environment.</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-8 h-8 rounded-full bg-brand-100 flex items-center justify-center mr-3 flex-shrink-0">
                      <span className="text-brand-600 font-semibold">3</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Satisfaction Guaranteed</h4>
                      <p className="text-sm text-gray-600">If you're not happy, we'll re-clean the area at no extra cost.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
