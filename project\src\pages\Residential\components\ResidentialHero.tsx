import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Home, Sparkles, Box, Construction, PartyPopper, Wind, Brush, GlassWater, Waves, Droplets, Flame, Sofa,
  ArrowRight, ChevronDown, Shield, Star, X
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const servicesList = [
  { icon: Home, title: 'Regular Cleaning', description: 'Consistent, high-quality cleaning for a pristine home environment.', route: 'regular' },
  { icon: Sparkles, title: 'Deep Cleaning', description: 'A thorough, top-to-bottom scrub for a truly immaculate result.', route: 'deep' },
  { icon: Box, title: 'Move In/Out', description: 'Specialized moving cleaning service for empty homes.', route: 'moveout' },
  { icon: Construction, title: 'Post-Construction', description: 'Final-pass cleaning to make your new space shine.', route: 'construction' },
  { icon: PartyPopper, title: 'Event Cleaning', description: 'Pre- and post-event services for a seamless occasion.', route: 'event' },
  { icon: Brush, title: 'Carpet Cleaning', description: 'Professional deep extraction and stain removal for carpets.', route: 'carpet' },
  { icon: Sofa, title: 'Upholstery Cleaning', description: 'Revitalize furniture and upholstery with deep cleaning.', route: 'upholstery' },
  { icon: GlassWater, title: 'Window Cleaning', description: 'Interior & exterior window cleaning for a streak-free shine.', route: 'window' },
  { icon: Waves, title: 'Pressure Washing', description: 'High-power washing for driveways, decks, and siding.', route: 'pressure' },
  { icon: Droplets, title: 'Sanitization', description: 'Disinfection services for a healthier environment.', route: 'sanitization' },
  { icon: Flame, title: 'Chimney Cleaning', description: 'Ensure safety and efficiency with professional chimney sweeping.', route: 'chimney' },
  { icon: Droplets, title: 'Pool Cleaning', description: 'Regular maintenance to keep your pool sparkling clean.', route: 'pool' },
];

export function ResidentialHero() {
  const [isModalOpen, setModalOpen] = useState(false);
  const navigate = useNavigate();

  const handleServiceSelect = (route: string) => {
    setModalOpen(false);
    navigate(`/residential/${route}`);
  };

  return (
    <>
      <section className="relative min-h-[85vh] flex items-center justify-center pt-20 text-white overflow-hidden">
        {/* Subtle Animated Background Elements */}
        <div className="absolute inset-0 z-0">
          {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
              className="absolute bg-white/5 rounded-full"
              style={{
                width: Math.random() * 80 + 20,
                height: Math.random() * 80 + 20,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              initial={{ opacity: 0, scale: 0.5 }}
            animate={{
                opacity: [0, 1, 0],
                y: [0, Math.random() * 40 - 20, 0],
                x: [0, Math.random() * 40 - 20, 0],
              }}
              transition={{
                duration: 15 + Math.random() * 10,
                  repeat: Infinity,
                ease: 'easeInOut',
                delay: Math.random() * 5,
              }}
            />
        ))}
      </div>

        {/* Main Content */}
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 text-center">
            <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-5xl md:text-7xl font-bold leading-tight mb-6"
            style={{ textShadow: '0 2px 20px rgba(0,0,0,0.3)' }}
              >
            Effortless Clean,
            <br />
            Impeccable Home.
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
            className="text-lg md:text-xl text-white/80 max-w-2xl mx-auto mb-10"
            >
            Discover a new standard of clean with our professional, eco-friendly residential services.
            </motion.p>

            <motion.div
            initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5, ease: 'easeOut' }}
            className="flex items-center justify-center"
            >
            <motion.button
              onClick={() => setModalOpen(true)}
              whileHover={{ 
                scale: 1.05, 
                y: -3,
                boxShadow: '0 10px 30px rgba(255,255,255,0.3)',
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 md:px-10 md:py-5 text-base md:text-lg font-bold rounded-xl transition-all duration-300 flex items-center gap-3 bg-gradient-to-r from-white to-gray-100 text-gray-900 shadow-lg hover:shadow-2xl relative overflow-hidden whitespace-nowrap"
              style={{
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                boxShadow: '0 8px 32px rgba(0,0,0,0.1), inset 0 1px 0 rgba(255,255,255,0.8)'
              }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-green-400/20 opacity-0"
                whileHover={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
              <span className="relative z-10 whitespace-nowrap">Book Now</span>
              <motion.div
                animate={{ rotate: isModalOpen ? 180 : 0 }}
                transition={{ duration: 0.3, ease: 'easeInOut' }}
                className="relative z-10"
              >
                <ChevronDown className="w-5 h-5" />
              </motion.div>
            </motion.button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6, ease: 'easeOut' }}
            className="flex justify-center items-center gap-8 mt-16"
            >
              {[
              { icon: Shield, text: 'Insured' },
              { icon: Star, text: '5-Star Rated' },
              { icon: Wind, text: 'Eco-Friendly' }
            ].map((badge) => (
              <div key={badge.text} className="flex items-center gap-2 text-white/70">
                <badge.icon className="w-5 h-5" />
                <span className="text-sm font-medium">{badge.text}</span>
              </div>
              ))}
            </motion.div>
        </div>
      </section>

      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/60 overflow-y-auto"
            onClick={() => setModalOpen(false)}
          >
            <div className="min-h-full flex items-center justify-center p-4 py-8">
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                transition={{ type: 'spring', stiffness: 400, damping: 40 }}
                className="w-full max-w-5xl max-h-[90vh] rounded-2xl shadow-2xl overflow-hidden flex flex-col"
                style={{
                  background: 'rgba(28, 29, 31, 0.75)',
                  backdropFilter: 'blur(24px)',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                }}
                onClick={(e) => e.stopPropagation()}
              >
              <header className="p-6 flex justify-between items-center border-b border-white/10">
                <h2 className="text-lg font-semibold text-white">Select a Service</h2>
                <motion.button
                  onClick={() => setModalOpen(false)}
                  className="w-8 h-8 rounded-full bg-white/5 hover:bg-white/10 flex items-center justify-center text-white/70"
                  whileHover={{ scale: 1.1, rotate: 90 }}
                >
                  <X size={18} />
                </motion.button>
              </header>

              <main className="flex-1 overflow-y-auto p-8">
                <motion.div 
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                  initial="hidden"
                  animate="visible"
                  variants={{
                    visible: { transition: { staggerChildren: 0.05 } }
                  }}
                >
                  {servicesList.map((service) => (
                    <motion.div
                      key={service.title}
                      variants={{
                        hidden: { opacity: 0, y: 20 },
                        visible: { opacity: 1, y: 0 },
                      }}
                      whileHover={{ 
                        y: -5,
                        backgroundColor: 'rgba(255, 255, 255, 0.08)',
                        transition: { type: 'spring', stiffness: 300 } 
                      }}
                      onClick={() => handleServiceSelect(service.route)}
                      className="group p-6 rounded-xl cursor-pointer"
                      style={{
                        background: 'rgba(255, 255, 255, 0.04)',
                        border: '1px solid rgba(255, 255, 255, 0.12)',
                      }}
                    >
                      <service.icon className="w-8 h-8 text-emerald-400 mb-4 transition-transform duration-300" />
                      <h3 className="text-lg font-semibold text-white mb-2">{service.title}</h3>
                      <p className="text-sm text-white/60 mb-4 min-h-[60px]">{service.description}</p>
                      <div className="flex items-center text-sm text-emerald-400 font-medium">
                        <span>Book Now</span>
                        <ArrowRight size={16} className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </motion.div>
                  ))}
            </motion.div>
              </main>
              
              <footer className="px-8 py-5 flex justify-between items-center border-t border-white/10 bg-black/20">
                <div className="flex items-center gap-6 text-sm text-white/60">
                  <div className="flex items-center gap-2">
                    <Shield size={16} />
                    <span>Licensed & Insured</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Star size={16} />
                    <span>5-Star Rated Service</span>
                  </div>
                </div>
                <button
                  onClick={() => setModalOpen(false)}
                  className="px-5 py-2 text-sm font-semibold bg-white/10 text-white/80 rounded-lg hover:bg-white/20 transition-colors"
                >
                  Close
                </button>
              </footer>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
