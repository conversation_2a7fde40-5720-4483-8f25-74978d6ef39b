import React from 'react';
import { motion } from 'framer-motion';
import { Users, Coffee, DoorOpen, Printer, Plants } from 'lucide-react';

interface OfficeLayoutProps {
  squareFootage: number;
  employeeCount: number;
}

export function OfficeLayout({ squareFootage, employeeCount }: OfficeLayoutProps) {
  const scale = squareFootage / 1000;
  
  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-lg font-medium text-slate-900 mb-4">Office Layout Preview</h3>
      
      <div className="relative aspect-[16/9] bg-slate-50 rounded-lg overflow-hidden">
        {/* Grid overlay */}
        <div className="absolute inset-0" style={{
          backgroundImage: 'linear-gradient(to right, #e2e8f0 1px, transparent 1px), linear-gradient(to bottom, #e2e8f0 1px, transparent 1px)',
          backgroundSize: `${20 * scale}px ${20 * scale}px`
        }} />

        {/* Main office areas */}
        <div className="relative p-4 h-full">
          {/* Work Area */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute left-4 top-4 right-1/3 bottom-1/2 bg-sky-50 rounded-lg p-3"
          >
            <div className="flex items-center text-sm text-slate-700 mb-2">
              <Users className="w-4 h-4 mr-1 text-sky-600" />
              Work Area
            </div>
            <div className="grid grid-cols-4 gap-2">
              {Array.from({ length: Math.min(employeeCount, 8) }).map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: i * 0.1 }}
                  className="aspect-square bg-white rounded-md shadow-sm"
                />
              ))}
            </div>
          </motion.div>

          {/* Meeting Room */}
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className="absolute left-4 bottom-4 right-1/3 h-[calc(50%-1rem)] bg-indigo-50 rounded-lg p-3"
          >
            <div className="flex items-center text-sm text-slate-700">
              <Users className="w-4 h-4 mr-1 text-indigo-600" />
              Meeting Room
            </div>
          </motion.div>

          {/* Facilities */}
          <div className="absolute right-4 top-4 bottom-4 w-1/3-4 space-y-4">
            {/* Break Room */}
            <motion.div 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="h-1/3 bg-amber-50 rounded-lg p-3"
            >
              <div className="flex items-center text-sm text-slate-700">
                <Coffee className="w-4 h-4 mr-1 text-amber-600" />
                Break Room
              </div>
            </motion.div>

            {/* Utilities */}
            <motion.div 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
              className="h-1/3 bg-emerald-50 rounded-lg p-3"
            >
              <div className="flex items-center text-sm text-slate-700">
                <Printer className="w-4 h-4 mr-1 text-emerald-600" />
                Utilities
              </div>
            </motion.div>

            {/* Green Space */}
            <motion.div 
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
              className="h-1/3 bg-green-50 rounded-lg p-3"
            >
              <div className="flex items-center text-sm text-slate-700">
                <Plants className="w-4 h-4 mr-1 text-green-600" />
                Green Space
              </div>
            </motion.div>
          </div>

          {/* Entrance */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="absolute left-0 top-1/2 -translate-y-1/2 bg-slate-100 rounded-r-lg p-2"
          >
            <DoorOpen className="w-4 h-4 text-slate-600" />
          </motion.div>
        </div>
      </div>

      {/* Metrics */}
      <div className="mt-4 grid grid-cols-3 gap-4">
        <div className="text-center">
          <div className="text-sm font-medium text-slate-600">Total Area</div>
          <div className="text-lg font-semibold text-slate-900">{squareFootage} sq ft</div>
        </div>
        <div className="text-center">
          <div className="text-sm font-medium text-slate-600">Capacity</div>
          <div className="text-lg font-semibold text-slate-900">{employeeCount} people</div>
        </div>
        <div className="text-center">
          <div className="text-sm font-medium text-slate-600">Space Ratio</div>
          <div className="text-lg font-semibold text-slate-900">
            {Math.round(squareFootage / employeeCount)} sq ft/person
          </div>
        </div>
      </div>
    </div>
  );
}
