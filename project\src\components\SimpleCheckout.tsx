import React, { useState } from 'react';
import { supabase } from '../lib/supabase/client';

interface SimpleCheckoutProps {
  amount: number;
  description?: string;
  customerEmail?: string;
}

export const SimpleCheckout: React.FC<SimpleCheckoutProps> = ({
  amount,
  description = "Payment",
  customerEmail
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleCheckout = async () => {
    setLoading(true);
    setError(null);

    try {
      // Call the checkout Edge Function
      const { data, error: functionError } = await supabase.functions.invoke('checkout', {
        body: {
          amount,
          description,
          customerEmail
        }
      });

      if (functionError) {
        throw new Error(functionError.message);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Square's hosted checkout page
      window.location.href = data.checkoutUrl;

    } catch (err) {
      console.error('Checkout error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Checkout</h2>
      
      <div className="mb-4">
        <p className="text-gray-600">Amount: <span className="font-semibold">${amount.toFixed(2)}</span></p>
        <p className="text-gray-600">Description: {description}</p>
        {customerEmail && (
          <p className="text-gray-600">Email: {customerEmail}</p>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <button
        onClick={handleCheckout}
        disabled={loading}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded transition-colors"
      >
        {loading ? 'Creating checkout...' : 'Pay Now'}
      </button>
    </div>
  );
};

export default SimpleCheckout;
