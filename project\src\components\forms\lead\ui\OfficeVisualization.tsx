import React from 'react';
import { Users, LayoutDashboard, Coffee, Bath, Monitor, Leaf } from 'lucide-react';

interface OfficeVisualizationProps {
  squareFootage: number;
  employeeCount: number;
}

export function OfficeVisualization({ squareFootage, employeeCount }: OfficeVisualizationProps) {
  const scale = Math.min(squareFootage / 5000, 1);
  
  return (
    <div className="mt-8">
      <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl border border-gray-200 shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
        <div className="p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <LayoutDashboard className="w-5 h-5 mr-2 text-green-600" />
            Office Layout Preview
          </h3>

          <div className="relative aspect-[16/9] bg-white rounded-xl overflow-hidden">
            {/* Dynamic Grid Background */}
            <div 
              className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 opacity-50" 
              style={{ 
                backgroundSize: `${scale * 20}px ${scale * 20}px`,
                backgroundImage: 'linear-gradient(to right, #f0f0f0 1px, transparent 1px), linear-gradient(to bottom, #f0f0f0 1px, transparent 1px)'
              }} 
            />

            {/* Main Office Area */}
            <div className="absolute inset-4 grid grid-cols-4 gap-4">
              {/* Work Area */}
              <div className="col-span-3 space-y-4">
                <div className="bg-green-50/80 backdrop-blur rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md group">
                  <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                    <Users className="w-4 h-4 mr-2 text-green-600" />
                    Work Area
                  </h4>
                  <div className="grid grid-cols-4 gap-3">
                    {Array.from({ length: Math.min(employeeCount, 16) }).map((_, i) => (
                      <div
                        key={i}
                        className="group/desk relative animate-fade-in"
                        style={{ 
                          animationDelay: `${i * 0.1}s`,
                          transform: `scale(${0.8 + (scale * 0.2)})`
                        }}
                      >
                        <div className="aspect-square bg-white rounded-lg border border-green-200 shadow-sm transition-all duration-300 group-hover/desk:shadow-md group-hover/desk:scale-105">
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover/desk:opacity-100 transition-opacity">
                            <Monitor className="w-4 h-4 text-green-600" />
                          </div>
                        </div>
                      </div>
                    ))}
                    {employeeCount > 16 && (
                      <div className="flex items-center justify-center">
                        <span className="text-sm text-gray-500">+{employeeCount - 16} more</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Collaboration Space */}
                <div className="bg-blue-50/80 backdrop-blur rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md group">
                  <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <Users className="w-4 h-4 mr-2 text-blue-600" />
                    Collaboration Space
                  </h4>
                  <div className="flex items-center space-x-2">
                    <div className="flex -space-x-2">
                      {Array.from({ length: Math.min(3, Math.ceil(employeeCount * 0.3)) }).map((_, i) => (
                        <div 
                          key={i}
                          className="w-6 h-6 rounded-full bg-blue-100 border-2 border-white flex items-center justify-center"
                        >
                          <Users className="w-3 h-3 text-blue-600" />
                        </div>
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      Capacity: {Math.ceil(employeeCount * 0.3)} people
                    </span>
                  </div>
                </div>
              </div>

              {/* Facilities Column */}
              <div className="space-y-4">
                {/* Break Room */}
                <div className="bg-yellow-50/80 backdrop-blur rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md group">
                  <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <Coffee className="w-4 h-4 mr-2 text-yellow-600" />
                    Break Room
                  </h4>
                  <div className="h-1 bg-yellow-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-yellow-400 transition-all duration-500"
                      style={{ width: `${Math.min(employeeCount * 5, 100)}%` }}
                    />
                  </div>
                </div>

                {/* Restrooms */}
                <div className="bg-purple-50/80 backdrop-blur rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md group">
                  <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <Bath className="w-4 h-4 mr-2 text-purple-600" />
                    Restrooms
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {Array.from({ length: Math.ceil(employeeCount / 20) }).map((_, i) => (
                      <div key={i} className="h-2 bg-purple-200 rounded-full" />
                    ))}
                  </div>
                </div>

                {/* Green Space */}
                <div className="bg-emerald-50/80 backdrop-blur rounded-lg p-4 shadow-sm transition-all duration-300 hover:shadow-md group">
                  <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <Leaf className="w-4 h-4 mr-2 text-emerald-600" />
                    Green Space
                  </h4>
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, Math.ceil(squareFootage / 1000)) }).map((_, i) => (
                      <Leaf 
                        key={i} 
                        className="w-3 h-3 text-emerald-500 animate-pulse-scale"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Info Overlays */}
            <div className="absolute bottom-4 right-4 flex space-x-3">
              <div className="bg-white/90 backdrop-blur rounded-full px-3 py-1.5 shadow-sm hover:shadow-md transition-all duration-300">
                <span className="text-sm font-medium text-gray-700">
                  {squareFootage.toLocaleString()} sq ft
                </span>
              </div>
              <div className="bg-white/90 backdrop-blur rounded-full px-3 py-1.5 shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-1.5">
                  <Users className="w-4 h-4 text-gray-600" />
                  <span className="text-sm font-medium text-gray-700">
                    {employeeCount}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Metrics */}
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg hover:shadow-sm transition-all duration-300">
              <div className="text-sm font-medium text-gray-700 mb-1">Space Efficiency</div>
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-green-500 transition-all duration-500"
                  style={{ width: `${Math.min((squareFootage / employeeCount) / 150 * 100, 100)}%` }}
                />
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg hover:shadow-sm transition-all duration-300">
              <div className="text-sm font-medium text-gray-700 mb-1">Capacity Utilization</div>
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-blue-500 transition-all duration-500"
                  style={{ width: `${(employeeCount / (squareFootage / 100)) * 100}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
