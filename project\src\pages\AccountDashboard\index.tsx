import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import { 
  Calendar, History, MessageSquare, User, LogOut, 
  Star, Plus, Compass, Sparkles, Wind, Home, X
} from 'lucide-react';
import { useAuth } from '../../lib/auth/AuthProvider';
import { Header } from '../../components/layout/Header';
import { AnimatedBackground } from '../../components/layout/AnimatedBackground';
import { AccountSettings } from './components/AccountSettings';
import { BookingsDisplay } from './components/BookingsDisplay';
import { Logo } from '../../components/ui/Logo';


type DashboardTab = 'bookings' | 'profile';

export function AccountDashboard() {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<DashboardTab>('bookings');
  const [newBookingData, setNewBookingData] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  useEffect(() => {
    // Check if we have a new booking from form submission
    if (location.state?.newBooking) {
      setNewBookingData(location.state.newBooking);
      // Set active tab to bookings if specified
      if (location.state?.showBookingsTab) {
        setActiveTab('bookings');
      }
      // Clear the state to prevent showing the notification again on refresh
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  if (!user) {
    return <LoginPrompt />;
  }

  return (
    <AnimatedBackground>
      <Header toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />
      <div className="relative min-h-screen font-sans text-gray-800">
        <AnimatePresence>
          {isSidebarOpen && (
            <Sidebar 
              user={user}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              signOut={signOut}
              setIsSidebarOpen={setIsSidebarOpen}
            />
          )}
        </AnimatePresence>
        <main className="flex-1 md:pl-24 flex flex-col min-h-screen pt-16 md:pt-20">
          <div className="flex-1 overflow-y-auto p-3 sm:p-4 md:p-6 no-scrollbar">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, scale: 0.98 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.98 }}
                transition={{ duration: 0.2 }}
              >
                {renderTabContent(activeTab, newBookingData)}
              </motion.div>
            </AnimatePresence>
          </div>
        </main>
      </div>
    </AnimatedBackground>
  );
}

// White Glass Login Prompt
function LoginPrompt() {
    return (
        <div className="min-h-screen bg-black flex items-center justify-center p-4">
            <video autoPlay loop muted playsInline className="fixed top-0 left-0 w-full h-full object-cover z-0 opacity-40">
                <source src="https://videos.pexels.com/video-files/4784260/4784260-hd_1920_1080_25fps.mp4" type="video/mp4" />
            </video>
            <div className="fixed inset-0 bg-black/60 z-10" />
            <motion.div
                className="relative max-w-sm w-full z-20"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, ease: "easeOut" }}
            >
                <div className="relative p-8 bg-white/10 backdrop-blur-2xl border border-white/20 rounded-3xl shadow-2xl shadow-black/50">
                    <div className="text-center">
                        <motion.div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-emerald-500/50">
                            <Logo className="w-8 h-8" />
                        </motion.div>
                        <h2 className="text-3xl font-bold text-white mb-2">Welcome Back</h2>
                        <p className="text-white/70 mb-8">Please sign in to continue.</p>
                        <motion.button
                            onClick={() => window.location.href = '/auth/login'}
                            className="w-full bg-white text-emerald-700 font-semibold py-3 rounded-xl transition-all shadow-lg shadow-white/20"
                            whileHover={{ scale: 1.02, y: -2, boxShadow: '0 0 25px rgba(255, 255, 255, 0.4)' }}
                            whileTap={{ scale: 0.98 }}
                        >
                            Sign In
                        </motion.button>
                    </div>
                </div>
            </motion.div>
        </div>
    );
}

// Mobile-Optimized Sidebar
function Sidebar({ user, activeTab, setActiveTab, signOut, setIsSidebarOpen }: {
  user: { email?: string };
  activeTab: DashboardTab;
  setActiveTab: (tab: DashboardTab) => void;
  signOut: () => void;
  setIsSidebarOpen: (isOpen: boolean) => void;
}) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const menuItems = [
    { id: 'bookings' as DashboardTab, icon: <Calendar size={isMobile ? 20 : 22} />, label: 'Bookings' },
  ];

  const sidebarVariants = {
    closed: { 
      width: isMobile ? '4rem' : '5rem', 
      transition: { type: 'spring', stiffness: 350, damping: 35 } 
    },
    open: { 
      width: isMobile ? '14rem' : '16rem', 
      transition: { type: 'spring', stiffness: 350, damping: 35 } 
    }
  };

  const navTextVariants = {
    closed: { opacity: 0, x: -15, transition: { duration: 0.2 } },
    open: { opacity: 1, x: 0, transition: { delay: 0.15, duration: 0.2 } }
  }

  return (
    <motion.aside
      variants={sidebarVariants}
      initial={{ x: '-100%' }}
      animate={{ x: 0 }}
      exit={{ x: '-100%' }}
      transition={{ type: 'spring', stiffness: 350, damping: 35 }}
      className={`fixed ${isMobile ? 'left-2 top-[25vh]' : 'left-4 top-[25vh]'} z-50 touch-manipulation`}
    >
             <div className={`${isMobile ? 'p-2' : 'p-3'} bg-gradient-to-br from-white/98 via-white/94 to-white/88 backdrop-blur-3xl border border-white/70 rounded-[2rem] shadow-xl shadow-black/20 flex flex-col items-center ring-1 ring-white/50 ring-inset before:absolute before:inset-0 before:rounded-[2rem] before:bg-gradient-to-br before:from-white/40 before:via-white/15 before:to-transparent before:pointer-events-none relative overflow-hidden`}>
        <button onClick={() => setIsSidebarOpen(false)} className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 z-20">
          <X size={24} />
        </button>
        
                 <motion.nav layout="position" className={`flex-1 ${isMobile ? 'space-y-1' : 'space-y-2'} w-full ${isMobile ? 'pt-4' : 'pt-8'} relative z-10`}>
          {menuItems.map((item) => (
            <motion.button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
                             className={`${isMobile ? 'h-10' : 'h-12'} flex items-center rounded-full transition-colors duration-200 overflow-hidden w-full group touch-manipulation ${ activeTab === item.id ? 'bg-gradient-to-br from-white/96 via-white/90 to-emerald-50/60 ring-1 ring-white/50' : 'hover:bg-white/80' }`}
              whileTap={{ scale: 0.95 }}
            >
                             <div className={`${isMobile ? 'w-10 h-10' : 'w-14 h-12'} flex-shrink-0 flex items-center justify-center ${activeTab === item.id ? 'text-white' : 'text-white/70 group-hover:text-gray-800'}`}>
                  {item.icon}
              </div>
                             <motion.span variants={navTextVariants} initial="closed" animate={isExpanded ? 'open' : 'closed'} className={`font-semibold ${isMobile ? 'text-xs' : 'text-sm'} ml-2 pr-4 ${activeTab === item.id ? 'text-white' : 'text-white group-hover:text-gray-800'}`}>{item.label}</motion.span>
            </motion.button>
          ))}
        </motion.nav>

                 <motion.div layout="position" className={`mt-auto space-y-2 w-full relative z-10 ${isMobile ? 'mb-2' : 'mb-4'}`}>
                       <motion.button 
               onClick={() => setActiveTab('profile')}
               className={`${isMobile ? 'h-10' : 'h-12'} flex items-center rounded-full hover:bg-white/80 transition-colors overflow-hidden w-full group touch-manipulation ${activeTab === 'profile' ? 'bg-gradient-to-br from-white/96 via-white/90 to-emerald-50/60 ring-1 ring-white/50' : ''}`}
               whileTap={{ scale: 0.95 }}
            >
               <div className={`${isMobile ? 'w-10 h-10' : 'w-14 h-12'} flex-shrink-0 flex items-center justify-center`}>
                   <div className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} rounded-full bg-emerald-100 border border-emerald-200 flex items-center justify-center font-semibold ${isMobile ? 'text-xs' : 'text-sm'} text-emerald-600`}>
                       {user.email?.charAt(0).toUpperCase()}
                   </div>
               </div>
               <motion.div variants={navTextVariants} initial="closed" animate={isExpanded ? 'open' : 'closed'} className="text-left ml-2 pr-4">
                   <p className={`${isMobile ? 'text-xs' : 'text-xs'} font-bold truncate max-w-[120px] ${activeTab === 'profile' ? 'text-white' : 'text-white group-hover:text-gray-800'}`}>{user.email}</p>
                   <p className={`${isMobile ? 'text-xs' : 'text-xs'} ${activeTab === 'profile' ? 'text-white/70' : 'text-white/70 group-hover:text-gray-600'}`}>View Profile</p>
               </motion.div>
            </motion.button>
                       <motion.button 
               onClick={signOut} 
               className={`${isMobile ? 'h-10' : 'h-12'} flex items-center rounded-full text-gray-600 hover:text-red-600 hover:bg-red-100 transition-colors overflow-hidden w-full group touch-manipulation`}
               whileTap={{ scale: 0.95 }}
            >
               <div className={`${isMobile ? 'w-10 h-10' : 'w-14 h-12'} flex-shrink-0 flex items-center justify-center text-gray-500 group-hover:text-red-500`}>
                   <LogOut size={isMobile ? 18 : 22} />
               </div>
               <motion.span variants={navTextVariants} initial="closed" animate={isExpanded ? 'open' : 'closed'} className={`font-semibold ${isMobile ? 'text-xs' : 'text-sm'} text-white group-hover:text-red-600 ml-2 pr-4`}>Sign Out</motion.span>
           </motion.button>
        </motion.div>
      </div>
    </motion.aside>
  );
}



// Tab Content Renderer
function renderTabContent(activeTab: DashboardTab, newBookingData: any) {
  switch (activeTab) {
    case 'bookings':
      return <BookingsDisplay newBooking={newBookingData} />;
    case 'profile':
      return <AccountSettings />;
    default:
      return <DashboardOverview />;
  }
}

const cardVariants = { 
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4, ease: 'easeOut' }}
};
const cardHover = { scale: 1.02, transition: { duration: 0.2 }};

// Unified crystal-glass style – brighter, clearer, and consistent across all cards
const cardBaseStyle = "backdrop-blur-3xl border border-white/60 rounded-3xl shadow-2xl shadow-emerald-900/5 ring-1 ring-white/40 ring-inset relative overflow-hidden";
// Brighter glass – mostly white with a faint emerald whisper
const cardBgStyle = "bg-gradient-to-br from-white/95 via-white/88 to-white/82";
// Compact cards – nearly pure white for maximum contrast
const smallCardBgStyle = "bg-gradient-to-br from-white/100 via-white/96 to-emerald-50/60";

// Dashboard Overview with Thumbtack-inspired additions
function DashboardOverview() {
    return (
      <div className="space-y-6 md:space-y-8">
        <motion.div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6" variants={{ visible: { transition: { staggerChildren: 0.05 } }}} initial="hidden" animate="visible">
          <StatCard title="Total Cleanings" value="24" icon={<Home className="text-emerald-300" />} />
          <StatCard title="Money Saved" value="$1,240" icon={<Plus className="text-emerald-300" />} />
          <StatCard title="Next Service" value="Tomorrow" icon={<Calendar className="text-emerald-300" />} />
          <StatCard title="Your Rating" value="4.9" icon={<Star className="text-emerald-300" />} />
        </motion.div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          <div className="lg:col-span-2 space-y-4 md:space-y-6">
            <NextCleaningCard />
            <ExploreServicesCard />
          </div>
          <div className="space-y-4 md:space-y-6">
            <LeaveReviewCard />
            <QuickActionsCard />
          </div>
        </div>
      </div>
    );
}

// Stat Card for the top row
function StatCard({ title, value, icon }: { title: string, value: string, icon: React.ReactNode }) {
    return (
        <motion.div variants={cardVariants} whileHover={cardHover} className={`p-3 md:p-4 flex items-center gap-3 md:gap-4 ${cardBaseStyle} ${smallCardBgStyle}`}>
            <div className="text-emerald-700 bg-gradient-to-br from-white/95 to-emerald-50/90 p-2 md:p-3 rounded-lg border border-white/50 relative z-10">
                {icon}
            </div>
            <div className="relative z-10">
                <p className="text-xs md:text-sm font-medium text-gray-600">{title}</p>
                <p className="text-lg md:text-xl font-bold text-gray-900">{value}</p>
            </div>
        </motion.div>
    );
}

// Next Cleaning Card (previously existed, kept for consistency)
function NextCleaningCard() {
    return (
        <motion.div variants={cardVariants} className={`p-4 md:p-6 ${cardBaseStyle} ${cardBgStyle}`}>
            <div className="flex justify-between items-start relative z-10">
                <div className="flex-1 min-w-0">
                    <h2 className="text-lg md:text-2xl font-bold text-gray-800">Your next cleaning is set!</h2>
                    <p className="text-sm md:text-base text-gray-600/90 mt-1">Standard Home Cleaning · Tomorrow at 2:00 PM</p>
                </div>
                <div className="text-right flex-shrink-0 ml-3 md:ml-4">
                    <p className="text-xs uppercase font-semibold text-gray-500/90">Cleaner</p>
                    <p className="text-sm md:text-base font-semibold text-gray-800">John S.</p>
                </div>
            </div>
            <div className="mt-3 md:mt-4 pt-3 md:pt-4 border-t border-white/40 flex justify-end relative z-10">
                <button className="text-xs md:text-sm font-semibold text-emerald-700 hover:text-emerald-800 transition-colors px-3 md:px-4 py-2 bg-white/50 backdrop-blur-sm rounded-xl border border-white/60 touch-manipulation">Manage Booking</button>
            </div>
        </motion.div>
    );
}

// New component to prompt user for a review
function LeaveReviewCard() {
    return (
        <motion.div variants={cardVariants} whileHover={cardHover} className={`p-4 md:p-6 text-center ${cardBaseStyle} ${cardBgStyle}`}>
            <h3 className="text-base md:text-lg font-bold text-gray-800 mb-2 relative z-10">How was your last cleaning?</h3>
            <p className="text-xs md:text-sm text-gray-700 mb-3 md:mb-4 relative z-10">Your feedback helps us improve.</p>
            <div className="flex justify-center gap-1 mb-3 md:mb-4 relative z-10">
                {[...Array(5)].map((_, i) => <Star key={i} className="w-5 h-5 md:w-6 md:h-6 text-gray-400 hover:text-yellow-500 transition-colors cursor-pointer touch-manipulation" />)}
            </div>
            <button className="w-full py-2 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-emerald-800 transition-all shadow-lg backdrop-blur-sm relative z-10 text-sm md:text-base touch-manipulation">Submit Review</button>
        </motion.div>
    );
}

// Quick Actions Card (individual button)
function QuickActionCard({ title, icon }: { title: string, icon: React.ReactNode }) {
    return (
        <motion.div whileHover={{ y: -2, scale: 1.03, boxShadow: '0 0 25px rgba(16,185,129,0.15)' }} className={`flex items-center p-3 md:p-4 transition-all duration-300 cursor-pointer rounded-2xl ${cardBaseStyle} bg-white/95 touch-manipulation min-h-[60px] md:min-h-[70px]`}>
            <div className="text-emerald-600 bg-white p-1.5 md:p-2 rounded-lg border border-emerald-100 shadow-sm relative z-10">{icon}</div>
            <span className="ml-3 md:ml-4 font-semibold text-gray-800 text-xs md:text-sm relative z-10 leading-tight">{title}</span>
        </motion.div>
    )
}

// Quick Actions Card (Container)
function QuickActionsCard() {
    return (
        <motion.div variants={cardVariants} whileHover={cardHover} className={`p-4 md:p-6 h-full ${cardBaseStyle} ${cardBgStyle}`}>
            <h3 className="text-base md:text-lg font-bold text-gray-800 mb-3 md:mb-4 relative z-10">Quick Actions</h3>
            <div className="space-y-2 md:space-y-3 relative z-10">
                <QuickActionCard title="Book a New Cleaning" icon={<Calendar />} />
                <QuickActionCard title="View Cleaning History" icon={<History />} />
                <QuickActionCard title="Manage Account" icon={<User />} />
            </div>
        </motion.div>
    );
}

// Renamed from ServiceBookingCard for clarity
function ExploreServicesCard() {
    return (
        <motion.div variants={cardVariants} className={`p-4 md:p-6 h-full ${cardBaseStyle} ${cardBgStyle}`}>
            <div className="flex items-center gap-2 md:gap-3 mb-3 md:mb-4 relative z-10">
                <Compass className="text-emerald-700" size={18}/>
                <h3 className="text-base md:text-lg font-bold text-gray-800">Explore Other Services</h3>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-4 relative z-10">
                <ServiceOption title="Deep Cleaning" icon={<Sparkles size={20}/>} />
                <ServiceOption title="Window Cleaning" icon={<Wind size={20}/>} />
                <ServiceOption title="Move Out" icon={<Home size={20}/>} />
            </div>
        </motion.div>
    );
}
  
function ServiceOption({ title, icon }: { title: string; icon: React.ReactNode; }) {
    return (
        <motion.button 
            whileHover={{ y: -3, boxShadow: '0 0 25px rgba(16, 185, 129, 0.2)' }} 
            className={`p-4 md:p-6 space-y-2 md:space-y-3 flex flex-col items-center justify-center rounded-2xl transition-all ${cardBaseStyle} bg-white/96 hover:shadow-emerald-200/30 touch-manipulation min-h-[80px] md:min-h-[120px]`}
        >
            <div className="text-emerald-600 bg-white p-2 md:p-3 rounded-lg border border-emerald-100 shadow-sm relative z-10">{icon}</div>
            <p className="font-semibold text-gray-800 text-xs md:text-sm pt-1 relative z-10 text-center leading-tight">{title}</p>
        </motion.button>
    );
}


