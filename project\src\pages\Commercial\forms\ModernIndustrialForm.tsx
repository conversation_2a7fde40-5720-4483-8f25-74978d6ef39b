import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Factory, Truck, Shield, CheckCircle, AlertCircle, HardHat, ArrowRight
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { AnimatedBackground } from '../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../lib/auth/AuthProvider';

interface IndustrialFormData {
  servicePackage: string;
  facilityType: string;
  industryType: string;
  facilitySize: number;
  hazardousMaterials: string[];
  safetyRequirements: string[];
  accessRestrictions: string;
  operatingHours: string;
  facilityAddress: string;
  serviceFrequency: string;
  preferredTime: string;
  priorityAreas: string[];
  additionalServices: string[];
  specialInstructions: string;
  emergencyContact: string;
  complianceStandards: string[];
  // Contact information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName: string;
  jobTitle: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  facilityAddress?: string;
  facilityType?: string;
  industryType?: string;
  facilitySize?: string;
  serviceFrequency?: string;
  preferredTime?: string;
  servicePackage?: string;
}

const steps = [
  { id: 1, name: 'Service Type' },
  { id: 2, name: 'Facility Details' },
  { id: 3, name: 'Safety & Compliance' },
  { id: 4, name: 'Service Schedule' },
  { id: 5, name: 'Additional Services' },
  { id: 6, name: 'Contact' },
];

const servicePackages = [
  { id: 'standard', name: 'Standard Industrial Package', description: 'Basic cleaning for manufacturing facilities', icon: <Factory /> },
  { id: 'heavy-duty', name: 'Heavy-Duty Industrial', description: 'Deep cleaning for heavy manufacturing', icon: <Truck /> },
  { id: 'hazmat', name: 'Hazmat Specialized', description: 'Specialized cleaning for hazardous environments', icon: <Shield /> },
  { id: 'warehouse', name: 'Warehouse Package', description: 'Large-scale warehouse and distribution cleaning', icon: <HardHat /> },
];

const facilityTypes = [
  { id: 'manufacturing', name: 'Manufacturing Plant' },
  { id: 'warehouse', name: 'Warehouse/Distribution' },
  { id: 'chemical', name: 'Chemical Processing' },
  { id: 'food-processing', name: 'Food Processing' },
  { id: 'automotive', name: 'Automotive Assembly' },
  { id: 'pharmaceutical', name: 'Pharmaceutical' },
];

const industryTypes = [
  { id: 'automotive', name: 'Automotive' },
  { id: 'food-beverage', name: 'Food & Beverage' },
  { id: 'chemical', name: 'Chemical' },
  { id: 'pharmaceutical', name: 'Pharmaceutical' },
  { id: 'manufacturing', name: 'General Manufacturing' },
  { id: 'logistics', name: 'Logistics & Distribution' },
];

// Enhanced Email Validation
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Enhanced Phone Number Formatting
const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

// Phone Number Validation
const validatePhoneNumber = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length === 10 || (cleaned.length === 11 && cleaned[0] === '1');
};

// Address Validation
const validateAddress = (address: string): boolean => {
  return address.trim().length >= 10 && /\d/.test(address) && /[a-zA-Z]/.test(address);
};

const ModernIndustrialForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<Partial<IndustrialFormData>>({
    servicePackage: 'standard',
    hazardousMaterials: [],
    safetyRequirements: [],
    priorityAreas: [],
    additionalServices: [],
    complianceStandards: [],
    facilitySize: 10000,
  });

  // Save form data to localStorage on changes
  useEffect(() => {
    localStorage.setItem('industrialFormData', JSON.stringify(formData));
  }, [formData]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('industrialFormData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Enhanced form field validation
  const validateField = (fieldName: keyof FormErrors, value: string): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value) return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
        if (!value || value.trim().length < 2) return 'First name must be at least 2 characters';
        break;
      case 'lastName':
        if (!value || value.trim().length < 2) return 'Last name must be at least 2 characters';
        break;
      case 'companyName':
        if (!value || value.trim().length < 2) return 'Company name is required';
        break;
      case 'facilityAddress':
        if (!value) return 'Facility address is required';
        if (!validateAddress(value)) return 'Please enter a complete address with street number and name';
        break;
      case 'facilityType':
        if (!value) return 'Please select a facility type';
        break;
      case 'industryType':
        if (!value) return 'Please select an industry type';
        break;
      case 'facilitySize':
        if (!value || parseInt(value) < 1000) return 'Facility size must be at least 1000 sq ft';
        break;
      case 'serviceFrequency':
        if (!value) return 'Please select a service frequency';
        break;
      case 'preferredTime':
        if (!value) return 'Please select a preferred time';
        break;
    }
    return undefined;
  };

  // Enhanced handleNext with validation
  const handleNext = () => {
    if (currentStep < steps.length) {
      setIsLoading(true);
      
      // Validate current step
      const errors: FormErrors = {};
      
      if (currentStep === 1) {
        if (!formData.servicePackage) {
          setFormErrors({ servicePackage: 'Please select a service package' });
          setIsLoading(false);
          return;
        }
      } else if (currentStep === 2) {
        errors.facilityType = validateField('facilityType', formData.facilityType || '');
        errors.industryType = validateField('industryType', formData.industryType || '');
        errors.facilitySize = validateField('facilitySize', formData.facilitySize?.toString() || '');
        errors.facilityAddress = validateField('facilityAddress', formData.facilityAddress || '');
      } else if (currentStep === 4) {
        errors.serviceFrequency = validateField('serviceFrequency', formData.serviceFrequency || '');
        errors.preferredTime = validateField('preferredTime', formData.preferredTime || '');
      } else if (currentStep === 6) {
        errors.firstName = validateField('firstName', formData.firstName || '');
        errors.lastName = validateField('lastName', formData.lastName || '');
        errors.email = validateField('email', formData.email || '');
        errors.phone = validateField('phone', formData.phone || '');
        errors.companyName = validateField('companyName', formData.companyName || '');
      }
      
      // Filter out undefined errors
      const filteredErrors = Object.fromEntries(
        Object.entries(errors).filter((entry) => entry[1] !== undefined)
      );
      
      setFormErrors(filteredErrors);
      
      if (Object.keys(filteredErrors).length === 0) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1);
          setIsLoading(false);
        }, 500);
      } else {
        setIsLoading(false);
        // Scroll to first error
        setTimeout(() => {
          const firstErrorElement = document.querySelector('.text-red-400');
          if (firstErrorElement) {
            firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.servicePackage && !formErrors.servicePackage;
      case 2:
        return formData.facilityType && 
               formData.industryType && 
               formData.facilitySize && 
               formData.facilityAddress &&
               !formErrors.facilityType &&
               !formErrors.industryType &&
               !formErrors.facilitySize &&
               !formErrors.facilityAddress;
      case 4:
        return formData.serviceFrequency && 
               formData.preferredTime &&
               !formErrors.serviceFrequency &&
               !formErrors.preferredTime;
      case 6:
        return formData.firstName && 
               formData.lastName && 
               formData.email && 
               formData.phone && 
               formData.companyName &&
               !formErrors.firstName &&
               !formErrors.lastName &&
               !formErrors.email &&
               !formErrors.phone &&
               !formErrors.companyName;
      default:
        return true;
    }
  };

  // Enhanced input handlers with validation
  const handleEmailChange = (value: string) => {
    setFormData({...formData, email: value});
    const error = validateField('email', value);
    setFormErrors(prev => ({ ...prev, email: error }));
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    setFormData({...formData, phone: formatted});
    const error = validateField('phone', value);
    setFormErrors(prev => ({ ...prev, phone: error }));
  };

  const handleNameChange = (field: 'firstName' | 'lastName', value: string) => {
    setFormData({...formData, [field]: value});
    const error = validateField(field, value);
    setFormErrors(prev => ({ ...prev, [field]: error }));
  };

  const handleCompanyNameChange = (value: string) => {
    setFormData({...formData, companyName: value});
    const error = validateField('companyName', value);
    setFormErrors(prev => ({ ...prev, companyName: error }));
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate all contact fields before submission
      const contactErrors: FormErrors = {};
      contactErrors.firstName = validateField('firstName', formData.firstName || '');
      contactErrors.lastName = validateField('lastName', formData.lastName || '');
      contactErrors.email = validateField('email', formData.email || '');
      contactErrors.phone = validateField('phone', formData.phone || '');
      contactErrors.companyName = validateField('companyName', formData.companyName || '');
      
      const filteredContactErrors = Object.fromEntries(
        Object.entries(contactErrors).filter((entry) => entry[1] !== undefined)
      );
      
      if (Object.keys(filteredContactErrors).length > 0) {
        setFormErrors(filteredContactErrors);
        setIsLoading(false);
        return;
      }

      // Submit form data for estimate scheduling
      const estimateData = {
        ...formData,
        serviceType: 'industrial-cleaning',
        requestType: 'estimate',
        submittedAt: new Date().toISOString(),
        contactInfo: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle
        },
        facilityInfo: {
          address: formData.facilityAddress,
          facilityType: formData.facilityType,
          industryType: formData.industryType,
          facilitySize: formData.facilitySize
        }
      };
      
      console.log('Scheduling industrial estimate with data:', estimateData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Clear form data from localStorage after successful submission
      localStorage.removeItem('industrialFormData');
      
      // Show success popup instead of navigating immediately
      setShowSuccessPopup(true);
      
    } catch (error) {
      console.error('Error scheduling industrial estimate:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClosePopup = () => {
    setShowSuccessPopup(false);
    if (user) {
      navigate('/accountdashboard');
    } else {
      localStorage.setItem('redirectAfterLogin', '/accountdashboard');
      navigate('/auth/login');
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress Bar */}
          <div className="mb-12">
            {/* Step Counter */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">
                  Step {currentStep} of {steps.length}
                </span>
              </div>
            </div>
            
            {/* Progress Line */}
            <div className="relative">
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"
                  initial={{ width: '16.67%' }}
                  animate={{ width: `${(currentStep / steps.length) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                />
              </div>
              
              {/* Step Dots */}
              <div className="absolute inset-0 flex items-center justify-between px-1">
                {steps.map((step) => (
                  <motion.div
                    key={step.id}
                    className={`w-4 h-4 rounded-full border-2 ${
                      currentStep >= step.id 
                        ? 'bg-emerald-400 border-emerald-400' 
                        : 'bg-white/10 border-white/30'
                    }`}
                    animate={{
                      scale: currentStep === step.id ? 1.2 : 1,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </div>
            
            {/* Current Step Label */}
            <div className="text-center mt-4">
              <h1 className="text-xl font-semibold text-white">
                {steps[currentStep - 1]?.name}
              </h1>
            </div>
          </div>

          {/* Form Content */}
          <motion.div 
            className="rounded-3xl shadow-2xl p-8 md:p-12"
            style={{
              background: 'rgba(255, 255, 255, 0.08)',
              border: '1px solid rgba(255, 255, 255, 0.15)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              {/* Step 1: Service Package Selection */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-white mb-3">Choose Your Industrial Package</h2>
                    <p className="text-gray-300 text-lg">Select the industrial cleaning package that meets your facility needs</p>
                    {formErrors.servicePackage && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center justify-center mt-4 text-red-400"
                      >
                        <AlertCircle className="w-5 h-5 mr-2" />
                        <span>{formErrors.servicePackage}</span>
                      </motion.div>
                    )}
                  </div>

                  <div className="grid md:grid-cols-2 gap-6 mb-8">
                    {servicePackages.map((pkg) => (
                      <motion.div
                        key={pkg.id}
                        className={`group relative p-8 rounded-2xl cursor-pointer transition-all duration-300 ${
                          formData.servicePackage === pkg.id 
                            ? 'bg-gradient-to-br from-emerald-500/20 to-green-600/20 border-2 border-emerald-400 shadow-lg shadow-emerald-400/20' 
                            : 'bg-white/5 border-2 border-white/10 hover:border-white/30 hover:bg-white/10'
                        }`}
                        onClick={() => {
                          setFormData({ ...formData, servicePackage: pkg.id });
                          // Clear any service package error
                          if (formErrors.servicePackage) {
                            setFormErrors(prev => ({ ...prev, servicePackage: undefined }));
                          }
                        }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {formData.servicePackage === pkg.id && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-4 right-4 w-6 h-6 bg-emerald-400 rounded-full flex items-center justify-center"
                          >
                            <div className="w-3 h-3 bg-white rounded-full" />
                          </motion.div>
                        )}
                        
                        <div className="flex items-center mb-4">
                          <div className="text-emerald-400 mr-4">
                            {React.cloneElement(pkg.icon as React.ReactElement, { className: "w-8 h-8" })}
                          </div>
                          <h3 className="text-xl font-bold text-white">{pkg.name}</h3>
                        </div>
                        <p className="text-gray-300">{pkg.description}</p>
                      </motion.div>
                    ))}
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={() => navigate('/')}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Facility Details */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Facility Details</h2>
                  <p className="text-gray-300 mb-6">Tell us about your industrial facility</p>

                  <div className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-white font-medium mb-2">
                          Facility Type <span className="text-red-400">*</span>
                        </label>
                        <select 
                          className={`w-full bg-white/10 p-3 rounded-lg border text-white focus:outline-none transition-colors duration-300 ${
                            formErrors.facilityType 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-white/20 focus:border-emerald-400'
                          }`}
                          value={formData.facilityType || ''}
                          onChange={(e) => {
                            setFormData({...formData, facilityType: e.target.value});
                            const error = validateField('facilityType', e.target.value);
                            setFormErrors(prev => ({ ...prev, facilityType: error }));
                          }}
                        >
                          <option value="">Select facility type</option>
                          {facilityTypes.map(type => (
                            <option key={type.id} value={type.id} style={{ color: 'black' }}>{type.name}</option>
                          ))}
                        </select>
                        {formErrors.facilityType && (
                          <div className="flex items-center mt-1 text-red-400 text-sm">
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.facilityType}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-white font-medium mb-2">
                          Industry Type <span className="text-red-400">*</span>
                        </label>
                        <select 
                          className={`w-full bg-white/10 p-3 rounded-lg border text-white focus:outline-none transition-colors duration-300 ${
                            formErrors.industryType 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-white/20 focus:border-emerald-400'
                          }`}
                          value={formData.industryType || ''}
                          onChange={(e) => {
                            setFormData({...formData, industryType: e.target.value});
                            const error = validateField('industryType', e.target.value);
                            setFormErrors(prev => ({ ...prev, industryType: error }));
                          }}
                        >
                          <option value="">Select industry</option>
                          {industryTypes.map(industry => (
                            <option key={industry.id} value={industry.id} style={{ color: 'black' }}>{industry.name}</option>
                          ))}
                        </select>
                        {formErrors.industryType && (
                          <div className="flex items-center mt-1 text-red-400 text-sm">
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.industryType}
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Facility Address <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="e.g., 123 Industrial Blvd, Manufacturing District"
                        className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                          formErrors.facilityAddress 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-white/20 focus:border-emerald-400'
                        }`}
                        value={formData.facilityAddress || ''}
                        onChange={(e) => {
                          setFormData({...formData, facilityAddress: e.target.value});
                          const error = validateField('facilityAddress', e.target.value);
                          setFormErrors(prev => ({ ...prev, facilityAddress: error }));
                        }}
                      />
                      {formErrors.facilityAddress && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.facilityAddress}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Facility Size (sq ft) <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="number"
                        min="1000"
                        placeholder="e.g., 50000"
                        className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                          formErrors.facilitySize 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-white/20 focus:border-emerald-400'
                        }`}
                        value={formData.facilitySize || ''}
                        onChange={(e) => {
                          setFormData({...formData, facilitySize: parseInt(e.target.value) || 0});
                          const error = validateField('facilitySize', e.target.value);
                          setFormErrors(prev => ({ ...prev, facilitySize: error }));
                        }}
                      />
                      {formErrors.facilitySize && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.facilitySize}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Validation Summary for Step 2 */}
                  {(formErrors.facilityType || formErrors.industryType || formErrors.facilitySize || formErrors.facilityAddress) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-lg p-4 mt-6"
                    >
                      <h4 className="text-red-400 font-medium mb-2">Please complete the following:</h4>
                      <ul className="text-red-300 text-sm space-y-1">
                        {formErrors.facilityType && <li>• {formErrors.facilityType}</li>}
                        {formErrors.industryType && <li>• {formErrors.industryType}</li>}
                        {formErrors.facilitySize && <li>• {formErrors.facilitySize}</li>}
                        {formErrors.facilityAddress && <li>• {formErrors.facilityAddress}</li>}
                      </ul>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Safety & Compliance */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Safety & Compliance</h2>
                  <p className="text-gray-300 mb-6">Industrial safety requirements and compliance standards</p>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-white font-medium mb-4">
                        Hazardous Materials Present (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Chemicals', 'Flammable Materials', 'Heavy Metals', 'Radioactive Materials', 'Biological Hazards', 'None'].map((material) => (
                          <motion.button
                            key={material}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.hazardousMaterials?.includes(material)
                                ? 'border-emerald-400 bg-emerald-500/20 text-white shadow-lg shadow-emerald-400/20'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40 hover:bg-white/10'
                            }`}
                            onClick={() => {
                              const current = formData.hazardousMaterials || [];
                              if (current.includes(material)) {
                                setFormData({...formData, hazardousMaterials: current.filter(h => h !== material)});
                              } else {
                                setFormData({...formData, hazardousMaterials: [...current, material]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.hazardousMaterials?.includes(material) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{material}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-4">
                        Required Safety Certifications (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['OSHA 10', 'OSHA 30', 'HAZWOPER', 'Confined Space', 'Lockout/Tagout', 'None Required'].map((cert) => (
                          <motion.button
                            key={cert}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.safetyRequirements?.includes(cert)
                                ? 'border-emerald-400 bg-emerald-500/20 text-white shadow-lg shadow-emerald-400/20'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40 hover:bg-white/10'
                            }`}
                            onClick={() => {
                              const current = formData.safetyRequirements || [];
                              if (current.includes(cert)) {
                                setFormData({...formData, safetyRequirements: current.filter(s => s !== cert)});
                              } else {
                                setFormData({...formData, safetyRequirements: [...current, cert]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.safetyRequirements?.includes(cert) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{cert}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Access Restrictions or Special Requirements
                      </label>
                      <textarea
                        placeholder="e.g., Security clearance required, restricted hours, escort needed..."
                        className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 transition-colors duration-300 h-24"
                        value={formData.accessRestrictions || ''}
                        onChange={(e) => setFormData({...formData, accessRestrictions: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      <>
                        Continue <ArrowRight className="ml-2 w-5 h-5" />
                      </>
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Service Schedule */}
              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Service Schedule</h2>
                  <p className="text-gray-300 mb-6">When do you need industrial cleaning services?</p>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-white font-medium mb-2">
                        Service Frequency <span className="text-red-400">*</span>
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['One-time', 'Weekly', 'Bi-weekly', 'Monthly', 'Quarterly', 'As Needed'].map((freq) => (
                          <button
                            key={freq}
                            type="button"
                            className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                              formData.serviceFrequency === freq
                                ? 'border-emerald-400 bg-emerald-500/20 text-white'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                            }`}
                            onClick={() => {
                              setFormData({...formData, serviceFrequency: freq});
                              const error = validateField('serviceFrequency', freq);
                              setFormErrors(prev => ({ ...prev, serviceFrequency: error }));
                            }}
                          >
                            {freq}
                          </button>
                        ))}
                      </div>
                      {formErrors.serviceFrequency && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.serviceFrequency}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Preferred Time <span className="text-red-400">*</span>
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Night Shift (11PM-7AM)', 'Day Shift (7AM-3PM)', 'Evening Shift (3PM-11PM)', 'Weekend Only', 'Shutdown Periods', 'Flexible'].map((time) => (
                          <button
                            key={time}
                            type="button"
                            className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                              formData.preferredTime === time
                                ? 'border-emerald-400 bg-emerald-500/20 text-white'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                            }`}
                            onClick={() => {
                              setFormData({...formData, preferredTime: time});
                              const error = validateField('preferredTime', time);
                              setFormErrors(prev => ({ ...prev, preferredTime: error }));
                            }}
                          >
                            {time}
                          </button>
                        ))}
                      </div>
                      {formErrors.preferredTime && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.preferredTime}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Operating Hours
                      </label>
                      <input
                        type="text"
                        placeholder="e.g., 24/7, Monday-Friday 6AM-6PM, Weekends Only"
                        className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 transition-colors duration-300"
                        value={formData.operatingHours || ''}
                        onChange={(e) => setFormData({...formData, operatingHours: e.target.value})}
                      />
                    </div>
                  </div>

                  {/* Validation Summary for Step 4 */}
                  {(formErrors.serviceFrequency || formErrors.preferredTime) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-lg p-4 mt-6"
                    >
                      <h4 className="text-red-400 font-medium mb-2">Please complete the following:</h4>
                      <ul className="text-red-300 text-sm space-y-1">
                        {formErrors.serviceFrequency && <li>• {formErrors.serviceFrequency}</li>}
                        {formErrors.preferredTime && <li>• {formErrors.preferredTime}</li>}
                      </ul>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      <>
                        Continue <ArrowRight className="ml-2 w-5 h-5" />
                      </>
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 5: Additional Services */}
              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Additional Services</h2>
                  <p className="text-gray-300 mb-6">Optional services to enhance your industrial cleaning</p>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-white font-medium mb-4">
                        Priority Cleaning Areas (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Production Floor', 'Administrative Offices', 'Restrooms', 'Break Rooms', 'Loading Docks', 'Machinery Areas', 'Storage Areas', 'Control Rooms'].map((area) => (
                          <motion.button
                            key={area}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.priorityAreas?.includes(area)
                                ? 'border-emerald-400 bg-emerald-500/20 text-white shadow-lg shadow-emerald-400/20'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40 hover:bg-white/10'
                            }`}
                            onClick={() => {
                              const current = formData.priorityAreas || [];
                              if (current.includes(area)) {
                                setFormData({...formData, priorityAreas: current.filter(p => p !== area)});
                              } else {
                                setFormData({...formData, priorityAreas: [...current, area]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.priorityAreas?.includes(area) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{area}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-4">
                        Additional Services (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Deep Degreasing', 'Equipment Cleaning', 'Pressure Washing', 'Window Cleaning', 'Waste Removal', 'Disinfection Services', 'Carpet Cleaning', 'Floor Stripping/Waxing'].map((service) => (
                          <motion.button
                            key={service}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.additionalServices?.includes(service)
                                ? 'border-emerald-400 bg-emerald-500/20 text-white shadow-lg shadow-emerald-400/20'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40 hover:bg-white/10'
                            }`}
                            onClick={() => {
                              const current = formData.additionalServices || [];
                              if (current.includes(service)) {
                                setFormData({...formData, additionalServices: current.filter(s => s !== service)});
                              } else {
                                setFormData({...formData, additionalServices: [...current, service]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.additionalServices?.includes(service) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{service}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Special Instructions or Requirements
                      </label>
                      <textarea
                        placeholder="Any specific cleaning protocols, equipment to avoid, safety procedures..."
                        className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 transition-colors duration-300 h-32"
                        value={formData.specialInstructions || ''}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Emergency Contact
                      </label>
                      <input
                        type="text"
                        placeholder="Name and phone number for emergencies"
                        className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 transition-colors duration-300"
                        value={formData.emergencyContact || ''}
                        onChange={(e) => setFormData({...formData, emergencyContact: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      <>
                        Continue <ArrowRight className="ml-2 w-5 h-5" />
                      </>
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 6: Contact Information */}
              {currentStep === 6 && (
                <motion.div
                  key="step6"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
                  <p className="text-gray-300 mb-6">Let's get your contact details to finalize your industrial estimate.</p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-white font-medium mb-2">
                        First Name <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="John"
                        className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                          formErrors.firstName 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-white/20 focus:border-emerald-400'
                        }`}
                        value={formData.firstName || ''}
                        onChange={(e) => handleNameChange('firstName', e.target.value)}
                      />
                      {formErrors.firstName && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.firstName}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Last Name <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="Smith"
                        className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                          formErrors.lastName 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-white/20 focus:border-emerald-400'
                        }`}
                        value={formData.lastName || ''}
                        onChange={(e) => handleNameChange('lastName', e.target.value)}
                      />
                      {formErrors.lastName && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.lastName}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Email Address <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                          formErrors.email 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-white/20 focus:border-emerald-400'
                        }`}
                        value={formData.email || ''}
                        onChange={(e) => handleEmailChange(e.target.value)}
                      />
                      {formErrors.email && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.email}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Phone Number <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="tel"
                        placeholder="(*************"
                        className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                          formErrors.phone 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-white/20 focus:border-emerald-400'
                        }`}
                        value={formData.phone || ''}
                        onChange={(e) => handlePhoneChange(e.target.value)}
                      />
                      {formErrors.phone && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.phone}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Company Name <span className="text-red-400">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="ABC Manufacturing"
                        className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                          formErrors.companyName 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-white/20 focus:border-emerald-400'
                        }`}
                        value={formData.companyName || ''}
                        onChange={(e) => handleCompanyNameChange(e.target.value)}
                      />
                      {formErrors.companyName && (
                        <div className="flex items-center mt-1 text-red-400 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.companyName}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Job Title
                      </label>
                      <input
                        type="text"
                        placeholder="Facilities Manager"
                        className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 transition-colors duration-300"
                        value={formData.jobTitle || ''}
                        onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                      />
                    </div>
                  </div>

                  {/* Validation Summary for Step 6 */}
                  {(formErrors.firstName || formErrors.lastName || formErrors.email || formErrors.phone || formErrors.companyName) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-lg p-4 mt-6"
                    >
                      <h4 className="text-red-400 font-medium mb-2">Please complete the following:</h4>
                      <ul className="text-red-300 text-sm space-y-1">
                        {formErrors.firstName && <li>• {formErrors.firstName}</li>}
                        {formErrors.lastName && <li>• {formErrors.lastName}</li>}
                        {formErrors.email && <li>• {formErrors.email}</li>}
                        {formErrors.phone && <li>• {formErrors.phone}</li>}
                        {formErrors.companyName && <li>• {formErrors.companyName}</li>}
                      </ul>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        'Request Industrial Estimate'
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Success Popup */}
      {showSuccessPopup && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="rounded-3xl shadow-2xl p-8 max-w-md w-full mx-4"
            onClick={(e) => e.stopPropagation()}
            style={{
              background: 'rgba(255, 255, 255, 0.12)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(20px)',
            }}
          >
            <div className="text-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, type: 'spring', bounce: 0.5 }}
                className="w-16 h-16 bg-emerald-400 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <CheckCircle className="w-8 h-8 text-white" />
              </motion.div>
              
              <h3 className="text-2xl font-bold text-white mb-2">
                Industrial Estimate Requested!
              </h3>
              
              <p className="text-gray-300 mb-6">
                {user 
                  ? "Your request has been submitted. We'll contact you within 24 hours to schedule your facility assessment."
                  : "Please log in to track your request and access your dashboard."
                }
              </p>
              
              <Button
                onClick={handleClosePopup}
                className="w-full px-6 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
              >
                {user ? 'View Your Requests' : 'Login to Continue'}
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatedBackground>
  );
};

export default ModernIndustrialForm; 

