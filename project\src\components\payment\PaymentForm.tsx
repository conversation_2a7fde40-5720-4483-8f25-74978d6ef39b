import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { CreditCard, DollarSign, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { Button } from '../ui/Button';
import { processResidentialPayment, isSquareConfigured } from '../../lib/api/paymentService';
import { useAuth } from '../../lib/auth/AuthProvider';
import { ErrorHandler } from '../../lib/utils/errorHandler';

interface PaymentFormProps {
  amount: number;
  description: string;
  customerEmail?: string;
  formData: any;
  onSuccess: (paymentLink: string) => void;
  onError: (error: Error) => void;
}

export function PaymentForm({ 
  amount, 
  description, 
  customerEmail, 
  formData,
  onSuccess, 
  onError 
}: PaymentFormProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRIES = 3;

  const handleCreatePayment = async () => {
    // Reset error state
    setError(null);

    // Validate amount before proceeding
    if (!amount || amount <= 0) {
      const errorMessage = 'Invalid payment amount';
      setError(errorMessage);
      onError(new Error(errorMessage));
      return;
    }

    // Validate user is logged in
    if (!user) {
      const errorMessage = 'You must be logged in to make a payment';
      setError(errorMessage);
      onError(new Error(errorMessage));
      return;
    }

    try {
      setIsLoading(true);
      
      ErrorHandler.logInfo('Processing payment', { amount, description, customerEmail });
      
      // Use the processResidentialPayment function to handle the payment through Supabase
      const paymentLink = await processResidentialPayment(
        formData,
        amount,
        user
      );
      
      if (paymentLink?.url) {
        ErrorHandler.logSuccess('Payment link created successfully', paymentLink.url);
        setRetryCount(0); // Reset retry count on success
        onSuccess(paymentLink.url);
      } else {
        throw new Error('Failed to create payment link - invalid response');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred processing your payment';
      ErrorHandler.logError(ErrorHandler.createError('PAYMENT_ERROR', 'Payment processing error', err));

      // Implement retry logic for network errors
      if (
        errorMessage.toLowerCase().includes('network error') && 
        retryCount < MAX_RETRIES
      ) {
        setRetryCount(prev => prev + 1);
        setError(`Network error. Retrying... (Attempt ${retryCount + 1}/${MAX_RETRIES})`);
        setTimeout(() => handleCreatePayment(), 2000); // Retry after 2 seconds
        return;
      }

      setError(errorMessage);
      onError(err instanceof Error ? err : new Error(errorMessage));
    } finally {
      setIsLoading(false);
    }
  };

  // Check if Square is properly configured
  const isConfigured = isSquareConfigured();

  // If Square is not configured, show a user-friendly message
  if (!isConfigured) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg sm:rounded-xl shadow-lg p-4 sm:p-6 lg:p-8"
      >
        <div className="flex items-center space-x-2 sm:space-x-3 mb-4 sm:mb-6">
          <div className="p-2 sm:p-3 rounded-full bg-amber-100">
            <AlertCircle className="w-5 h-5 sm:w-6 sm:h-6 text-amber-600" />
          </div>
          <h3 className="text-base sm:text-lg lg:text-xl font-medium text-gray-900">Payment System Unavailable</h3>
        </div>

        <div className="p-3 sm:p-4 bg-amber-50 rounded-lg mb-4 sm:mb-6">
          <p className="text-xs sm:text-sm lg:text-base text-amber-700 leading-relaxed">
            The payment system is not properly configured. Please ensure all required Square API credentials are set and are not using placeholder values.
          </p>
        </div>

        <Button
          onClick={() => window.location.href = '/contact'}
          className="w-full bg-amber-600 hover:bg-amber-700"
        >
          Contact Support
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg sm:rounded-xl shadow-lg p-4 sm:p-6 lg:p-8"
    >
      <div className="flex items-center space-x-2 sm:space-x-3 mb-4 sm:mb-6">
        <div className="p-2 sm:p-3 rounded-full bg-brand-100">
          <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-brand-600" />
        </div>
        <h3 className="text-base sm:text-lg lg:text-xl font-medium text-gray-900">Payment Details</h3>
      </div>

      <div className="space-y-4 sm:space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-3 sm:p-4 bg-gray-50 rounded-lg gap-2 sm:gap-0">
          <span className="text-sm sm:text-base text-gray-700 font-medium">Total Amount:</span>
          <div className="flex items-center">
            <DollarSign className="w-4 h-4 sm:w-5 sm:h-5 text-brand-600 mr-1" />
            <span className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">{amount.toFixed(2)}</span>
          </div>
        </div>

        <div className="p-3 sm:p-4 bg-brand-50 rounded-lg">
          <p className="text-brand-700 text-xs sm:text-sm lg:text-base leading-relaxed">
            You'll be redirected to Square's secure payment page to complete your transaction.
          </p>
        </div>

        {error && (
          <div className="p-3 sm:p-4 bg-red-50 rounded-lg flex items-start">
            <AlertCircle className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-red-700 text-xs sm:text-sm lg:text-base leading-relaxed">{error}</p>
          </div>
        )}

        <Button
          onClick={handleCreatePayment}
          disabled={isLoading}
          className="w-full py-3 sm:py-4 text-sm sm:text-base lg:text-lg font-medium touch-manipulation"
        >
          {isLoading ? (
            <>
              <Loader className="w-4 h-4 sm:w-5 sm:h-5 mr-2 animate-spin" />
              <span className="text-xs sm:text-sm lg:text-base">
                {retryCount > 0 ? `Retrying (${retryCount}/${MAX_RETRIES})...` : 'Processing...'}
              </span>
            </>
          ) : (
            <>
              <span>Proceed to Payment</span>
              <CreditCard className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
            </>
          )}
        </Button>
      </div>
    </motion.div>
  );
}
