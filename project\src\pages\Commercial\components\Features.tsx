import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Clock, Leaf, Users, HeartHandshake, Sparkles } from 'lucide-react';

const features = [
  { icon: Shield, title: 'Compliance & Safety', description: 'Adherence to OSHA standards, fully insured and bonded for your protection.' },
  { icon: Clock, title: 'After-Hours Service', description: 'Flexible cleaning schedules to minimize disruption to your business operations.' },
  { icon: Leaf, title: 'Green Cleaning Solutions', description: 'Optional eco-friendly and sustainable cleaning products for a healthier workplace.' },
  { icon: Users, title: 'Dedicated Account Manager', description: 'A single point of contact to ensure seamless communication and service.' },
  { icon: HeartHandshake, title: 'Service Level Agreements', description: 'Customized SLAs to guarantee quality and define service expectations.' },
  { icon: Sparkles, title: 'Advanced Disinfection', description: 'Specialized techniques and hospital-grade disinfectants for a sanitized environment.' }
];

export function Features() {
  return (
    <section className="py-24 sm:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Your Partner in Professionalism</h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto">
            We are committed to delivering a reliable, secure, and high-quality cleaning service.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6, ease: 'easeOut' }}
                className="flex items-start gap-6"
              >
                <div className="flex-shrink-0 flex items-center justify-center w-12 h-12 bg-white/10 rounded-xl border border-white/20">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                  <p className="text-white/70 leading-relaxed">{feature.description}</p>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
} 
