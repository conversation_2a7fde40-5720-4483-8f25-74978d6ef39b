import React from 'react';
import { FormField } from '../../components/FormFields';
import { Upload } from 'lucide-react';

export function StainTreatment() {
  return (
    <div className="space-y-6">
      <FormField label="Stain Types">
        <div className="grid grid-cols-2 gap-4">
          {[
            'Coffee/Tea',
            'Wine',
            'Pet Stains',
            'Food',
            'Ink',
            'Oil/Grease',
            'Unknown',
            'Other'
          ].map((stain) => (
            <label key={stain} className="flex items-center space-x-2">
              <input
                type="checkbox"
                name="stainTypes"
                value={stain.toLowerCase()}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="text-gray-700">{stain}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Upload Photos of Problem Areas">
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <input
            type="file"
            name="stainPhotos"
            accept="image/*"
            multiple
            className="hidden"
            id="stain-photos"
          />
          <label
            htmlFor="stain-photos"
            className="flex flex-col items-center cursor-pointer"
          >
            <Upload className="w-8 h-8 text-gray-400 mb-2" />
            <span className="text-sm text-gray-600">
              Click to upload photos (optional)
            </span>
          </label>
        </div>
      </FormField>
    </div>
  );
}
