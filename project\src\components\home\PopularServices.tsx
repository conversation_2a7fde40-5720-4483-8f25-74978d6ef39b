import React from 'react';
import { Building2, <PERSON><PERSON><PERSON>, <PERSON>an, Brush } from 'lucide-react';
import { ServiceCard } from './ServiceCard';

const services = [
  {
    icon: Building2,
    name: 'Office Cleaning',
    description: 'Regular cleaning for offices'
  },
  {
    icon: Sparkles,
    name: 'Deep Cleaning',
    description: 'Thorough cleaning and sanitization'
  },
  {
    icon: Scan,
    name: 'Window Cleaning',
    description: 'Professional window services'
  },
  {
    icon: Brush,
    name: 'Disinfection',
    description: 'Sanitization services'
  }
];

export function PopularServices() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
      {services.map((service) => (
        <ServiceCard key={service.name} service={service} />
      ))}
    </div>
  );
}
