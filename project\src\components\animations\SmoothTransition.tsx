import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SmoothTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade';
  duration?: number;
  delay?: number;
  className?: string;
}

export function SmoothTransition({
  children,
  isVisible,
  direction = 'fade',
  duration = 0.4,
  delay = 0,
  className = ''
}: SmoothTransitionProps) {
  const getVariants = () => {
    const baseVariants = {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 }
    };

    switch (direction) {
      case 'up':
        return {
          initial: { opacity: 0, y: 30, scale: 0.95 },
          animate: { opacity: 1, y: 0, scale: 1 },
          exit: { opacity: 0, y: -30, scale: 0.95 }
        };
      case 'down':
        return {
          initial: { opacity: 0, y: -30, scale: 0.95 },
          animate: { opacity: 1, y: 0, scale: 1 },
          exit: { opacity: 0, y: 30, scale: 0.95 }
        };
      case 'left':
        return {
          initial: { opacity: 0, x: 30, scale: 0.95 },
          animate: { opacity: 1, x: 0, scale: 1 },
          exit: { opacity: 0, x: -30, scale: 0.95 }
        };
      case 'right':
        return {
          initial: { opacity: 0, x: -30, scale: 0.95 },
          animate: { opacity: 1, x: 0, scale: 1 },
          exit: { opacity: 0, x: 30, scale: 0.95 }
        };
      default:
        return baseVariants;
    }
  };

  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.div
          variants={getVariants()}
          initial="initial"
          animate="animate"
          exit="exit"
          transition={{
            duration,
            delay,
            ease: [0.25, 0.46, 0.45, 0.94] // Custom easing for smooth feel
          }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface StaggeredTransitionProps {
  children: React.ReactNode[];
  isVisible: boolean;
  staggerDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade';
  className?: string;
}

export function StaggeredTransition({
  children,
  isVisible,
  staggerDelay = 0.1,
  direction = 'up',
  className = ''
}: StaggeredTransitionProps) {
  const getVariants = () => {
    switch (direction) {
      case 'up':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: -20 }
        };
      case 'down':
        return {
          initial: { opacity: 0, y: -20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: 20 }
        };
      case 'left':
        return {
          initial: { opacity: 0, x: 20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: -20 }
        };
      case 'right':
        return {
          initial: { opacity: 0, x: -20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: 20 }
        };
      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
    }
  };

  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.div
          initial="initial"
          animate="animate"
          exit="exit"
          className={className}
        >
          {children.map((child, index) => (
            <motion.div
              key={index}
              variants={getVariants()}
              transition={{
                duration: 0.4,
                delay: index * staggerDelay,
                ease: [0.25, 0.46, 0.45, 0.94]
              }}
            >
              {child}
            </motion.div>
          ))}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface SlideTransitionProps {
  children: React.ReactNode;
  direction?: 'horizontal' | 'vertical';
  className?: string;
}

export function SlideTransition({
  children,
  direction = 'horizontal',
  className = ''
}: SlideTransitionProps) {
  const variants = {
    enter: direction === 'horizontal' 
      ? { x: 300, opacity: 0 }
      : { y: 300, opacity: 0 },
    center: { x: 0, y: 0, opacity: 1 },
    exit: direction === 'horizontal'
      ? { x: -300, opacity: 0 }
      : { y: -300, opacity: 0 }
  };

  return (
    <motion.div
      variants={variants}
      initial="enter"
      animate="center"
      exit="exit"
      transition={{
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
} 
