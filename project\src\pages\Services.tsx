import React from 'react';
import { motion } from 'framer-motion';
import {
  Building2, <PERSON>rk<PERSON>, GlassWater, Brush,
  Construction, Sprout, Shield, Clock,
  Leaf, Users, Building, HeartPulse
} from 'lucide-react';
import { Button } from '../components/ui/Button';
import { Header } from '../components/layout/Header';
import { Footer } from '../components/layout/Footer';

const services = [
  {
    category: 'Core Cleaning Services',
    items: [
      {
        icon: Building2,
        title: 'Commercial Office Cleaning',
        description: 'Comprehensive cleaning solutions for offices of all sizes. Daily, weekly, or custom schedules available.',
        features: ['Workspace sanitization', 'Break room maintenance', 'Restroom cleaning', 'Trash removal'],
        image: 'https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80'
      },
      {
        icon: HeartPulse,
        title: 'Medical Facility Cleaning',
        description: 'Specialized cleaning and sanitization services for healthcare environments following strict protocols.',
        features: ['OSHA compliance', 'Medical-grade disinfection', 'Biohazard handling', 'Infection control'],
        image: 'https://images.unsplash.com/photo-1584744982491-665216d95f8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80'
      },
      {
        icon: Users,
        title: 'Educational Facility Services',
        description: 'Creating clean, healthy learning environments for students and staff.',
        features: ['Classroom cleaning', 'Common area maintenance', 'Gym sanitization', 'Cafeteria cleaning'],
        image: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80'
      }
    ]
  },
  {
    category: 'Specialized Services',
    items: [
      {
        icon: Sparkles,
        title: 'Deep Cleaning & Sanitization',
        description: 'Thorough deep cleaning and sanitization services for complete peace of mind.',
        features: ['Surface disinfection', 'Deep scrubbing', 'Air quality treatment', 'Odor elimination'],
        image: 'https://images.unsplash.com/photo-1584744982491-665216d95f8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80'
      },
      {
        icon: Construction,
        title: 'Post-Construction Cleanup',
        description: 'Detailed cleaning services after construction or renovation projects.',
        features: ['Debris removal', 'Dust elimination', 'Surface restoration', 'Final inspection'],
        image: 'https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80'
      },
      {
        icon: Building,
        title: 'Industrial & Warehouse',
        description: 'Heavy-duty cleaning solutions for industrial spaces and warehouses.',
        features: ['Equipment cleaning', 'Floor maintenance', 'Loading dock cleaning', 'Safety compliance'],
        image: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80'
      }
    ]
  }
];

const features = [
  {
    icon: Shield,
    title: 'Licensed & Insured',
    description: 'Fully bonded and insured for your peace of mind'
  },
  {
    icon: Clock,
    title: '24/7 Availability',
    description: 'Flexible scheduling to meet your needs'
  },
  {
    icon: Leaf,
    title: 'Eco-Friendly',
    description: '100% green cleaning products'
  }
];

export function Services() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-brand-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-white mb-6">
              Professional Cleaning Services
            </h1>
            <p className="text-xl text-brand-100 max-w-2xl mx-auto">
              Comprehensive commercial cleaning solutions tailored to your business needs
            </p>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="flex items-center space-x-4 p-6 bg-brand-50 rounded-xl"
              >
                <div className="rounded-full bg-brand-100 p-3">
                  <feature.icon className="w-6 h-6 text-brand-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Grid */}
      {services.map((category, categoryIndex) => (
        <section key={category.category} className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
              {category.category}
            </h2>
            <div className="space-y-16">
              {category.items.map((service, index) => (
                <motion.div
                  key={service.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.2 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
                >
                  <div className={`space-y-6 ${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                    <div className="inline-flex items-center space-x-3">
                      <div className="rounded-xl bg-brand-100 p-3">
                        <service.icon className="w-6 h-6 text-brand-600" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">
                        {service.title}
                      </h3>
                    </div>
                    <p className="text-lg text-gray-600">
                      {service.description}
                    </p>
                    <ul className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {service.features.map((feature) => (
                        <li key={feature} className="flex items-center space-x-3">
                          <Sparkles className="w-5 h-5 text-brand-500" />
                          <span className="text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <Button size="lg" className="mt-6">
                      Get a Quote
                    </Button>
                  </div>
                  <div className={`relative ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                    <div className="aspect-w-16 aspect-h-9 rounded-2xl overflow-hidden shadow-2xl">
                      <img
                        src={service.image}
                        alt={service.title}
                        className="object-cover w-full h-full"
                      />
                      <div className="absolute inset-0 bg-gradient-to-r from-brand-600/20 to-transparent" />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      ))}

      {/* CTA Section */}
      <section className="py-24 bg-brand-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-6">
            Ready to Transform Your Space?
          </h2>
          <p className="text-xl text-brand-100 mb-8 max-w-2xl mx-auto">
            Get in touch for a customized cleaning plan that meets your specific needs
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              size="lg"
              className="bg-white text-brand-600 hover:bg-brand-50"
            >
              Get Free Quote
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10"
            >
              Contact Us
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
