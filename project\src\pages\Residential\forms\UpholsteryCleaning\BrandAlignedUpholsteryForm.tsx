import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sofa, Calendar, Clock, CheckCircle, 
  Shield, Star, Building2,
  Users, DollarSign, Heart,
  ChevronRight, ChevronLeft, Sparkles, Droplets, Wind
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

interface BookingFormData {
  // Property Info
  propertyType: string;
  propertySize: string;
  
  // Service Details  
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  
  // Add-ons
  addOns: string[];
  
  // Contact Info
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  
  // Marketing
  howDidYouHear: string;
  newsletter: boolean;
}

const BrandAlignedUpholsteryForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState<BookingFormData>({
    propertyType: '',
    propertySize: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    howDidYouHear: '',
    newsletter: false
  });

  // Check for form data restoration after login
  useEffect(() => {
    const savedFormData = localStorage.getItem('upholsteryCleaningFormData');
    if (savedFormData && user) {
      try {
        const parsedData = JSON.parse(savedFormData);
        setFormData(parsedData);
        localStorage.removeItem('upholsteryCleaningFormData');
        // Auto-open payment modal after restore
        setTimeout(() => {
          setShowPaymentModal(true);
        }, 1000);
      } catch (error) {
        console.error('Error parsing saved form data:', error);
        localStorage.removeItem('upholsteryCleaningFormData');
      }
    }
  }, [user]);

  // Dynamic price calculation for upholstery cleaning
  const calculatePrice = () => {
    const addOnPrices: Record<string, number> = {
      'fabric-protection': 49,
      'scotchgard': 69,
      'stain-treatment': 39,
      'deodorizing': 29
    };

    // Get base price from selected property size
    const selectedSize = getPropertySizes().find(size => size.id === formData.propertySize);
    const price = selectedSize?.basePrice || 149;
    
    // Add-on costs
    const addOnTotal = formData.addOns?.reduce((total, addon) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;

    return Math.round(price + addOnTotal);
  };

  // Property types for upholstery cleaning
  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Studio to 3BR',
      floors: '1 level',
      time: '1-2 hours'
    },
    { 
      id: 'house', 
      name: 'House', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Single family',
      floors: '1-3 levels',
      time: '2-3 hours'
    },
    { 
      id: 'condo', 
      name: 'Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Any size',
      floors: '1 level',
      time: '1-2 hours'
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Multi-level',
      floors: '2-3 levels',
      time: '2-3 hours'
    }
  ];

  // Upholstery-specific pricing (piece-based rather than room-based)
  const propertyTypeConfigs = {
    apartment: [
      { 
        id: 'small-pieces', 
        name: '1-2 Pieces', 
        description: 'Single chair or loveseat',
        rooms: '1-2 furniture pieces',
        time: '1-1.5 hours',
        price: 'From $89',
        basePrice: 89,
        popular: false 
      },
      { 
        id: 'medium-pieces', 
        name: '3-4 Pieces', 
        description: 'Sofa + chair combo',
        rooms: '3-4 furniture pieces',
        time: '1.5-2 hours',
        price: 'From $149',
        basePrice: 149,
        popular: true 
      },
      { 
        id: 'large-pieces', 
        name: '5-6 Pieces', 
        description: 'Full living room set',
        rooms: '5-6 furniture pieces',
        time: '2-2.5 hours',
        price: 'From $219',
        basePrice: 219,
        popular: false 
      },
      { 
        id: 'xl-pieces', 
        name: '7+ Pieces', 
        description: 'Multiple rooms',
        rooms: '7+ furniture pieces',
        time: '2.5-3 hours',
        price: 'From $289',
        basePrice: 289,
        popular: false 
      }
    ],
    house: [
      { 
        id: 'small-house', 
        name: '3-5 Pieces', 
        description: 'Main living area',
        rooms: '3-5 furniture pieces',
        time: '1.5-2.5 hours',
        price: 'From $179',
        basePrice: 179,
        popular: true 
      },
      { 
        id: 'medium-house', 
        name: '6-8 Pieces', 
        description: 'Living + family room',
        rooms: '6-8 furniture pieces',
        time: '2.5-3.5 hours',
        price: 'From $259',
        basePrice: 259,
        popular: false 
      },
      { 
        id: 'large-house', 
        name: '9-12 Pieces', 
        description: 'Multiple living areas',
        rooms: '9-12 furniture pieces',
        time: '3.5-4.5 hours',
        price: 'From $349',
        basePrice: 349,
        popular: false 
      },
      { 
        id: 'xl-house', 
        name: '13+ Pieces', 
        description: 'Whole house furniture',
        rooms: '13+ furniture pieces',
        time: '4.5+ hours',
        price: 'From $449',
        basePrice: 449,
        popular: false 
      }
    ],
    condo: [
      { 
        id: 'small-condo', 
        name: '2-4 Pieces', 
        description: 'Main seating area',
        rooms: '2-4 furniture pieces',
        time: '1-2 hours',
        price: 'From $129',
        basePrice: 129,
        popular: true 
      },
      { 
        id: 'medium-condo', 
        name: '5-7 Pieces', 
        description: 'Living + dining',
        rooms: '5-7 furniture pieces',
        time: '2-3 hours',
        price: 'From $199',
        basePrice: 199,
        popular: false 
      },
      { 
        id: 'large-condo', 
        name: '8+ Pieces', 
        description: 'Multiple rooms',
        rooms: '8+ furniture pieces',
        time: '3-4 hours',
        price: 'From $279',
        basePrice: 279,
        popular: false 
      }
    ],
    townhouse: [
      { 
        id: 'small-townhouse', 
        name: '4-6 Pieces', 
        description: 'Main level furniture',
        rooms: '4-6 furniture pieces',
        time: '2-2.5 hours',
        price: 'From $199',
        basePrice: 199,
        popular: true 
      },
      { 
        id: 'medium-townhouse', 
        name: '7-10 Pieces', 
        description: 'Multi-level seating',
        rooms: '7-10 furniture pieces',
        time: '2.5-3.5 hours',
        price: 'From $289',
        basePrice: 289,
        popular: false 
      },
      { 
        id: 'large-townhouse', 
        name: '11+ Pieces', 
        description: 'Whole home furniture',
        rooms: '11+ furniture pieces',
        time: '3.5+ hours',
        price: 'From $389',
        basePrice: 389,
        popular: false 
      }
    ]
  };

  // Get property sizes based on selected property type
  const getPropertySizes = () => {
    if (!formData.propertyType) return [];
    const config = propertyTypeConfigs[formData.propertyType as keyof typeof propertyTypeConfigs];
    return config || [];
  };

  // Upholstery-specific add-on services
  const addOnServices = [
    {
      id: 'fabric-protection',
      name: 'Fabric Protection',
      description: 'Protective coating to repel stains and spills',
      price: 49,
      icon: <Shield className="w-5 h-5" />,
      recommended: true
    },
    {
      id: 'scotchgard',
      name: 'Scotchgard™ Treatment',
      description: 'Premium stain and water protection',
      price: 69,
      icon: <Droplets className="w-5 h-5" />
    },
    {
      id: 'stain-treatment',
      name: 'Stain Pre-Treatment',
      description: 'Target tough stains before cleaning',
      price: 39,
      icon: <Sparkles className="w-5 h-5" />
    },
    {
      id: 'deodorizing',
      name: 'Deodorizing Treatment',
      description: 'Eliminate odors and freshen fabric',
      price: 29,
      icon: <Wind className="w-5 h-5" />
    }
  ];

  // Enhanced time slots
  const timeSlots = [
    { 
      id: 'morning', 
      name: '8:00 AM - 11:00 AM', 
      label: 'Morning',
      available: true,
      slots: 4,
      urgency: 'Few spots left',
      urgencyColor: 'text-orange-600'
    },
    { 
      id: 'midday', 
      name: '11:00 AM - 2:00 PM', 
      label: 'Midday',
      available: true,
      slots: 6,
      urgency: 'Available',
      urgencyColor: 'text-green-600'
    },
    { 
      id: 'afternoon', 
      name: '2:00 PM - 5:00 PM', 
      label: 'Afternoon',
      available: true,
      slots: 3,
      urgency: 'Popular time',
      urgencyColor: 'text-yellow-600'
    },
    { 
      id: 'evening', 
      name: '5:00 PM - 7:00 PM', 
      label: 'Evening',
      available: true,
      slots: 2,
      urgency: 'Last 2 spots',
      urgencyColor: 'text-red-600'
    }
  ];

  // Form validation
  const isStepValid = (step: number) => {
    switch (step) {
      case 0: return formData.propertyType && formData.propertySize;
      case 1: return formData.preferredDate && formData.preferredTime;
      case 2: return true; // Add-ons are optional
      case 3: return formData.firstName && formData.lastName && formData.email && formData.phone && formData.address;
      default: return false;
    }
  };

  // Submit handler
  const handleSubmit = async () => {
    if (!isStepValid(3)) return;
    
    // Check if user is authenticated
    if (!user) {
      // Save form data and redirect to login
      localStorage.setItem('upholsteryCleaningFormData', JSON.stringify(formData));
      navigate('/auth/login');
      return;
    }
    
    // Open payment modal for authenticated users
    setShowPaymentModal(true);
  };

  // Calendar functionality
  const getCalendarDates = () => {
    const today = new Date();
    const dates = [];
    
    // Generate next 14 days
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      // Skip Sundays (business choice)
      if (date.getDay() === 0) continue;
      
      dates.push({
        date: date.toISOString().split('T')[0],
        display: date.toLocaleDateString('en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        }),
        available: Math.random() > 0.1, // 90% availability simulation
        dayOfWeek: date.getDay()
      });
    }
    
    return dates.slice(0, 12); // Show 12 available dates
  };

  const handleStepNavigation = (direction: 'next' | 'prev') => {
    if (direction === 'next') {
      if (isStepValid(currentStep) && currentStep < 3) {
        setCurrentStep(currentStep + 1);
      } else if (currentStep === 3 && isStepValid(3)) {
        handleSubmit();
      }
    } else {
      if (currentStep > 0) {
        setCurrentStep(currentStep - 1);
      }
    }
  };

  const steps = [
    {
      title: "Furniture Details",
      subtitle: "Property & piece count",
      icon: <Sofa className="w-6 h-6" />
    },
    {
      title: "Schedule Service", 
      subtitle: "Pick date & time",
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: "Customize Service",
      subtitle: "Add-on services",
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: "Contact Info",
      subtitle: "Complete booking",
      icon: <CheckCircle className="w-6 h-6" />
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Sofa className="w-8 h-8 text-brand-600" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Book Upholstery Cleaning</h1>
                <p className="text-sm text-gray-600">Professional furniture & fabric care for your home</p>
              </div>
            </div>
            
            {/* Enhanced Trust badges */}
            <div className="hidden md:flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-brand-600" />
                <span className="text-sm font-medium">Fully Insured & Bonded</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium">4.9★ (2,847 reviews)</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium">100% Satisfaction Guarantee</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex items-center">
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: currentStep === index ? 1.1 : 1,
                      backgroundColor: currentStep >= index ? '#638907' : '#e5e7eb'
                    }}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white transition-all`}
                  >
                    {currentStep > index ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className={currentStep >= index ? 'text-white' : 'text-gray-600'}>
                        {index + 1}
                      </span>
                    )}
                  </motion.div>
                  <div className="hidden md:block ml-3">
                    <p className={`font-medium ${currentStep >= index ? 'text-brand-600' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500">{step.subtitle}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRight className={`mx-4 w-5 h-5 ${
                    currentStep > index ? 'text-brand-600' : 'text-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
          {/* Step 0: Property Details */}
          {currentStep === 0 && (
            <motion.div
              key="step0"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="bg-white rounded-2xl shadow-soft p-8"
            >
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">What type of property?</h2>
                <p className="text-gray-600">This helps us assign the right team and equipment</p>
              </div>

              {/* Property Type Selection */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <label className="block text-sm font-semibold text-gray-700">Property Type</label>
                  <span className="text-xs text-gray-500">All types welcome!</span>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {propertyTypes.map((type) => (
                    <motion.button
                      key={type.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setFormData({ 
                        ...formData, 
                        propertyType: type.id,
                        propertySize: '' // Reset property size when type changes
                      })}
                      className={`relative p-6 rounded-2xl border-2 transition-all text-left ${
                        formData.propertyType === type.id
                          ? 'border-brand-500 bg-brand-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 bg-white'
                      }`}
                    >
                      <div className={`mb-3 ${
                        formData.propertyType === type.id ? 'text-brand-600' : 'text-gray-600'
                      }`}>
                        {type.icon}
                      </div>
                      <h3 className="font-semibold text-gray-900">{type.name}</h3>
                      <p className="text-sm text-gray-500 mt-1">{type.description}</p>
                      <div className="mt-3 space-y-1">
                        <div className="flex items-center gap-2 text-xs text-gray-600">
                          <Home className="w-3 h-3" />
                          <span>{type.floors}</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-600">
                          <Clock className="w-3 h-3" />
                          <span>{type.time}</span>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Property Size Selection */}
              {formData.propertyType && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">How many pieces need cleaning?</h3>
                  <p className="text-gray-600 mb-6">Choose the package that best fits your furniture</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {getPropertySizes().map((size) => {
                      const isSelected = formData.propertySize === size.id;
                      return (
                        <motion.button
                          key={size.id}
                          whileHover={{ scale: 1.01 }}
                          whileTap={{ scale: 0.99 }}
                          onClick={() => setFormData({ ...formData, propertySize: size.id })}
                          className={`p-6 rounded-2xl border-2 text-left transition-all relative ${
                            isSelected 
                              ? 'border-brand-500 bg-brand-50 shadow-lg' 
                              : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                          }`}
                        >
                          {size.popular && (
                            <div className="absolute -top-2 -right-2 bg-brand-500 text-white text-xs px-2 py-1 rounded-full">
                              Most Popular
                            </div>
                          )}
                          
                          <div className="flex items-start gap-4">
                            <div className={`p-3 rounded-xl transition-colors ${
                              isSelected
                                ? 'bg-brand-100 text-brand-600'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              <Sofa className="w-6 h-6" />
                            </div>
                            <div className="flex-1">
                              <h4 className="font-semibold text-gray-900 text-lg">{size.name}</h4>
                              <p className="text-sm text-gray-600 mt-1">{size.description}</p>
                              <div className="mt-3 space-y-2">
                                <div className="flex items-center gap-2 text-xs text-gray-600">
                                  <Home className="w-3 h-3" />
                                  <span>{size.rooms}</span>
                                </div>
                                <div className="flex items-center gap-2 text-xs text-gray-600">
                                  <Clock className="w-3 h-3" />
                                  <span>Typically {size.time}</span>
                                </div>
                                <div className="flex items-center gap-2 text-xs font-medium text-brand-600">
                                  <DollarSign className="w-3 h-3" />
                                  <span>{size.price}</span>
                                </div>
                              </div>
                            </div>
                                                      </div>
                        </motion.button>
                      );
                    })}
                  </div>
                </motion.div>
              )}

              {/* Upholstery cleaning specifics */}
              {formData.propertySize && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-blue-50 rounded-xl p-6"
                >
                  <h4 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Sparkles className="w-5 h-5 text-brand-600 mr-2" />
                    Professional Upholstery Cleaning Includes
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center text-gray-700">
                        <CheckCircle className="w-4 h-4 text-brand-600 mr-2" />
                        <span className="text-sm">Deep fabric extraction cleaning</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <CheckCircle className="w-4 h-4 text-brand-600 mr-2" />
                        <span className="text-sm">Stain spot treatment</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <CheckCircle className="w-4 h-4 text-brand-600 mr-2" />
                        <span className="text-sm">Fabric conditioning</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center text-gray-700">
                        <CheckCircle className="w-4 h-4 text-brand-600 mr-2" />
                        <span className="text-sm">Odor elimination treatment</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <CheckCircle className="w-4 h-4 text-brand-600 mr-2" />
                        <span className="text-sm">Color restoration</span>
                      </div>
                      <div className="flex items-center text-gray-700">
                        <CheckCircle className="w-4 h-4 text-brand-600 mr-2" />
                        <span className="text-sm">Fast-dry technology</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </motion.div>
          )}

          {/* Step 2: Schedule */}
          {currentStep === 1 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Schedule your upholstery cleaning</h2>
                <p className="text-gray-600 mb-6">Choose your preferred date and time</p>
                
                {/* Calendar */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Dates</h3>
                  <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    {getCalendarDates().map((date) => {
                      const isSelected = formData.preferredDate === date.date;
                      const isAvailable = date.available;
                      
                      return (
                        <motion.button
                          key={date.date}
                          whileHover={{ scale: isAvailable ? 1.05 : 1 }}
                          whileTap={{ scale: isAvailable ? 0.95 : 1 }}
                          onClick={() => isAvailable && setFormData({ ...formData, preferredDate: date.date })}
                          disabled={!isAvailable}
                          className={`p-3 rounded-lg text-center transition-all ${
                            isSelected 
                              ? 'bg-brand-500 text-white shadow-lg' 
                              : isAvailable
                                ? 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
                                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          }`}
                        >
                          <div className="text-sm font-medium">{date.display}</div>
                          {!isAvailable && (
                            <div className="text-xs mt-1">Booked</div>
                          )}
                        </motion.button>
                      );
                    })}
                  </div>
                </div>

                {/* Time Slots */}
                {formData.preferredDate && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-4"
                  >
                    <h3 className="text-lg font-semibold text-gray-900">Preferred Time</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {timeSlots.map((slot) => {
                        const isSelected = formData.preferredTime === slot.id;
                        return (
                          <motion.button
                            key={slot.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, preferredTime: slot.id })}
                                                      className={`p-4 rounded-lg border-2 text-left transition-all ${
                            isSelected 
                              ? 'border-brand-500 bg-brand-50' 
                              : 'border-gray-200 hover:border-brand-300'
                          }`}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium text-gray-900">{slot.name}</div>
                                <div className="text-sm text-gray-600">{slot.label}</div>
                              </div>
                              <div className="text-right">
                                <div className={`text-sm font-medium ${slot.urgencyColor}`}>
                                  {slot.urgency}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {slot.slots} slots
                                </div>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          )}

          {/* Step 3: Add-ons */}
          {currentStep === 2 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Enhance Your Service</h2>
                <p className="text-gray-600">Enhance your upholstery cleaning with these professional services</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {addOnServices.map((addon) => {
                  const isSelected = formData.addOns.includes(addon.id);
                  return (
                    <motion.div
                      key={addon.id}
                      whileHover={{ scale: 1.02 }}
                      className={`p-6 rounded-xl border-2 cursor-pointer transition-all ${
                        isSelected 
                                                      ? 'border-brand-500 bg-brand-50' 
                            : 'border-gray-200 hover:border-brand-300'
                      }`}
                      onClick={() => {
                        const newAddOns = isSelected 
                          ? formData.addOns.filter(id => id !== addon.id)
                          : [...formData.addOns, addon.id];
                        setFormData({ ...formData, addOns: newAddOns });
                      }}
                    >
                      {addon.recommended && (
                        <div className="inline-block bg-brand-500 text-white text-xs px-2 py-1 rounded-full mb-3">
                          Recommended
                        </div>
                      )}
                      
                      <div className="flex items-start justify-between mb-4">
                        <div className={`p-2 rounded-lg ${
                          isSelected ? 'bg-brand-100' : 'bg-gray-100'
                        }`}>
                          <div className={isSelected ? 'text-brand-600' : 'text-gray-600'}>
                            {addon.icon}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-xl font-bold text-brand-600">+${addon.price}</div>
                        </div>
                      </div>
                      
                      <h3 className="font-semibold text-gray-900 mb-2">{addon.name}</h3>
                      <p className="text-gray-600 text-sm">{addon.description}</p>
                      
                      <div className={`mt-4 text-center py-2 rounded-lg transition-all ${
                        isSelected ? 'bg-brand-500 text-white' : 'bg-gray-100 text-gray-600'
                      }`}>
                        {isSelected ? 'Added to Service' : 'Add to Service'}
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
          )}

          {/* Step 4: Contact Info */}
          {currentStep === 3 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Complete Your Booking</h2>
                <p className="text-gray-600">We'll use this information to confirm your upholstery cleaning</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Email Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Phone Number <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="space-y-2 md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Service Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.address}
                    onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    placeholder="123 Main St"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    City <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.city}
                    onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    ZIP Code <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.zipCode}
                    onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="space-y-2 md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Special Instructions (Optional)
                  </label>
                  <textarea
                    value={formData.specialInstructions}
                    onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    rows={3}
                    placeholder="Access instructions, specific stains, special requests..."
                  />
                </div>
              </div>

              {/* Order Summary */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-brand-50 rounded-xl p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>Upholstery cleaning ({getPropertySizes().find(size => size.id === formData.propertySize)?.name})</span>
                    <span className="font-medium">${getPropertySizes().find(size => size.id === formData.propertySize)?.basePrice}</span>
                  </div>
                  
                  {formData.addOns.map(addonId => {
                    const addon = addOnServices.find(a => a.id === addonId);
                    return addon ? (
                      <div key={addonId} className="flex justify-between items-center text-gray-700">
                        <span>{addon.name}</span>
                        <span>+${addon.price}</span>
                      </div>
                    ) : null;
                  })}
                  
                  <div className="border-t border-brand-200 pt-3">
                    <div className="flex justify-between items-center text-lg font-bold">
                      <span>Total</span>
                      <span className="text-brand-600">${calculatePrice()}</span>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 p-4 bg-brand-100 rounded-lg">
                  <p className="text-sm text-brand-800 mb-4">Professional upholstery cleaning + fabric protection</p>
                  <div className="flex items-center space-x-4 text-sm text-brand-700">
                    <div className="flex items-center">
                      <Shield className="w-4 h-4 mr-1" />
                      <span>Licensed & Insured</span>
                    </div>
                    <div className="flex items-center">
                      <Star className="w-4 h-4 mr-1" />
                      <span>4.9/5 Rating</span>
                    </div>
                    <div className="flex items-center">
                      <Heart className="w-4 h-4 mr-1" />
                      <span>Satisfaction Guaranteed</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
            </AnimatePresence>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-32 space-y-6">
              {/* Price Summary */}
              <div className="bg-white rounded-2xl shadow-soft p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Summary</h3>
                
                {formData.propertySize && (
                  <div className="space-y-3 mb-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">
                        {getPropertySizes().find(size => size.id === formData.propertySize)?.name}
                      </span>
                      <span className="font-medium">
                        ${getPropertySizes().find(size => size.id === formData.propertySize)?.basePrice}
                      </span>
                    </div>
                    
                    {formData.addOns.map(addonId => {
                      const addon = addOnServices.find(a => a.id === addonId);
                      return addon ? (
                        <div key={addonId} className="flex justify-between items-center text-sm text-gray-600">
                          <span>{addon.name}</span>
                          <span>+${addon.price}</span>
                        </div>
                      ) : null;
                    })}
                    
                    <div className="border-t pt-3">
                      <div className="flex justify-between items-center text-lg font-bold">
                        <span>Total</span>
                        <span className="text-brand-600">${calculatePrice()}</span>
                      </div>
                    </div>
                  </div>
                )}
                
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>Professional fabric cleaning</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>Stain treatment included</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-4 h-4 text-green-600 mr-2" />
                    <span>Fast-dry technology</span>
                  </div>
                </div>
              </div>

              {/* Trust Signals */}
              <div className="bg-white rounded-2xl shadow-soft p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Why Choose Us?</h3>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Shield className="w-5 h-5 text-brand-600 mr-3 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">Fully Insured</p>
                      <p className="text-sm text-gray-600">Licensed & bonded professionals</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Star className="w-5 h-5 text-yellow-500 mr-3 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">4.9★ Rating</p>
                      <p className="text-sm text-gray-600">2,847+ satisfied customers</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Heart className="w-5 h-5 text-red-500 mr-3 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">100% Guarantee</p>
                      <p className="text-sm text-gray-600">We'll re-clean if not satisfied</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t">
          <Button
            variant="outline"
            onClick={() => handleStepNavigation('prev')}
            disabled={currentStep === 0}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Back</span>
          </Button>

          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>Takes 2 minutes</span>
          </div>

          <Button
            onClick={() => handleStepNavigation('next')}
            disabled={!isStepValid(currentStep)}
            className="flex items-center space-x-2 bg-brand-600 hover:bg-brand-700"
          >
            {currentStep === 3 ? (
              <>
                <span>Complete Booking</span>
                <CheckCircle className="w-4 h-4" />
              </>
            ) : (
              <>
                <span>Continue</span>
                <ChevronRight className="w-4 h-4" />
              </>
            )}
          </Button>
        </div>

        {/* Trust Signals */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-8 text-center"
        >
          <div className="flex justify-center items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center">
              <Shield className="w-4 h-4 mr-1" />
              <span>Satisfaction Guaranteed</span>
            </div>
            <div className="flex items-center">
              <Users className="w-4 h-4 mr-1" />
              <span>300+ Happy Customers</span>
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              <span>Same Day Service Available</span>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description={`Upholstery Cleaning - ${getPropertySizes().find(size => size.id === formData.propertySize)?.name || 'Custom'}`}
          customerEmail={formData.email}
          formData={{
            ...formData,
            serviceType: 'upholstery-cleaning'
          }}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedUpholsteryForm; 

