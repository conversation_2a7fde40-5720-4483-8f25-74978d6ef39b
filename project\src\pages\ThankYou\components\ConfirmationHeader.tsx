import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Star, Clock, Users } from 'lucide-react';

const badges = [
  {
    icon: Shield,
    text: 'Licensed & Insured',
    color: 'from-emerald-500 to-green-600',
    delay: 0.2
  },
  {
    icon: Star,
    text: '4.9/5 Rating',
    color: 'from-amber-500 to-yellow-600',
    delay: 0.3
  },
  {
    icon: Clock,
    text: '24/7 Support',
    color: 'from-blue-500 to-indigo-600',
    delay: 0.4
  },
  {
    icon: Users,
    text: 'Expert Team',
    color: 'from-purple-500 to-pink-600',
    delay: 0.5
  }
];

export function ConfirmationHeader() {
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center mb-12"
    >
      <motion.h1
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-brand-600 to-brand-400 mb-4"
      >
        Thank You!
      </motion.h1>
      
      <motion.p
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="text-2xl text-gray-600 mb-12"
      >
        Your booking has been confirmed
      </motion.p>

      {/* Trust Badges */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {badges.map((badge, index) => {
          const Icon = badge.icon;
          return (
            <motion.div
              key={badge.text}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: badge.delay }}
              whileHover={{ scale: 1.05 }}
              className="relative overflow-hidden rounded-2xl shadow-lg group"
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${badge.color} opacity-10 
                            group-hover:opacity-20 transition-opacity`} />
              <div className="relative p-4">
                <Icon className="w-8 h-8 mx-auto mb-3 text-brand-600" />
                <p className="font-medium text-gray-900">{badge.text}</p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
}
