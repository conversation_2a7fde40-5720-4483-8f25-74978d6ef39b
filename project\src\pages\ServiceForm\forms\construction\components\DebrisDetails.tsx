import React from 'react';
import { Trash2, AlertTriangle, Truck, HardHat } from 'lucide-react';

interface DebrisDetailsProps {
  details: {
    debrisTypes: string[];
    disposalRequired: boolean;
    heavyEquipment: boolean;
    hazardousMaterials: boolean;
  };
  onChange: (details: any) => void;
}

export function DebrisDetails({ details, onChange }: DebrisDetailsProps) {
  const debrisTypes = [
    'Construction Waste',
    'Drywall/Plaster',
    'Wood/Lumber',
    'Metal/Wiring',
    'Paint/Solvents',
    'Glass/Windows',
    'Packaging Materials',
    'Concrete/Masonry'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Trash2 className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Debris Details</h3>
          <p className="text-gray-600">Specify debris types and disposal requirements</p>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Debris Types <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {debrisTypes.map((type) => (
              <label
                key={type}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  details.debrisTypes.includes(type)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.debrisTypes.includes(type)}
                  onChange={(e) => {
                    const newTypes = e.target.checked
                      ? [...details.debrisTypes, type]
                      : details.debrisTypes.filter(t => t !== type);
                    onChange({ ...details, debrisTypes: newTypes });
                  }}
                  className="sr-only"
                />
                <Trash2 className={`w-5 h-5 mr-3 ${
                  details.debrisTypes.includes(type) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{type}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Additional Requirements
          </label>
          <div className="space-y-4">
            <label className="flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer hover:border-brand-300">
              <input
                type="checkbox"
                checked={details.disposalRequired}
                onChange={(e) => onChange({ ...details, disposalRequired: e.target.checked })}
                className="sr-only"
              />
              <div className={`p-2 rounded-lg mr-3 transition-colors ${
                details.disposalRequired ? 'bg-brand-100' : 'bg-gray-100'
              }`}>
                <Truck className={`w-5 h-5 ${
                  details.disposalRequired ? 'text-brand-600' : 'text-gray-400'
                }`} />
              </div>
              <div>
                <span className="font-medium text-gray-900">Disposal Service Required</span>
                <p className="text-sm text-gray-600">We'll handle debris removal and disposal</p>
              </div>
            </label>

            <label className="flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer hover:border-brand-300">
              <input
                type="checkbox"
                checked={details.heavyEquipment}
                onChange={(e) => onChange({ ...details, heavyEquipment: e.target.checked })}
                className="sr-only"
              />
              <div className={`p-2 rounded-lg mr-3 transition-colors ${
                details.heavyEquipment ? 'bg-brand-100' : 'bg-gray-100'
              }`}>
                <HardHat className={`w-5 h-5 ${
                  details.heavyEquipment ? 'text-brand-600' : 'text-gray-400'
                }`} />
              </div>
              <div>
                <span className="font-medium text-gray-900">Heavy Equipment Needed</span>
                <p className="text-sm text-gray-600">Requires special equipment for debris removal</p>
              </div>
            </label>

            <label className="flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer hover:border-brand-300">
              <input
                type="checkbox"
                checked={details.hazardousMaterials}
                onChange={(e) => onChange({ ...details, hazardousMaterials: e.target.checked })}
                className="sr-only"
              />
              <div className={`p-2 rounded-lg mr-3 transition-colors ${
                details.hazardousMaterials ? 'bg-brand-100' : 'bg-gray-100'
              }`}>
                <AlertTriangle className={`w-5 h-5 ${
                  details.hazardousMaterials ? 'text-brand-600' : 'text-gray-400'
                }`} />
              </div>
              <div>
                <span className="font-medium text-gray-900">Hazardous Materials Present</span>
                <p className="text-sm text-gray-600">Requires special handling and disposal</p>
              </div>
            </label>
          </div>
        </div>

        {details.hazardousMaterials && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Hazardous Materials Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Special handling procedures and additional fees may apply for hazardous materials disposal. Our team will contact you to discuss specific requirements and safety protocols.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
