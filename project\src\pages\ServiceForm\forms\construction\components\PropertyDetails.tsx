import React from 'react';
import { Building2, MapPin, Building, Layers, Calendar, Clock, Car, Shield } from 'lucide-react';
import { motion } from 'framer-motion';

interface PropertyDetailsProps {
  details: {
    projectType: string;
    squareFootage: number;
    floors: number;
    propertyAddress: string;
    propertyType: string;
    industryType: string;
    completionDate: string;
    projectPhase: string;
    accessHours: string;
    securityRequirements: string;
    parkingAvailable: boolean;
  };
  onChange: (details: any) => void;
}

export function PropertyDetails({ details, onChange }: PropertyDetailsProps) {
  const propertyTypes = [
    'Commercial Building',
    'Industrial Facility',
    'Retail Center',
    'Medical Facility',
    'Educational Institution',
    'Mixed-Use Development',
    'Other'
  ];

  const industryTypes = [
    'Corporate',
    'Manufacturing',
    'Healthcare',
    'Education',
    'Retail',
    'Hospitality',
    'Government',
    'Other'
  ];

  const projectPhases = [
    'Pre-Construction',
    'Foundation',
    'Framing',
    'Interior Finishing',
    'Final Phase',
    'Post-Completion'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Building2 className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Property Details</h3>
          <p className="text-gray-600">Tell us about your construction project</p>
        </div>
      </motion.div>

      {/* Basic Property Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Property Type <span className="text-red-500">*</span>
          </label>
          <select
            value={details.propertyType}
            onChange={(e) => onChange({ ...details, propertyType: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select property type</option>
            {propertyTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Industry Type <span className="text-red-500">*</span>
          </label>
          <select
            value={details.industryType}
            onChange={(e) => onChange({ ...details, industryType: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select industry type</option>
            {industryTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Project Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Project Phase <span className="text-red-500">*</span>
          </label>
          <select
            value={details.projectPhase}
            onChange={(e) => onChange({ ...details, projectPhase: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select project phase</option>
            {projectPhases.map((phase) => (
              <option key={phase} value={phase}>{phase}</option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Expected Completion Date <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="date"
              value={details.completionDate}
              onChange={(e) => onChange({ ...details, completionDate: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>
        </div>
      </div>

      {/* Space Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Square Footage <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.squareFootage || ''}
              onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Total area"
              min="1"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Number of Floors <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Layers className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.floors || ''}
              onChange={(e) => onChange({ ...details, floors: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              min="1"
              required
            />
          </div>
        </div>
      </div>

      {/* Location & Access */}
      <div className="space-y-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Property Address <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={details.propertyAddress}
              onChange={(e) => onChange({ ...details, propertyAddress: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Enter complete address"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Access Hours <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={details.accessHours}
                onChange={(e) => onChange({ ...details, accessHours: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="e.g., 7 AM - 5 PM"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Security Requirements
            </label>
            <div className="relative">
              <Shield className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <textarea
                value={details.securityRequirements}
                onChange={(e) => onChange({ ...details, securityRequirements: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                rows={3}
                placeholder="Any security protocols or requirements?"
              />
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={details.parkingAvailable}
            onChange={(e) => onChange({ ...details, parkingAvailable: e.target.checked })}
            className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
          />
          <div className="flex items-center space-x-2">
            <Car className="w-5 h-5 text-gray-400" />
            <span className="text-gray-700">Parking available for cleaning staff</span>
          </div>
        </div>
      </div>
    </div>
  );
}
