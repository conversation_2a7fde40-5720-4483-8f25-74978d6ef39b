import React from 'react';
import { Spark<PERSON>, Target, Users, Clock } from 'lucide-react';

interface SanitizationScopeProps {
  scope: {
    areas: string[];
    highTouchSurfaces: string[];
    occupancy: string;
    operatingHours: string;
  };
  onChange: (scope: any) => void;
}

export function SanitizationScope({ scope, onChange }: SanitizationScopeProps) {
  const areas = [
    'Workstations',
    'Conference Rooms',
    'Break Rooms',
    'Restrooms',
    'Reception Area',
    'Common Areas',
    'Elevators',
    'Stairwells'
  ];

  const highTouchSurfaces = [
    'Door Handles',
    'Light Switches',
    'Elevator Buttons',
    'Handrails',
    'Counter Tops',
    'Shared Equipment',
    'Phones/Keyboards',
    'Tables/Chairs'
  ];

  const occupancyLevels = [
    'Low (1-10 people)',
    'Medium (11-50 people)',
    'High (51-100 people)',
    'Very High (100+ people)'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Sparkles className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Sanitization Scope</h3>
          <p className="text-gray-600">Define the areas requiring sanitization</p>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Areas to Sanitize <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {areas.map((area) => (
              <label
                key={area}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.areas.includes(area)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.areas.includes(area)}
                  onChange={(e) => {
                    const newAreas = e.target.checked
                      ? [...scope.areas, area]
                      : scope.areas.filter(a => a !== area);
                    onChange({ ...scope, areas: newAreas });
                  }}
                  className="sr-only"
                />
                <Target className={`w-5 h-5 mr-3 ${
                  scope.areas.includes(area) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{area}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            High-Touch Surfaces <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {highTouchSurfaces.map((surface) => (
              <label
                key={surface}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.highTouchSurfaces.includes(surface)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.highTouchSurfaces.includes(surface)}
                  onChange={(e) => {
                    const newSurfaces = e.target.checked
                      ? [...scope.highTouchSurfaces, surface]
                      : scope.highTouchSurfaces.filter(s => s !== surface);
                    onChange({ ...scope, highTouchSurfaces: newSurfaces });
                  }}
                  className="sr-only"
                />
                <Sparkles className={`w-5 h-5 mr-3 ${
                  scope.highTouchSurfaces.includes(surface) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{surface}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Occupancy Level <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Users className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={scope.occupancy}
                onChange={(e) => onChange({ ...scope, occupancy: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              >
                <option value="">Select occupancy level</option>
                {occupancyLevels.map((level) => (
                  <option key={level} value={level}>{level}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Operating Hours <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={scope.operatingHours}
                onChange={(e) => onChange({ ...scope, operatingHours: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="e.g., Mon-Fri 9AM-5PM"
                required
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
