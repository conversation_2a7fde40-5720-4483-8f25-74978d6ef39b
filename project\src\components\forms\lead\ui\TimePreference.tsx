import React from 'react';
import { Sun, Moon } from 'lucide-react';

const timeSlots = [
  { id: 'morning', label: 'Morning', icon: Sun, time: '8:00 AM - 12:00 PM' },
  { id: 'afternoon', label: 'Afternoon', icon: Sun, time: '12:00 PM - 4:00 PM' },
  { id: 'evening', label: 'Evening', icon: Moon, time: '4:00 PM - 8:00 PM' }
];

interface TimePreferenceProps {
  selected: string | null;
  onChange: (timeSlot: string) => void;
}

export function TimePreference({ selected, onChange }: TimePreferenceProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {timeSlots.map(({ id, label, icon: Icon, time }) => (
        <button
          key={id}
          onClick={() => onChange(id)}
          className={`p-4 rounded-lg border-2 transition-all ${
            selected === id
              ? 'border-green-500 bg-green-50'
              : 'border-gray-200 hover:border-green-300'
          }`}
        >
          <div className="flex flex-col items-center text-center">
            <Icon className={`w-6 h-6 mb-2 ${
              selected === id ? 'text-green-600' : 'text-gray-500'
            }`} />
            <span className="font-medium">{label}</span>
            <span className="text-sm text-gray-500">{time}</span>
          </div>
        </button>
      ))}
    </div>
  );
}
