import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, GlassWater, Layers, Sparkles, Wind, CheckCircle, AlertCircle
} from 'lucide-react';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';

// Data Interfaces
interface WindowFormData {
  servicePackage?: string;
  propertyType?: string;
  buildingHeight?: string;
  numberOfWindows?: number;
  windowTypes?: string[];
  squareFootage?: number;
  propertyAddress?: string;
  accessRequirements?: string;
  safetyEquipment?: string;
  parkingAvailable?: boolean;
  serviceFrequency?: string;
  preferredTime?: string;
  priorityAreas?: string[];
  additionalServices?: string[];
  specialInstructions?: string;
  startDate?: string;
  wantsRecurringContract?: boolean;
  contractLength?: string;
  budgetRange?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  jobTitle?: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  propertyAddress?: string;
  propertyType?: string;
  buildingHeight?: string;
  numberOfWindows?: string;
  serviceFrequency?: string;
  preferredTime?: string;
}

// Validation Utilities
const validateEmail = (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
const validatePhoneNumber = (phone: string) => /^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/.test(phone);
const formatPhoneNumber = (value: string) => {
  if (!value) return value;
  const phoneNumber = value.replace(/[^\d]/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) return phoneNumber;
  if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  }
  return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
};

// Form Constants
const steps = [
  { id: 1, name: 'Service' },
  { id: 2, name: 'Property' },
  { id: 3, name: 'Scope' },
  { id: 4, name: 'Add-ons' },
  { id: 5, name: 'Schedule' },
  { id: 6, name: 'Contact' },
];

const windowServices = [
  { id: 'interior-exterior', name: 'Interior & Exterior Cleaning', description: 'Complete window cleaning service.', icon: <GlassWater /> },
  { id: 'exterior-only', name: 'Exterior Only', description: 'Outside window cleaning service.', icon: <Building /> },
  { id: 'interior-only', name: 'Interior Only', description: 'Inside window cleaning service.', icon: <Layers /> },
  { id: 'high-rise', name: 'High-Rise Specialist', description: 'Professional high-rise window cleaning.', icon: <Wind /> },
  { id: 'maintenance-program', name: 'Maintenance Program', description: 'Regular window care schedule.', icon: <Sparkles /> },
];

const propertyTypes = [
  { id: 'office-building', name: 'Office Building' },
  { id: 'retail-store', name: 'Retail Store' },
  { id: 'restaurant', name: 'Restaurant/Cafe' },
  { id: 'hotel', name: 'Hotel/Hospitality' },
  { id: 'medical-facility', name: 'Medical Facility' },
  { id: 'school-university', name: 'School/University' },
  { id: 'showroom', name: 'Vehicle Showroom' },
  { id: 'warehouse', name: 'Warehouse/Industrial' },
  { id: 'other', name: 'Other' },
];

const buildingHeights = [
  { id: 'single-story', name: 'Single Story (1 floor)' },
  { id: 'low-rise', name: 'Low-Rise (2-4 floors)' },
  { id: 'mid-rise', name: 'Mid-Rise (5-12 floors)' },
  { id: 'high-rise', name: 'High-Rise (13+ floors)' },
];

const windowTypes = [
  { id: 'standard-glass', name: 'Standard Glass Windows' },
  { id: 'floor-ceiling', name: 'Floor-to-Ceiling Windows' },
  { id: 'storefront', name: 'Storefront Windows' },
  { id: 'skylights', name: 'Skylights' },
  { id: 'curtain-wall', name: 'Curtain Wall Systems' },
  { id: 'specialty-glass', name: 'Specialty Glass' },
];

const serviceFrequencies = [
  { id: 'one-time', name: 'One-Time Service' },
  { id: 'weekly', name: 'Weekly' },
  { id: 'bi-weekly', name: 'Bi-Weekly' },
  { id: 'monthly', name: 'Monthly' },
  { id: 'quarterly', name: 'Quarterly' },
  { id: 'custom', name: 'Custom Schedule' },
];

const preferredTimes = [
  { id: 'early-morning', name: 'Early Morning (6AM - 9AM)' },
  { id: 'morning', name: 'Morning (9AM - 12PM)' },
  { id: 'afternoon', name: 'Afternoon (12PM - 5PM)' },
  { id: 'after-hours', name: 'After Business Hours (Recommended)' },
];

const priorityAreas = [
  { id: 'storefront', name: 'Storefront Windows' },
  { id: 'lobby', name: 'Lobby/Reception Area' },
  { id: 'conference-rooms', name: 'Conference Rooms' },
  { id: 'executive-offices', name: 'Executive Offices' },
  { id: 'customer-facing', name: 'Customer-Facing Areas' },
  { id: 'high-traffic', name: 'High-Traffic Zones' },
];

const additionalServices = [
  { id: 'screen-cleaning', name: 'Window Screen Cleaning' },
  { id: 'frame-cleaning', name: 'Window Frame & Sill Cleaning' },
  { id: 'pressure-washing', name: 'Building Exterior Pressure Washing' },
  { id: 'gutter-cleaning', name: 'Gutter Cleaning Service' },
  { id: 'solar-panel', name: 'Solar Panel Cleaning' },
  { id: 'awning-cleaning', name: 'Awning & Canopy Cleaning' },
];

const BrandAlignedWindowForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<WindowFormData>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState<'success' | 'error' | null>(null);

  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        firstName: prev.firstName || user.user_metadata?.first_name,
        lastName: prev.lastName || user.user_metadata?.last_name,
        email: prev.email || user.email,
        phone: prev.phone || user.phone,
        companyName: prev.companyName || user.user_metadata?.company_name,
      }));
    }
  }, [user]);

  const validateField = (fieldName: keyof FormErrors, value: string | number | string[] | undefined): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value || typeof value !== 'string') return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value || typeof value !== 'string') return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
      case 'lastName':
      case 'companyName':
      case 'propertyAddress':
        if (!value || typeof value !== 'string' || value.trim().length < 2) return `${fieldName.replace(/([A-Z])/g, ' $1')} must be at least 2 characters`;
        break;
      case 'propertyType':
      case 'buildingHeight':
        if (!value || typeof value !== 'string') return `Please select a ${fieldName.replace(/([A-Z])/g, ' $1').toLowerCase()}`;
        break;
      case 'numberOfWindows':
        if (!value || typeof value !== 'number' || value < 1) return 'Number of windows must be at least 1';
        break;
      case 'serviceFrequency':
        if (!value || typeof value !== 'string') return 'Please select a service frequency';
        break;
      case 'preferredTime':
        if (!value || typeof value !== 'string') return 'Please select a preferred time';
        break;
    }
    return undefined;
  };

  const handleInputChange = (field: keyof WindowFormData, value: string | number | boolean | string[] | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (Object.keys(formErrors).includes(field) && field in ({} as FormErrors)) {
      if (typeof value !== 'boolean') {
        const error = validateField(field as keyof FormErrors, value as string | number | string[] | undefined);
        setFormErrors(prev => ({ ...prev, [field]: error }));
      }
    }
  };
  
  const handleNextStep = () => {
    let isValid = true;
    const newErrors: FormErrors = {};

    if (currentStep === 2) {
        ['propertyType', 'buildingHeight', 'numberOfWindows', 'propertyAddress'].forEach(field => {
            const value = formData[field as keyof WindowFormData];
            if (typeof value !== 'boolean') {
                const error = validateField(field as keyof FormErrors, value);
                if (error) {
                    newErrors[field as keyof FormErrors] = error;
                    isValid = false;
                }
            }
        });
    } else if (currentStep === 3) {
        ['serviceFrequency', 'preferredTime'].forEach(field => {
            const value = formData[field as keyof WindowFormData];
            if (typeof value !== 'boolean') {
                const error = validateField(field as keyof FormErrors, value);
                if (error) {
                    newErrors[field as keyof FormErrors] = error;
                    isValid = false;
                }
            }
        });
    } else if (currentStep === 6) {
        ['firstName', 'lastName', 'email', 'phone', 'companyName'].forEach(field => {
            const value = formData[field as keyof WindowFormData];
            if (typeof value !== 'boolean') {
                const error = validateField(field as keyof FormErrors, value);
                if (error) {
                    newErrors[field as keyof FormErrors] = error;
                    isValid = false;
                }
            }
        });
    }

    setFormErrors(newErrors);

    if (isValid) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigate('/commercial');
    }
  };
  
  const handleSubmit = async () => {
    setIsSubmitting(true);
    const newErrors: FormErrors = {};
    ['firstName', 'lastName', 'email', 'phone', 'companyName', 'propertyAddress', 'propertyType', 'buildingHeight', 'numberOfWindows', 'serviceFrequency', 'preferredTime'].forEach(field => {
        const value = formData[field as keyof WindowFormData];
        if (typeof value !== 'boolean') {
            const error = validateField(field as keyof FormErrors, value);
            if (error) {
                newErrors[field as keyof FormErrors] = error;
            }
        }
    });
     if (Object.keys(newErrors).length > 0) {
        setFormErrors(newErrors);
        setIsSubmitting(false);
        if (newErrors.propertyType || newErrors.buildingHeight || newErrors.numberOfWindows || newErrors.propertyAddress) setCurrentStep(2);
        else if (newErrors.serviceFrequency || newErrors.preferredTime) setCurrentStep(3);
        else if (newErrors.firstName || newErrors.lastName || newErrors.email || newErrors.phone || newErrors.companyName) setCurrentStep(6);
        return;
    }

    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      const submissionData = {
        user_id: user?.id || null,
        service_type: 'window',
        status: 'pending',
        property_details: {
          propertyType: formData.propertyType,
          propertyAddress: formData.propertyAddress,
          buildingHeight: formData.buildingHeight,
          numberOfWindows: formData.numberOfWindows,
          windowTypes: formData.windowTypes,
          squareFootage: formData.squareFootage,
          accessRequirements: formData.accessRequirements,
          safetyEquipment: formData.safetyEquipment,
          parkingAvailable: formData.parkingAvailable,
        },
        service_details: {
          servicePackage: formData.servicePackage,
          serviceFrequency: formData.serviceFrequency,
          preferredTime: formData.preferredTime,
          priorityAreas: formData.priorityAreas,
          additionalServices: formData.additionalServices,
          specialInstructions: formData.specialInstructions,
          budgetRange: formData.budgetRange,
        },
        schedule: {
          startDate: formData.startDate,
          wantsRecurringContract: formData.wantsRecurringContract,
          contractLength: formData.contractLength,
        },
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle,
        },
      };

      console.log('Submitting window cleaning request:', submissionData);

      const { data, error } = await supabase
        .from('booking_forms')
        .insert([submissionData])
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Successfully submitted:', data);
      setSubmissionStatus('success');
      
      localStorage.removeItem('pending_window_request');
      
      setTimeout(() => {
        if (user) {
          navigate('/accountdashboard');
        } else {
          localStorage.setItem('redirectAfterLogin', '/accountdashboard');
          navigate('/auth/login');
        }
      }, 2000);
    } catch (error) {
      console.error('Submission Error:', error);
      
      try {
        const fallbackData = {
          timestamp: new Date().toISOString(),
          formData: formData,
          type: 'window_request'
        };
        localStorage.setItem('pending_window_request', JSON.stringify(fallbackData));
        console.log('Saved request to localStorage for later retry');
      } catch (storageError) {
        console.error('Failed to save to localStorage:', storageError);
      }
      
      setSubmissionStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Continue with renderStepContent and rest of component...
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div key="step1" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Select Your Window Service</h2>
            <p className="text-gray-300 mb-6">Choose the window cleaning service that best fits your needs.</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {windowServices.map((service) => (
                <motion.div
                  key={service.id}
                  whileHover={{ scale: 1.05, boxShadow: '0px 10px 30px rgba(0, 255, 135, 0.15)' }}
                  className={`p-6 rounded-2xl border transition-all duration-300 cursor-pointer
                    ${formData.servicePackage === service.id
                      ? 'bg-green-500/10 border-green-400'
                      : 'bg-white/5 border-white/20 hover:border-green-400/50'
                    }`}
                  onClick={() => handleInputChange('servicePackage', service.id)}
                >
                  <div className="flex items-center gap-4 mb-3">
                    <div className="text-green-400">{service.icon}</div>
                    <h3 className="font-semibold text-white text-lg">{service.name}</h3>
                  </div>
                  <p className="text-gray-300 text-sm">{service.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Property & Window Details</h2>
            <p className="text-gray-300 mb-6">Tell us about your building and windows.</p>
            
            <div className="space-y-8">
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Property Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Property Type *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {propertyTypes.slice(0, 6).map((type) => (
                        <motion.div
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.propertyType === type.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('propertyType', type.id)}
                        >
                          <Building className="w-4 h-4 mx-auto mb-1 text-green-400" />
                          <span className="text-xs font-medium text-white">{type.name}</span>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.propertyType && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.propertyType}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Property Address *</label>
                      <input
                        type="text"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.propertyAddress ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="Enter property address"
                        value={formData.propertyAddress || ''}
                        onChange={(e) => handleInputChange('propertyAddress', e.target.value)}
                      />
                      {formErrors.propertyAddress && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.propertyAddress}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Building Height *</label>
                      <select
                        className={`w-full p-3 rounded-xl bg-slate-900 border text-white
                          ${formErrors.buildingHeight ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        value={formData.buildingHeight || ''}
                        onChange={(e) => handleInputChange('buildingHeight', e.target.value)}
                        style={{
                          backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                          backgroundPosition: 'right 0.5rem center',
                          backgroundRepeat: 'no-repeat',
                          backgroundSize: '1.5em 1.5em',
                          paddingRight: '2.5rem'
                        }}
                      >
                        <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                          Select building height
                        </option>
                        {buildingHeights.map((height) => (
                          <option key={height.id} value={height.id} style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                            {height.name}
                          </option>
                        ))}
                      </select>
                      {formErrors.buildingHeight && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.buildingHeight}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Window Details</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Number of Windows *</label>
                      <input
                        type="number"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.numberOfWindows ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="e.g., 50"
                        min="1"
                        value={formData.numberOfWindows || ''}
                        onChange={(e) => handleInputChange('numberOfWindows', +e.target.value)}
                      />
                      {formErrors.numberOfWindows && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.numberOfWindows}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Total Square Footage</label>
                      <input
                        type="number"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., 5000"
                        min="100"
                        value={formData.squareFootage || ''}
                        onChange={(e) => handleInputChange('squareFootage', +e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Window Types (Select all that apply)</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {windowTypes.map((type) => (
                        <motion.div
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.windowTypes?.includes(type.id)
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => {
                            const currentTypes = formData.windowTypes || [];
                            const newTypes = currentTypes.includes(type.id)
                              ? currentTypes.filter(t => t !== type.id)
                              : [...currentTypes, type.id];
                            handleInputChange('windowTypes', newTypes);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <div className={`w-4 h-4 rounded border-2 flex items-center justify-center
                              ${formData.windowTypes?.includes(type.id)
                                ? 'bg-green-400 border-green-400'
                                : 'border-white/30'
                              }`}>
                              {formData.windowTypes?.includes(type.id) && (
                                <CheckCircle className="w-2 h-2 text-white" />
                              )}
                            </div>
                            <span className="text-sm font-medium text-white">{type.name}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div key="step3" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Service Requirements</h2>
            <p className="text-gray-300 mb-6">Let us know your scheduling and access requirements.</p>
            
            <div className="space-y-8">
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Service Schedule</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Service Frequency *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {serviceFrequencies.map((freq) => (
                        <motion.div
                          key={freq.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.serviceFrequency === freq.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('serviceFrequency', freq.id)}
                        >
                          <div className="text-sm font-medium text-white">{freq.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.serviceFrequency && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.serviceFrequency}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Preferred Service Time *</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {preferredTimes.map((time) => (
                        <motion.div
                          key={time.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.preferredTime === time.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('preferredTime', time.id)}
                        >
                          <div className="text-sm font-medium text-white">{time.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.preferredTime && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.preferredTime}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Priority Areas</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">Focus Areas (Optional)</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {priorityAreas.map((area) => (
                      <motion.div
                        key={area.id}
                        whileHover={{ scale: 1.02 }}
                        className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.priorityAreas?.includes(area.id)
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => {
                          const currentAreas = formData.priorityAreas || [];
                          const newAreas = currentAreas.includes(area.id)
                            ? currentAreas.filter(a => a !== area.id)
                            : [...currentAreas, area.id];
                          handleInputChange('priorityAreas', newAreas);
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-4 h-4 rounded border-2 flex items-center justify-center
                            ${formData.priorityAreas?.includes(area.id)
                              ? 'bg-green-400 border-green-400'
                              : 'border-white/30'
                            }`}>
                            {formData.priorityAreas?.includes(area.id) && (
                              <CheckCircle className="w-2 h-2 text-white" />
                            )}
                          </div>
                          <span className="text-sm font-medium text-white">{area.name}</span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Access & Safety Requirements</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Access Requirements</label>
                      <input
                        type="text"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., Key card access required"
                        value={formData.accessRequirements || ''}
                        onChange={(e) => handleInputChange('accessRequirements', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Safety Equipment Needed</label>
                      <input
                        type="text"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., Harnesses, lifts required"
                        value={formData.safetyEquipment || ''}
                        onChange={(e) => handleInputChange('safetyEquipment', e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Parking Available for Service Vehicles?</label>
                    <div className="flex gap-4">
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.parkingAvailable === true
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => handleInputChange('parkingAvailable', true)}
                      >
                        <div className="text-center text-sm font-medium text-white">Yes</div>
                      </motion.div>
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.parkingAvailable === false
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => handleInputChange('parkingAvailable', false)}
                      >
                        <div className="text-center text-sm font-medium text-white">No</div>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div key="step4" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Additional Services</h2>
            <p className="text-gray-300 mb-6">Select any additional services you'd like to include.</p>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Add-On Services (Optional)</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {additionalServices.map((service) => (
                    <motion.div
                      key={service.id}
                      whileHover={{ scale: 1.02 }}
                      className={`p-4 rounded-xl border cursor-pointer transition-all duration-200
                        ${formData.additionalServices?.includes(service.id)
                          ? 'bg-green-500/10 border-green-400'
                          : 'bg-white/5 border-white/20 hover:border-green-400/50'
                        }`}
                      onClick={() => {
                        const currentServices = formData.additionalServices || [];
                        const newServices = currentServices.includes(service.id)
                          ? currentServices.filter(s => s !== service.id)
                          : [...currentServices, service.id];
                        handleInputChange('additionalServices', newServices);
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center
                          ${formData.additionalServices?.includes(service.id)
                            ? 'bg-green-400 border-green-400'
                            : 'border-white/30'
                          }`}>
                          {formData.additionalServices?.includes(service.id) && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                        <span className="text-sm font-medium text-white">{service.name}</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Special Instructions</label>
                <textarea
                  className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                    focus:border-green-400 focus:outline-none transition-colors resize-none"
                  rows={4}
                  placeholder="Any special requirements, access codes, or additional notes..."
                  value={formData.specialInstructions || ''}
                  onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                />
              </div>
            </div>
          </motion.div>
        );

      case 5:
        return (
          <motion.div key="step5" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Schedule & Contract</h2>
            <p className="text-gray-300 mb-6">Set your preferences for scheduling and contract terms.</p>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Start Date</label>
                  <input
                    type="date"
                    className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white
                      focus:border-green-400 focus:outline-none transition-colors"
                    value={formData.startDate || ''}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Budget Range</label>
                  <select
                    className="w-full p-3 rounded-xl bg-slate-900 border border-white/20 text-white
                      focus:border-green-400 focus:outline-none transition-colors"
                    value={formData.budgetRange || ''}
                    onChange={(e) => handleInputChange('budgetRange', e.target.value)}
                    style={{
                      backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                      backgroundPosition: 'right 0.5rem center',
                      backgroundRepeat: 'no-repeat',
                      backgroundSize: '1.5em 1.5em',
                      paddingRight: '2.5rem'
                    }}
                  >
                    <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                      Select budget range
                    </option>
                    <option value="under-500" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>Under $500</option>
                    <option value="500-1000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>$500 - $1,000</option>
                    <option value="1000-2500" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>$1,000 - $2,500</option>
                    <option value="2500-5000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>$2,500 - $5,000</option>
                    <option value="over-5000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>Over $5,000</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Recurring Service Contract</label>
                <div className="flex gap-4 mb-4">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                      ${formData.wantsRecurringContract === true
                        ? 'bg-green-500/10 border-green-400'
                        : 'bg-white/5 border-white/20 hover:border-green-400/50'
                      }`}
                    onClick={() => handleInputChange('wantsRecurringContract', true)}
                  >
                    <div className="text-center text-sm font-medium text-white">Yes, set up recurring service</div>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                      ${formData.wantsRecurringContract === false
                        ? 'bg-green-500/10 border-green-400'
                        : 'bg-white/5 border-white/20 hover:border-green-400/50'
                      }`}
                    onClick={() => handleInputChange('wantsRecurringContract', false)}
                  >
                    <div className="text-center text-sm font-medium text-white">No, one-time service</div>
                  </motion.div>
                </div>

                {formData.wantsRecurringContract && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Contract Length</label>
                    <select
                      className="w-full p-3 rounded-xl bg-slate-900 border border-white/20 text-white
                        focus:border-green-400 focus:outline-none transition-colors"
                      value={formData.contractLength || ''}
                      onChange={(e) => handleInputChange('contractLength', e.target.value)}
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.5rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em',
                        paddingRight: '2.5rem'
                      }}
                    >
                      <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                        Select contract length
                      </option>
                      <option value="3-months" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>3 Months</option>
                      <option value="6-months" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>6 Months</option>
                      <option value="1-year" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>1 Year</option>
                      <option value="2-years" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>2 Years</option>
                    </select>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        );

      case 6:
        return (
          <motion.div key="step6" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
            <p className="text-gray-300 mb-6">Please provide your contact details to complete the request.</p>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                  <input
                    type="text"
                    className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.firstName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your first name"
                    value={formData.firstName || ''}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                  />
                  {formErrors.firstName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.firstName}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                  <input
                    type="text"
                    className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.lastName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your last name"
                    value={formData.lastName || ''}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                  />
                  {formErrors.lastName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.lastName}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                  <input
                    type="email"
                    className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.email ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your email"
                    value={formData.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                  />
                  {formErrors.email && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.email}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                  <input
                    type="tel"
                    className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.phone ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="(*************"
                    value={formData.phone || ''}
                    onChange={(e) => handleInputChange('phone', formatPhoneNumber(e.target.value))}
                  />
                  {formErrors.phone && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.phone}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Company Name *</label>
                  <input
                    type="text"
                    className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.companyName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your company name"
                    value={formData.companyName || ''}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                  />
                  {formErrors.companyName && (
                    <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                      <AlertCircle className="w-3 h-3" />
                      {formErrors.companyName}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Job Title</label>
                  <input
                    type="text"
                    className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                      focus:border-green-400 focus:outline-none transition-colors"
                    placeholder="Enter your job title"
                    value={formData.jobTitle || ''}
                    onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        );

      default:
        return (
          <motion.div key={`step${currentStep}`} initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Step {currentStep}</h2>
            <p className="text-gray-300 mb-6">Step content coming soon...</p>
          </motion.div>
        );
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-4xl">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-2xl font-bold text-white">Commercial Window Cleaning</h1>
              <span className="text-sm text-gray-400">Step {currentStep} of {steps.length}</span>
            </div>
            
            <div className="relative">
              <div className="flex items-center justify-between mb-2">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300
                      ${currentStep > index + 1 ? 'bg-green-500 text-white' : 
                        currentStep === index + 1 ? 'bg-white text-gray-900' : 'bg-gray-600 text-gray-300'}`}>
                      {currentStep > index + 1 ? <CheckCircle className="w-4 h-4" /> : index + 1}
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`h-1 w-16 mx-2 transition-all duration-300
                        ${currentStep > index + 1 ? 'bg-green-500' : 'bg-gray-600'}`} />
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between text-xs text-gray-400">
                {steps.map((step) => (
                  <span key={step.id} className="w-8 text-center">{step.name}</span>
                ))}
              </div>
            </div>
          </div>

          {/* Form Content */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-8 shadow-2xl">
            <AnimatePresence mode="wait">
              {renderStepContent()}
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8 pt-6 border-t border-white/20">
              <button
                onClick={handlePrevStep}
                className="px-6 py-3 text-white bg-white/10 border border-white/20 rounded-xl hover:bg-white/20 transition-all duration-200"
              >
                {currentStep === 1 ? 'Back to Services' : 'Previous'}
              </button>
              
              {currentStep === steps.length ? (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-all duration-200 disabled:opacity-50"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Request'}
                </button>
              ) : (
                <button
                  onClick={handleNextStep}
                  className="px-8 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-all duration-200"
                >
                  Next Step
                </button>
              )}
            </div>
          </div>

          {/* Success/Error Messages */}
          <AnimatePresence>
            {submissionStatus && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`mt-6 p-4 rounded-xl ${submissionStatus === 'success' ? 'bg-green-500/20 border border-green-500/30' : 'bg-red-500/20 border border-red-500/30'}`}
              >
                <p className={`text-center ${submissionStatus === 'success' ? 'text-green-400' : 'text-red-400'}`}>
                  {submissionStatus === 'success' 
                    ? 'Request submitted successfully! Redirecting to dashboard...' 
                    : 'There was an error submitting your request. Please try again.'}
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedWindowForm; 
