import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building2, Sparkles, GlassWater, Brush, Construction, 
  Sprout, Grid, Waves, Hammer, ArrowRight 
} from 'lucide-react';
import { navigateToServiceForm } from '../../../lib/utils/navigation';

const solutions = [
  {
    id: 'office',
    icon: Building2,
    title: 'Office Cleaning',
    description: 'Professional cleaning solutions for offices of all sizes',
    features: ['Daily/Weekly Service', 'Eco-Friendly Products', 'Trained Staff'],
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 'deep',
    icon: Sparkles,
    title: 'Deep Cleaning',
    description: 'Thorough cleaning and sanitization services',
    features: ['Complete Sanitization', 'Deep Scrubbing', 'Air Purification'],
    color: 'from-purple-500 to-purple-600'
  },
  {
    id: 'window',
    icon: GlassWater,
    title: 'Window Cleaning',
    description: 'Professional window cleaning for commercial buildings',
    features: ['Interior & Exterior', 'High-Rise Capable', 'Streak-Free Results'],
    color: 'from-cyan-500 to-cyan-600'
  },
  {
    id: 'carpet',
    icon: Brush,
    title: 'Carpet Cleaning',
    description: 'Deep carpet cleaning and stain removal',
    features: ['Deep Extraction', 'Stain Treatment', 'Deodorizing'],
    color: 'from-amber-500 to-amber-600'
  },
  {
    id: 'post-construction',
    icon: Construction,
    title: 'Post-Construction',
    description: 'Detailed cleaning after construction or renovation',
    features: ['Debris Removal', 'Dust Control', 'Final Inspection'],
    color: 'from-orange-500 to-orange-600'
  },
  {
    id: 'sanitization',
    icon: Sprout,
    title: 'Sanitization',
    description: 'Commercial-grade sanitization and disinfection',
    features: ['Medical-Grade Products', 'High-Touch Surfaces', 'EPA Approved'],
    color: 'from-green-500 to-green-600'
  },
  {
    id: 'tile',
    icon: Grid,
    title: 'Tile & Grout',
    description: 'Professional tile and grout cleaning services',
    features: ['Deep Cleaning', 'Grout Sealing', 'Color Restoration'],
    color: 'from-teal-500 to-teal-600'
  },
  {
    id: 'pressure',
    icon: Waves,
    title: 'Pressure Washing',
    description: 'High-pressure cleaning for exterior surfaces',
    features: ['Building Exterior', 'Concrete & Pavement', 'Graffiti Removal'],
    color: 'from-sky-500 to-sky-600'
  },
  {
    id: 'floor',
    icon: Hammer,
    title: 'Floor Restoration',
    description: 'Stripping, sealing, waxing, or refinishing floors',
    features: ['Stripping & Waxing', 'Buffing & Polishing', 'Sealing'],
    color: 'from-rose-500 to-rose-600'
  }
];

export function SolutionsGrid() {
  const navigate = useNavigate();

  const handleSolutionSelect = (solutionId: string) => {
    navigateToServiceForm(navigate, solutionId);
  };

  return (
    <section id="solutions-grid" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Our Professional Solutions
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Choose from our comprehensive range of cleaning solutions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {solutions.map((solution, index) => {
            const Icon = solution.icon;
            
            return (
              <motion.div
                key={solution.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <button
                  onClick={() => handleSolutionSelect(solution.id)}
                  className="w-full text-left"
                >
                  <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                    {/* Gradient Header */}
                    <div className={`p-6 bg-gradient-to-r ${solution.color}`}>
                      <div className="flex items-center justify-between">
                        <Icon className="w-8 h-8 text-white" />
                        <motion.div 
                          className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center"
                          whileHover={{ rotate: 90 }}
                        >
                          <ArrowRight className="w-4 h-4 text-white" />
                        </motion.div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {solution.title}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {solution.description}
                      </p>

                      {/* Features */}
                      <div className="space-y-2">
                        {solution.features.map((feature, i) => (
                          <motion.div 
                            key={i}
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.2 + (i * 0.1) }}
                            className="flex items-center text-gray-600"
                          >
                            <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                            <span className="text-sm">{feature}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="px-6 py-4 bg-gray-50 group-hover:bg-brand-50 transition-colors">
                      <div className="flex items-center justify-between text-brand-600">
                        <span className="font-medium">Learn More</span>
                        <motion.div
                          whileHover={{ x: 4 }}
                          className="transition-transform"
                        >
                          <ArrowRight className="w-5 h-5" />
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </button>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
