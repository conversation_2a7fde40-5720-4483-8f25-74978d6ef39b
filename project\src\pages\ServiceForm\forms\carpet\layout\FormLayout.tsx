import React from 'react';
import { FormHeader } from './FormHeader';
import { OrderSummary } from '../components/OrderSummary';
import { Testimonials } from '../components/Testimonials';
import type { FormData } from '../types';

interface FormLayoutProps {
  children: React.ReactNode;
  formData: FormData;
  onBack: () => void;
}

export function FormLayout({ children, formData, onBack }: FormLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <FormHeader onBack={onBack} />
        
        <div className="mt-8 grid grid-cols-1 lg:grid-cols-[1fr,400px] gap-8">
          {/* Form Column */}
          <div className="bg-white rounded-2xl shadow-sm p-8 lg:p-12">
            {children}
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            <OrderSummary formData={formData} />
            <Testimonials />
          </div>
        </div>
      </div>
    </div>
  );
}
