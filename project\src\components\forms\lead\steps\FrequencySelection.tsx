import React from 'react';
import { Calendar, Clock, CalendarDays, CalendarRange } from 'lucide-react';
import { FrequencyCard } from '../ui/FrequencyCard';

const frequencies = [
  {
    id: 'daily',
    icon: Clock,
    label: 'Daily',
    description: 'Regular daily cleaning service'
  },
  {
    id: 'weekly',
    icon: Calendar,
    label: 'Weekly',
    description: 'Once per week cleaning'
  },
  {
    id: 'biweekly',
    icon: CalendarDays,
    label: 'Bi-Weekly',
    description: 'Every two weeks'
  },
  {
    id: 'monthly',
    icon: CalendarRange,
    label: 'Monthly',
    description: 'Once per month service'
  }
];

interface FrequencySelectionProps {
  selected: string | null;
  onChange: (frequency: string) => void;
}

export function FrequencySelection({ selected, onChange }: FrequencySelectionProps) {
  return (
    <div>
      <h2 className="text-2xl font-medium mb-6">Choose Service Frequency</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {frequencies.map((frequency) => (
          <FrequencyCard
            key={frequency.id}
            frequency={frequency}
            selected={selected === frequency.id}
            onClick={() => onChange(frequency.id)}
          />
        ))}
      </div>
    </div>
  );
}
