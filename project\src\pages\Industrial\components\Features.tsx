import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Clock, Leaf, Users, HeartHandshake, Zap } from 'lucide-react';

const features = [
  {
    icon: Shield,
    title: 'OSHA Compliant',
    description: 'All cleaning procedures follow strict safety protocols',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: Clock,
    title: '24/7 Availability',
    description: 'Round-the-clock service to fit your schedule',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: Leaf,
    title: 'Eco-Friendly',
    description: 'Industrial-grade green cleaning solutions',
    color: 'from-teal-500 to-teal-600'
  },
  {
    icon: Users,
    title: 'Trained Staff',
    description: 'Safety-certified cleaning professionals',
    color: 'from-purple-500 to-purple-600'
  },
  {
    icon: HeartHandshake,
    title: 'Satisfaction Guaranteed',
    description: '100% satisfaction or we\'ll make it right',
    color: 'from-pink-500 to-pink-600'
  },
  {
    icon: Zap,
    title: 'Fast Response',
    description: 'Quick response for emergency situations',
    color: 'from-yellow-500 to-yellow-600'
  }
];

export function Features() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-gray-900 mb-4"
          >
            Why Choose Us
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Experience the difference with our industrial cleaning services
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="group relative"
              >
                <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                  {/* Gradient Background */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-0 
                                group-hover:opacity-5 rounded-2xl transition-opacity`} />
                  
                  {/* Content */}
                  <div className="relative">
                    <div className={`inline-flex items-center justify-center p-3 rounded-xl 
                                  bg-gradient-to-br ${feature.color} mb-6 group-hover:scale-110 
                                  transition-transform`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
