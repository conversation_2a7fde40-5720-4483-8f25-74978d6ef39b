import React from 'react';
import { Sofa, Droplets, AlertCircle, <PERSON>, Dog } from 'lucide-react';
import { motion } from 'framer-motion';

interface UpholsteryDetailsProps {
  details: {
    furnitureType: string[];
    pieceCount: number;
    material: string;
    stains: boolean;
    odors: boolean;
    lastCleaning: string;
    petPresence: boolean;
  };
  onChange: (details: any) => void;
}

export function UpholsteryDetails({ details, onChange }: UpholsteryDetailsProps) {
  const furnitureTypes = [
    'Sofa',
    'Loveseat',
    'Sectional',
    'Chair',
    'Ottoman',
    'Recliner',
    'Dining Chair',
    'Mattress'
  ];

  const materials = [
    'Fabric',
    'Leather',
    'Microfiber',
    'Suede',
    'Velvet',
    'Cotton',
    'Linen',
    'Polyester',
    'Other'
  ];

  const lastCleaningOptions = [
    'Never',
    'Within last 6 months',
    'Within last year',
    '1-2 years ago',
    '3+ years ago',
    'Unknown'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Sofa className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Upholstery Details</h3>
          <p className="text-gray-600">Tell us about your furniture</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            What type of furniture needs cleaning? <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {furnitureTypes.map((type) => (
              <label
                key={type}
                className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  details.furnitureType.includes(type)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.furnitureType.includes(type)}
                  onChange={(e) => {
                    const newTypes = e.target.checked
                      ? [...details.furnitureType, type]
                      : details.furnitureType.filter(t => t !== type);
                    onChange({ ...details, furnitureType: newTypes });
                  }}
                  className="sr-only"
                />
                <Sofa className={`w-5 h-5 mr-2 ${
                  details.furnitureType.includes(type) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm">{type}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              How many pieces need cleaning? <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              value={details.pieceCount}
              onChange={(e) => onChange({ ...details, pieceCount: Number(e.target.value) })}
              min="1"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What material is the upholstery? <span className="text-red-500">*</span>
            </label>
            <select
              value={details.material}
              onChange={(e) => onChange({ ...details, material: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select material</option>
              {materials.map((material) => (
                <option key={material} value={material}>{material}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              When was the last professional cleaning?
            </label>
            <select
              value={details.lastCleaning}
              onChange={(e) => onChange({ ...details, lastCleaning: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            >
              <option value="">Select option</option>
              {lastCleaningOptions.map((option) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <label
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              details.stains
                ? 'border-brand-500 bg-brand-50'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={details.stains}
              onChange={(e) => onChange({ ...details, stains: e.target.checked })}
              className="sr-only"
            />
            <Droplets className={`w-5 h-5 mr-3 ${
              details.stains ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Visible stains present</span>
          </label>

          <label
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              details.odors
                ? 'border-brand-500 bg-brand-50'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={details.odors}
              onChange={(e) => onChange({ ...details, odors: e.target.checked })}
              className="sr-only"
            />
            <AlertCircle className={`w-5 h-5 mr-3 ${
              details.odors ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Odors present</span>
          </label>

          <label
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              details.petPresence
                ? 'border-brand-500 bg-brand-50'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={details.petPresence}
              onChange={(e) => onChange({ ...details, petPresence: e.target.checked })}
              className="sr-only"
            />
            <Dog className={`w-5 h-5 mr-3 ${
              details.petPresence ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Pets in the home</span>
          </label>
        </div>

        {details.stains && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Stain Treatment Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Our technicians will assess the stains during service. Some stains may require special treatment at additional cost.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
