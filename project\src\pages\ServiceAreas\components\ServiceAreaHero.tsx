import React from 'react';
import { motion } from 'framer-motion';
import { Building2, Shield, Star, Users, MapPin, ArrowRight } from 'lucide-react';
import { Button } from '../../../components/ui/Button';

export function ServiceAreaHero() {
  return (
    <section className="relative min-h-[90vh] flex items-center pt-20">
      {/* Enhanced Background with Multiple Layers */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Base Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-900/95 via-brand-800/90 to-brand-700/85" />
        
        {/* Animated Wave Background */}
        <div className="absolute inset-0">
          <div className="absolute bottom-0 left-0 right-0 h-64">
            <div className="absolute inset-0 w-[200%] animate-wave">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-brand-200/20">
                <path d="M0,50 Q25,60 50,50 Q75,40 100,50 L100,100 L0,100 Z" />
              </svg>
            </div>
            <div className="absolute inset-0 w-[200%] animate-wave-slow">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-brand-300/20">
                <path d="M0,60 Q25,50 50,60 Q75,70 100,60 L100,100 L0,100 Z" />
              </svg>
            </div>
            <div className="absolute inset-0 w-[200%] animate-wave-slower">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-brand-400/20">
                <path d="M0,70 Q25,60 50,70 Q75,80 100,70 L100,100 L0,100 Z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Floating Map Pins */}
        {[...Array(5)].map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 50 }}
            animate={{ 
              opacity: 0.2,
              y: [0, -20, 0],
              transition: {
                y: {
                  repeat: Infinity,
                  duration: 3,
                  delay: index * 0.5,
                  ease: "easeInOut"
                }
              }
            }}
            className="absolute"
            style={{
              left: `${20 + (index * 15)}%`,
              top: `${30 + (index * 10)}%`
            }}
          >
            <MapPin className="w-12 h-12 text-brand-200" />
          </motion.div>
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="inline-flex items-center justify-center p-3 bg-brand-500/20 backdrop-blur-sm rounded-xl mb-6"
            >
              <MapPin className="w-6 h-6 text-brand-100" />
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6 leading-tight"
            >
              Nationwide Service Areas
              <span className="block text-brand-200">Coast to Coast Coverage</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-xl md:text-2xl text-brand-100 mb-8"
            >
              Professional cleaning services across 8 states: NY, NJ, PA, NC, VA, FL, TX, and CA
            </motion.p>

            {/* Trust Badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              {[
                { icon: Building2, text: '1,400+ Buildings Serviced' },
                { icon: Shield, text: 'Licensed & Insured' },
                { icon: Star, text: '4.9/5 Rating' },
                { icon: Users, text: '200+ Team Members' }
              ].map((badge, index) => (
                <motion.div
                  key={badge.text}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 + (index * 0.1) }}
                  whileHover={{ scale: 1.05 }}
                  className="bg-brand-500/20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center space-x-2
                           border border-brand-400/20 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <badge.icon className="w-4 h-4 text-brand-100" />
                  <span className="text-sm text-white">{badge.text}</span>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="flex flex-col sm:flex-row items-center justify-center gap-4"
            >
              <Button
                size="lg"
                className="bg-white text-brand-600 hover:bg-brand-50 w-full sm:w-auto"
              >
                Check Availability
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-brand-500/20 w-full sm:w-auto"
              >
                View Service Areas
              </Button>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
