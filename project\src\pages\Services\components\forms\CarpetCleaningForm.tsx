import React from 'react';
import { Form, FormField } from './FormFields';

interface CarpetCleaningFormProps {
  onSubmit: (data: any) => void;
}

export function CarpetCleaningForm({ onSubmit }: CarpetCleaningFormProps) {
  return (
    <Form onSubmit={onSubmit}>
      <FormField label="Approximate Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Carpet Type" required>
        <select
          name="carpetType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="commercial">Commercial</option>
          <option value="berber">Berber</option>
          <option value="plush">Plush</option>
          <option value="other">Other</option>
        </select>
      </FormField>

      <FormField label="Service Type" required>
        <select
          name="serviceType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="standard">Standard Cleaning</option>
          <option value="deep">Deep Cleaning</option>
          <option value="stain">Stain Treatment</option>
          <option value="deodorizing">Deodorizing</option>
        </select>
      </FormField>

      <FormField label="High Traffic Areas">
        <textarea
          name="highTrafficAreas"
          rows={2}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Describe any high traffic areas that need special attention"
        />
      </FormField>

      <FormField label="Additional Notes">
        <textarea
          name="notes"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any special requirements or concerns?"
        />
      </FormField>
    </Form>
  );
}
