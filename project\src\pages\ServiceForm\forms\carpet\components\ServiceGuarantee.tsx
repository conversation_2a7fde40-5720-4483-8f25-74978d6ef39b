import React from 'react';
import { Shield, CheckCircle } from 'lucide-react';

export function ServiceGuarantee() {
  return (
    <div className="bg-brand-50/80 backdrop-blur rounded-xl p-6 border border-brand-100">
      <div className="flex items-center space-x-3 mb-4">
        <Shield className="w-6 h-6 text-brand-600" />
        <h3 className="text-lg font-semibold text-gray-900">Our Guarantee</h3>
      </div>

      <ul className="space-y-3">
        {[
          '100% Satisfaction Guaranteed',
          'Expert Technicians',
          'Eco-Friendly Products',
          'On-Time Service'
        ].map((item) => (
          <li key={item} className="flex items-center space-x-2 text-gray-700">
            <CheckCircle className="w-4 h-4 text-brand-600 flex-shrink-0" />
            <span>{item}</span>
          </li>
        ))}
      </ul>
    </div>
  );
}
