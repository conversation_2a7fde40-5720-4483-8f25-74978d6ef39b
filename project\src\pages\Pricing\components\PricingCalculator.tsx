import React, { useState } from 'react';
import { Calculator } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { calculatePrice } from '../utils/pricing';
import type { CalculatorInputs } from '../types';

const frequencies = [
  { id: 'daily', label: 'Daily', multiplier: 0.8 },
  { id: 'weekly', label: 'Weekly', multiplier: 1 },
  { id: 'biweekly', label: 'Bi-Weekly', multiplier: 1.2 },
  { id: 'monthly', label: 'Monthly', multiplier: 1.5 }
];

const serviceTypes = [
  { id: 'office', label: 'Office Cleaning', baseRate: 0.15 },
  { id: 'medical', label: 'Medical Facility', baseRate: 0.25 },
  { id: 'industrial', label: 'Industrial Space', baseRate: 0.20 },
  { id: 'retail', label: 'Retail Space', baseRate: 0.18 }
];

export function PricingCalculator() {
  const [inputs, setInputs] = useState<CalculatorInputs>({
    squareFootage: 1000,
    frequency: 'weekly',
    serviceType: 'office'
  });

  const price = calculatePrice(inputs);

  return (
    <section className="py-16 bg-white">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-4">
            <Calculator className="w-6 h-6 text-brand-600" />
          </div>
          <h2 className="text-3xl font-bold text-gray-900">
            Estimate Your Cost
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            Get an instant price estimate based on your specific needs
          </p>
        </div>

        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="space-y-6">
            {/* Square Footage */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Square Footage
              </label>
              <input
                type="number"
                min="100"
                max="50000"
                value={inputs.squareFootage}
                onChange={(e) => setInputs({ ...inputs, squareFootage: Number(e.target.value) })}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>

            {/* Service Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type of Service
              </label>
              <div className="grid grid-cols-2 gap-4">
                {serviceTypes.map((type) => (
                  <button
                    key={type.id}
                    onClick={() => setInputs({ ...inputs, serviceType: type.id })}
                    className={`p-4 text-left rounded-lg border-2 transition-colors ${
                      inputs.serviceType === type.id
                        ? 'border-brand-500 bg-brand-50'
                        : 'border-gray-200 hover:border-brand-200'
                    }`}
                  >
                    {type.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Frequency */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Frequency
              </label>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                {frequencies.map((freq) => (
                  <button
                    key={freq.id}
                    onClick={() => setInputs({ ...inputs, frequency: freq.id })}
                    className={`p-3 text-center rounded-lg border-2 transition-colors ${
                      inputs.frequency === freq.id
                        ? 'border-brand-500 bg-brand-50'
                        : 'border-gray-200 hover:border-brand-200'
                    }`}
                  >
                    {freq.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="text-center">
              <p className="text-lg text-gray-600 mb-2">Estimated Price</p>
              <p className="text-4xl font-bold text-gray-900">
                ${price.toFixed(2)}
                <span className="text-lg text-gray-500 font-normal">
                  /month
                </span>
              </p>
              <p className="mt-2 text-sm text-gray-500">
                *Final price may vary based on specific requirements
              </p>
            </div>

            <Button size="lg" className="w-full mt-6">
              Get Detailed Quote
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
