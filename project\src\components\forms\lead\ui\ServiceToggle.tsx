import React from 'react';
import { Check } from 'lucide-react';
import type { ServiceOption } from '../types';

interface ServiceToggleProps {
  service: ServiceOption;
  selected: boolean;
  onToggle: () => void;
}

export function ServiceToggle({ service, selected, onToggle }: ServiceToggleProps) {
  const Icon = service.icon;

  return (
    <button
      onClick={onToggle}
      className={`w-full p-4 rounded-lg transition-all duration-200 flex items-center touch-manipulation ${
        selected
          ? 'bg-green-50 border-2 border-green-500'
          : 'bg-white border border-gray-200 hover:border-green-300'
      }`}
      aria-pressed={selected}
    >
      <div className="p-2 rounded-full bg-green-100 mr-4">
        <Icon className="w-5 h-5 text-green-600" />
      </div>
      <div className="flex-1 text-left">
        <h3 className="text-lg font-medium">{service.label}</h3>
        <p className="text-sm text-gray-600">{service.description}</p>
      </div>
      <div className={`w-6 h-6 rounded-full border-2 ml-4 flex items-center justify-center ${
        selected ? 'border-green-500 bg-green-500' : 'border-gray-300'
      }`}>
        {selected && <Check className="w-4 h-4 text-white" />}
      </div>
    </button>
  );
}
