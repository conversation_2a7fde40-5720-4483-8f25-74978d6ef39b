import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, Calendar, Clock, CheckCircle, 
  Shield, Star, ArrowRight, Building2,
  Users, DollarSign, Heart,
  ChevronRight, ChevronLeft, Gift, Zap, Info,
  HomeIcon, Building, Castle, Warehouse, Wind, Sun, Waves, Paintbrush2, Layers, Sofa, SprayCan, PartyPopper, Trash2, ChefHat, Car
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

interface BookingFormData {
  // Event Cleaning Specific
  eventSize: string;
  cleaningTiming: string;
  
  // Service Details  
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  
  // Add-ons
  addOns: string[];
  
  // Contact Info
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  
  // Marketing
  howDidYouHear: string;
  newsletter: boolean;
}

const BrandAlignedEventForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPriceDetails, setShowPriceDetails] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState<BookingFormData>({
    eventSize: '',
    cleaningTiming: 'post-event',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    howDidYouHear: '',
    newsletter: false
  });

  // Check for form data restoration after login
  useEffect(() => {
    const savedFormData = localStorage.getItem('eventCleaningFormData');
    if (savedFormData && user) {
      try {
        const parsedData = JSON.parse(savedFormData);
        setFormData(parsedData);
        localStorage.removeItem('eventCleaningFormData');
        // Auto-open payment modal after restore
        setTimeout(() => {
          setShowPaymentModal(true);
        }, 1000);
      } catch (error) {
        console.error('Error parsing saved form data:', error);
        localStorage.removeItem('eventCleaningFormData');
      }
    }
  }, [user]);

  // Event size options
  const eventSizeOptions = [
    { 
      id: 'small', 
      name: 'Small Event', 
      description: 'Up to 25 guests, house parties',
      time: '2-3 hours',
      price: 'From $149',
      basePrice: 149,
      popular: false 
    },
    { 
      id: 'medium', 
      name: 'Medium Event', 
      description: '25-75 guests, family gatherings',
      time: '3-4 hours',
      price: 'From $249',
      basePrice: 249,
      popular: true 
    },
    { 
      id: 'large', 
      name: 'Large Event', 
      description: '75-150 guests, celebrations',
      time: '4-6 hours',
      price: 'From $399',
      basePrice: 399,
      popular: false 
    },
    { 
      id: 'xl', 
      name: 'XL Event', 
      description: '150+ guests, corporate events',
      time: '6+ hours',
      price: 'From $549',
      basePrice: 549,
      popular: false 
    }
  ];

  // Cleaning timing options
  const timingOptions = [
    { id: 'post-event', name: 'Post-Event Cleanup', description: 'Clean up after your event', priceMultiplier: 1.0 },
    { id: 'pre-event', name: 'Pre-Event Prep', description: 'Prepare before your event (20% discount)', priceMultiplier: 0.8 }
  ];

  // Dynamic price calculation
  const calculatePrice = () => {
    const addOnPrices: Record<string, number> = {
      'trash-haul': 79,
      'deep-kitchen': 89,
      'multiple-restrooms': 59,
      'outdoor-area': 69
    };

    // Get base price from selected event size
    const selectedSize = eventSizeOptions.find(size => size.id === formData.eventSize);
    let price = selectedSize?.basePrice || 149;

    // Apply timing multiplier
    const selectedTiming = timingOptions.find(timing => timing.id === formData.cleaningTiming);
    const multiplier = selectedTiming?.priceMultiplier || 1.0;
    price = Math.round(price * multiplier);
    
    // Add-on costs
    const addOnTotal = formData.addOns?.reduce((total, addon) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;

    return Math.round(price + addOnTotal);
  };

  // Add-on services
  const addOnServices = [
    {
      id: 'trash-haul',
      name: 'Trash Haul-Away',
      description: 'Remove all event trash and recyclables',
      price: 79,
      icon: <Trash2 className="w-5 h-5" />,
      recommended: true
    },
    {
      id: 'deep-kitchen',
      name: 'Deep Kitchen Clean',
      description: 'Complete kitchen sanitization after catering',
      price: 89,
      icon: <ChefHat className="w-5 h-5" />
    },
    {
      id: 'multiple-restrooms',
      name: 'Multiple Restrooms',
      description: 'Clean and restock 3+ bathrooms',
      price: 59,
      icon: <Sparkles className="w-5 h-5" />
    },
    {
      id: 'outdoor-area',
      name: 'Outdoor Area Cleanup',
      description: 'Clean patios, decks, and outdoor spaces',
      price: 69,
      icon: <Sun className="w-5 h-5" />
    }
  ];

  // Enhanced time slots with conversion psychology
  const timeSlots = [
    { 
      id: 'early', 
      name: '7:00 AM - 10:00 AM', 
      label: 'Early Morning',
      available: true,
      slots: 4,
      urgency: 'Available',
      urgencyColor: 'text-green-600'
    },
    { 
      id: 'morning', 
      name: '10:00 AM - 1:00 PM', 
      label: 'Morning',
      available: true,
      popular: true,
      slots: 2,
      urgency: 'Only 2 slots left!',
      urgencyColor: 'text-orange-600'
    },
    { 
      id: 'afternoon', 
      name: '1:00 PM - 4:00 PM', 
      label: 'Afternoon',
      available: true,
      slots: 3,
      urgency: 'Limited availability',
      urgencyColor: 'text-yellow-600'
    },
    { 
      id: 'evening', 
      name: '4:00 PM - 7:00 PM', 
      label: 'Evening',
      available: true,
      slots: 1,
      urgency: 'Last slot!',
      urgencyColor: 'text-red-600'
    }
  ];

  // Step validation
  const isStepValid = (step: number) => {
    switch (step) {
      case 0:
        return formData.eventSize && formData.cleaningTiming;
      case 1:
        return formData.preferredDate && formData.preferredTime;
      case 2:
        return true; // Add-ons are optional
      case 3:
        return formData.firstName && formData.lastName && formData.email && 
               formData.phone && formData.address && formData.city && formData.zipCode;
      default:
        return false;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!isStepValid(3)) return;
    
    // Check if user is authenticated
    if (!user) {
      // Save form data and redirect to login
      localStorage.setItem('eventCleaningFormData', JSON.stringify(formData));
      navigate('/auth/login');
      return;
    }
    
    // Open payment modal for authenticated users
    setShowPaymentModal(true);
  };

  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Get calendar dates for current month
  const getCalendarDates = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    const dates = [];
    
    // Add empty cells for days before month starts
    const startPadding = firstDay.getDay();
    for (let i = 0; i < startPadding; i++) {
      dates.push(null);
    }
    
    // Add all days of the month
    for (let day = 1; day <= lastDay.getDate(); day++) {
      dates.push(new Date(year, month, day));
    }
    
    return dates;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  // Navigation functions
  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      // Go back to the main page when at first step
      navigate('/residential');
    }
  };

  const handleGoToFirstStep = () => {
    setCurrentStep(0);
  };

  // Form steps configuration
  const steps = [
    { 
      title: 'Event Details', 
      subtitle: 'Tell us about your event',
      icon: <PartyPopper className="w-5 h-5" />
    },
    { 
      title: 'Schedule Service', 
      subtitle: 'Pick your preferred date & time',
      icon: <Calendar className="w-5 h-5" />
    },
    { 
      title: 'Add-Ons', 
      subtitle: 'Extra services for your event',
      icon: <Shield className="w-5 h-5" />
    },
    { 
      title: 'Contact Info', 
      subtitle: 'Almost done! Just need details',
      icon: <Users className="w-5 h-5" />
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ scale: [1, 1.1, 1], rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <PartyPopper className="w-8 h-8 text-brand-600" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Book Event Cleaning</h1>
                <p className="text-sm text-gray-600">Professional post-event and pre-event cleaning services</p>
              </div>
            </div>
            
            {/* Enhanced Trust badges */}
            <div className="hidden md:flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-brand-600" />
                <span className="text-sm font-medium">Last-Minute Availability</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium">4.9★ (500+ events)</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium">Discreet & Professional</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex items-center">
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: currentStep === index ? 1.1 : 1,
                      backgroundColor: currentStep >= index ? '#638907' : '#e5e7eb'
                    }}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white transition-all`}
                  >
                    {currentStep > index ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className={currentStep >= index ? 'text-white' : 'text-gray-600'}>
                        {index + 1}
                      </span>
                    )}
                  </motion.div>
                  <div className="hidden md:block ml-3">
                    <p className={`font-medium ${currentStep >= index ? 'text-brand-600' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500">{step.subtitle}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRight className={`mx-4 w-5 h-5 ${
                    currentStep > index ? 'text-brand-600' : 'text-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              {/* Step 0: Event Details */}
              {currentStep === 0 && (
                <motion.div
                  key="step0"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">What size event needs cleaning?</h2>
                    <p className="text-gray-600">This helps us determine the right team size and time needed.</p>
                  </div>

                  {/* Event Size Selection */}
                  <div className="mb-8">
                    <div className="grid grid-cols-2 gap-4">
                      {eventSizeOptions.map((opt) => (
                        <motion.button
                          key={opt.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setFormData({ 
                            ...formData, 
                            eventSize: opt.id,
                          })}
                          className={`relative p-6 rounded-2xl border-2 transition-all text-left ${
                            formData.eventSize === opt.id
                              ? 'border-brand-500 bg-brand-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          {opt.popular && (
                            <span className="absolute -top-2 right-4 bg-warm-500 text-white text-xs px-2 py-0.5 rounded-full">
                              Most Popular
                            </span>
                          )}
                          <div className={`mb-3 ${
                            formData.eventSize === opt.id ? 'text-brand-600' : 'text-gray-600'
                          }`}>
                            <PartyPopper className="w-6 h-6" />
                          </div>
                          <h3 className="font-semibold text-gray-900">{opt.name}</h3>
                          <p className="text-sm text-gray-500 mt-1">{opt.description}</p>
                          <div className="mt-3 space-y-1">
                            <div className="flex items-center gap-2 text-xs text-gray-600">
                              <Clock className="w-3 h-3" />
                              <span>{opt.time}</span>
                            </div>
                            <div className="flex items-center gap-2 text-xs font-medium text-brand-600">
                              <DollarSign className="w-3 h-3" />
                              <span>{opt.price}</span>
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Cleaning Timing Selection */}
                  {formData.eventSize && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-8"
                    >
                      <label className="block text-sm font-semibold text-gray-700 mb-4">
                        When do you need cleaning?
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {timingOptions.map((timing) => (
                          <motion.button
                            key={timing.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, cleaningTiming: timing.id })}
                            className={`relative p-6 rounded-2xl border-2 transition-all text-left ${
                              formData.cleaningTiming === timing.id
                                ? 'border-brand-500 bg-brand-50 shadow-md'
                                : 'border-gray-200 hover:border-gray-300 bg-white'
                            }`}
                          >
                            {timing.priceMultiplier < 1.0 && (
                              <span className="absolute -top-2 right-4 bg-green-500 text-white text-xs px-2 py-0.5 rounded-full">
                                20% OFF
                              </span>
                            )}
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 text-lg">{timing.name}</h3>
                              <p className="text-sm text-gray-600 mt-1">{timing.description}</p>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  )}

                  {/* Additional Event Info (Optional) */}
                  <div className="mb-8 p-6 bg-gray-50 rounded-2xl">
                    <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Info className="w-4 h-4 text-gray-600" />
                      Additional Information (Optional)
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Help us prepare better for your event cleanup:
                    </p>
                    <div className="space-y-3">
                      <label className="flex items-center gap-3 cursor-pointer">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded" />
                        <span className="text-sm text-gray-700">Food and beverage spills expected</span>
                      </label>
                      <label className="flex items-center gap-3 cursor-pointer">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded" />
                        <span className="text-sm text-gray-700">Outdoor area was used</span>
                      </label>
                      <label className="flex items-center gap-3 cursor-pointer">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded" />
                        <span className="text-sm text-gray-700">Multiple rooms/levels involved</span>
                      </label>
                      <label className="flex items-center gap-3 cursor-pointer">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded" />
                        <span className="text-sm text-gray-700">Decorations need careful handling</span>
                      </label>
                    </div>
                  </div>

                  {/* Continue Button */}
                  <Button
                    onClick={handleNext}
                    disabled={!isStepValid(0)}
                    className="w-full"
                    size="lg"
                  >
                    Continue
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>
                </motion.div>
              )}

              {/* Step 1: Service Schedule */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose your cleaning date</h2>
                    <p className="text-gray-600">Pick your preferred date and time for the service</p>
                  </div>

                  {/* Premium Calendar Experience */}
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-8"
                  >
                    <label className="block text-sm font-semibold text-gray-700 mb-4">
                      Preferred Date
                    </label>
                    <div className="bg-gray-50 rounded-2xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                        </h3>
                        <div className="flex gap-2">
                          <button
                            onClick={handleBack}
                            className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
                          >
                            <ChevronLeft className="w-5 h-5 text-gray-600" />
                          </button>
                          <button
                            onClick={() => navigateMonth('next')}
                            className="p-2 hover:bg-gray-200 rounded-lg transition-colors"
                          >
                            <ChevronRight className="w-5 h-5 text-gray-600" />
                          </button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-7 gap-1 mb-2">
                        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                          <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                            {day}
                          </div>
                        ))}
                      </div>
                      
                      <div className="grid grid-cols-7 gap-1">
                        {getCalendarDates().map((date, index) => {
                          if (!date) {
                            return <div key={index} className="p-2"></div>;
                          }
                          
                          const isToday = date.toDateString() === new Date().toDateString();
                          const isSelected = formData.preferredDate === date.toISOString().split('T')[0];
                          const isPast = date < new Date(new Date().setHours(0, 0, 0, 0));
                          
                          return (
                            <motion.button
                              key={index}
                              whileHover={!isPast ? { scale: 1.1 } : {}}
                              whileTap={!isPast ? { scale: 0.95 } : {}}
                              onClick={() => !isPast && setFormData({ 
                                ...formData, 
                                preferredDate: date.toISOString().split('T')[0] 
                              })}
                              disabled={isPast}
                              className={`p-2 text-sm rounded-lg transition-colors ${
                                isPast 
                                  ? 'text-gray-300 cursor-not-allowed'
                                  : isSelected
                                    ? 'bg-brand-600 text-white'
                                    : isToday
                                      ? 'bg-blue-100 text-blue-600 font-medium'
                                      : 'hover:bg-gray-200 text-gray-700'
                              }`}
                            >
                              {date.getDate()}
                            </motion.button>
                          );
                        })}
                      </div>
                    </div>
                  </motion.div>

                  {/* Time Selection */}
                  {formData.preferredDate && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-8"
                    >
                      <label className="block text-sm font-semibold text-gray-700 mb-4">
                        Preferred Time
                      </label>
                      <div className="grid grid-cols-2 gap-4">
                        {timeSlots.map((slot) => (
                          <motion.button
                            key={slot.id}
                            whileHover={{ scale: slot.available ? 1.02 : 1 }}
                            whileTap={{ scale: slot.available ? 0.98 : 1 }}
                            onClick={() => slot.available && setFormData({ ...formData, preferredTime: slot.id })}
                            disabled={!slot.available}
                            className={`relative p-4 rounded-xl border-2 transition-all text-left ${
                              formData.preferredTime === slot.id
                                ? 'border-brand-500 bg-brand-50'
                                : slot.available
                                  ? 'border-gray-200 hover:border-gray-300 bg-white'
                                  : 'border-gray-100 bg-gray-50 cursor-not-allowed'
                            }`}
                          >
                            {slot.popular && slot.available && (
                              <span className="absolute -top-2 right-2 bg-orange-500 text-white text-xs px-2 py-0.5 rounded-full">
                                Popular
                              </span>
                            )}
                            <div className="flex justify-between items-center">
                              <div>
                                <h3 className={`font-medium ${slot.available ? 'text-gray-900' : 'text-gray-400'}`}>
                                  {slot.label}
                                </h3>
                                <p className={`text-sm ${slot.available ? 'text-gray-600' : 'text-gray-400'}`}>
                                  {slot.name}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className={`text-xs ${slot.urgencyColor}`}>
                                  {slot.urgency}
                                </p>
                              </div>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  )}

                  {/* Navigation */}
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      onClick={handleBack}
                      className="flex-1"
                    >
                      <ChevronLeft className="mr-2 w-5 h-5" />
                      Back
                    </Button>
                    <Button
                      onClick={handleNext}
                      disabled={!isStepValid(1)}
                      className="flex-1"
                    >
                      Continue
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Add-ons */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Add extra services</h2>
                    <p className="text-gray-600">Enhance your event cleanup with these optional services</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4 mb-8">
                    {addOnServices.map((service) => {
                      const isSelected = formData.addOns.includes(service.id);
                      
                      return (
                        <motion.button
                          key={service.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => {
                            const newAddOns = isSelected
                              ? formData.addOns.filter(id => id !== service.id)
                              : [...formData.addOns, service.id];
                            setFormData({ ...formData, addOns: newAddOns });
                          }}
                          className={`relative p-6 rounded-2xl border-2 transition-all text-left ${
                            isSelected
                              ? 'border-brand-500 bg-brand-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          {service.recommended && (
                            <span className="absolute -top-2 right-4 bg-green-500 text-white text-xs px-2 py-0.5 rounded-full">
                              Recommended
                            </span>
                          )}
                          
                          <div className="flex items-start gap-4">
                            <div className={`p-3 rounded-xl transition-colors ${
                              isSelected
                                ? 'bg-brand-100 text-brand-600'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {service.icon}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <h3 className="font-semibold text-gray-900">{service.name}</h3>
                                <span className="text-lg font-bold text-brand-600">+${service.price}</span>
                              </div>
                              <p className="text-sm text-gray-600">{service.description}</p>
                            </div>
                          </div>
                          
                          {isSelected && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute top-4 right-4 w-6 h-6 bg-brand-600 rounded-full flex items-center justify-center"
                            >
                              <CheckCircle className="w-4 h-4 text-white" />
                            </motion.div>
                          )}
                        </motion.button>
                      );
                    })}
                  </div>

                  {/* Special Instructions */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Special Instructions (Optional)
                    </label>
                    <textarea
                      value={formData.specialInstructions}
                      onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })}
                      placeholder="Any specific areas to focus on or special requirements for your event cleanup..."
                      className="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent resize-none"
                      rows={4}
                    />
                  </div>

                  {/* Navigation */}
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      onClick={handleBack}
                      className="flex-1"
                    >
                      <ChevronLeft className="mr-2 w-5 h-5" />
                      Back
                    </Button>
                    <Button
                      onClick={handleNext}
                      className="flex-1"
                    >
                      Continue
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Contact Information */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Complete your booking</h2>
                    <p className="text-gray-600">Just a few details and you're all set!</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6 mb-8">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        value={formData.firstName}
                        onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        value={formData.lastName}
                        onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="Smith"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Phone Number *
                      </label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="(*************"
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        Service Address *
                      </label>
                      <input
                        type="text"
                        value={formData.address}
                        onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="123 Main Street, Apt 4B"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        City *
                      </label>
                      <input
                        type="text"
                        value={formData.city}
                        onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="New York"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">
                        ZIP Code *
                      </label>
                      <input
                        type="text"
                        value={formData.zipCode}
                        onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                        className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                        placeholder="10001"
                      />
                    </div>
                  </div>

                  {/* How did you hear about us */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      How did you hear about us? (Optional)
                    </label>
                    <select
                      value={formData.howDidYouHear}
                      onChange={(e) => setFormData({ ...formData, howDidYouHear: e.target.value })}
                      className="w-full p-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                    >
                      <option value="">Select an option</option>
                      <option value="google">Google Search</option>
                      <option value="social">Social Media</option>
                      <option value="referral">Friend/Family Referral</option>
                      <option value="advertisement">Advertisement</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  {/* Newsletter */}
                  <div className="mb-8">
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.newsletter}
                        onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
                        className="w-4 h-4 text-brand-600 rounded"
                      />
                      <span className="text-sm text-gray-700">
                        Subscribe to our newsletter for event cleaning tips and special offers
                      </span>
                    </label>
                  </div>

                  {/* Navigation */}
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      onClick={handleBack}
                      className="flex-1"
                    >
                      <ChevronLeft className="mr-2 w-5 h-5" />
                      Back
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      disabled={!isStepValid(3) || isSubmitting}
                      className="flex-1"
                      size="lg"
                    >
                      {isSubmitting ? (
                        <>
                          <Clock className="mr-2 w-5 h-5 animate-spin" />
                          Booking...
                        </>
                      ) : (
                        <>
                          Complete Booking
                          <CheckCircle className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
              
            </AnimatePresence>
          </div>

          {/* Enhanced Conversion Sidebar with Event-specific content */}
          <div className="lg:sticky lg:top-32 h-fit space-y-6">
            {/* Price Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl shadow-soft p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Price Summary</h3>
                <div className="flex items-center gap-1 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                  <Star className="w-3 h-3 fill-current" />
                  <span className="font-medium">Event Special</span>
                </div>
              </div>
              
              {formData.eventSize ? (
                <>
                  <div className="space-y-3 mb-4">
                    <div className="flex items-baseline justify-between">
                      <span className="text-3xl font-bold text-gray-900">${calculatePrice()}</span>
                      <div className="text-right">
                        <span className="text-gray-600">/service</span>
                        <p className="text-xs text-green-600 font-medium">Professional event cleanup</p>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => setShowPriceDetails(!showPriceDetails)}
                    className="text-sm text-brand-600 hover:text-brand-700 font-medium"
                  >
                    {showPriceDetails ? 'Hide' : 'Show'} price breakdown
                  </button>

                  <AnimatePresence>
                    {showPriceDetails && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-4 pt-4 border-t space-y-2 text-sm"
                      >
                        {formData.eventSize && (
                          <div className="flex justify-between text-gray-600">
                            <span>Event cleanup ({eventSizeOptions.find(size => size.id === formData.eventSize)?.name})</span>
                            <span>${eventSizeOptions.find(size => size.id === formData.eventSize)?.basePrice || 149}</span>
                          </div>
                        )}
                        {formData.cleaningTiming && formData.cleaningTiming === 'pre-event' && (
                          <div className="flex justify-between text-green-600">
                            <span>Pre-event discount (20% off)</span>
                            <span>-${Math.round((eventSizeOptions.find(size => size.id === formData.eventSize)?.basePrice || 149) * 0.2)}</span>
                          </div>
                        )}
                        {formData.addOns.map(addon => {
                          const service = addOnServices.find(s => s.id === addon);
                          return service ? (
                            <div key={addon} className="flex justify-between text-gray-600">
                              <span>{service.name}</span>
                              <span>+${service.price}</span>
                            </div>
                          ) : null;
                        })}
                        <div className="border-t pt-2 mt-2">
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>Avg. market price</span>
                            <span className="line-through">${calculatePrice() + 50}</span>
                          </div>
                          <div className="flex justify-between text-green-600 font-medium">
                            <span>Your savings</span>
                            <span>$50</span>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </>
              ) : (
                <div className="text-center py-8">
                  <PartyPopper className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                  <p className="text-gray-500">Select event size to see pricing</p>
                  <p className="text-xs text-gray-400 mt-1">Starting from $149</p>
                </div>
              )}
            </motion.div>

            {/* Enhanced Trust & Testimonial */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gradient-to-br from-brand-50 to-accent-50 rounded-2xl p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Heart className="w-5 h-5 text-red-500" />
                Why 500+ Events Trust Us
              </h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Same-Day Availability</p>
                    <p className="text-sm text-gray-600">Emergency and last-minute cleanup service</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Discreet Service</p>
                    <p className="text-sm text-gray-600">Professional, quiet, and respectful cleanup</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <PartyPopper className="w-5 h-5 text-purple-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Event Specialists</p>
                    <p className="text-sm text-gray-600">Experienced with all event types and venues</p>
                  </div>
                </div>

                {/* Quick Testimonial */}
                <div className="mt-6 p-4 bg-white rounded-xl border border-gray-100">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 fill-current" />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">Verified • 1 week ago</span>
                  </div>
                  <p className="text-sm text-gray-700 italic mb-2">
                    "They cleaned up after our wedding reception flawlessly! 150 guests, no problem. Everything was spotless by morning. Highly recommend!"
                  </p>
                  <p className="text-xs text-gray-500">- Jennifer & Mike, Wedding</p>
                </div>
              </div>
            </motion.div>

            {/* Limited Time Offer */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="bg-gradient-to-br from-purple-50 to-pink-50 border border-purple-200 rounded-2xl p-6"
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-3">
                  <Gift className="w-6 h-6 text-purple-600" />
                  <h3 className="text-lg font-bold text-purple-900">Event Special</h3>
                </div>
                <p className="text-2xl font-bold text-purple-600 mb-2">$50 OFF</p>
                <p className="text-sm text-purple-800 mb-4">Large events + free disposal bags</p>
                <div className="flex items-center justify-center gap-2 text-xs text-purple-700">
                  <Clock className="w-3 h-3" />
                  <span>Book within 48 hours of event</span>
                </div>
              </div>
            </motion.div>

            {/* AI Chat Support */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-br from-brand-50 to-accent-50 rounded-2xl p-4"
            >
              <div className="flex items-center gap-2 mb-3">
                <div className="w-8 h-8 bg-brand-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Event Questions?</p>
                  <p className="text-xs text-gray-600">Get instant answers</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full border-brand-200 hover:bg-brand-100"
                onClick={() => {/* TODO: Open chatbot */}}
              >
                <Sparkles className="w-4 h-4 mr-2 text-brand-600" />
                Chat with AI Helper
              </Button>
              <p className="text-xs text-gray-500 mt-2 text-center">Instant answers, 24/7 available</p>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description={`Event Cleaning - ${eventSizeOptions.find(size => size.id === formData.eventSize)?.name || 'Custom'} (${formData.cleaningTiming === 'pre-event' ? 'Pre-Event Prep' : 'Post-Event Cleanup'})`}
          customerEmail={formData.email}
          formData={{
            ...formData,
            serviceType: 'event-cleaning'
          }}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedEventForm; 
