import React from 'react';
import { <PERSON>rid, <PERSON>, Sparkles, AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';

interface RestorationScopeProps {
  scope: {
    services: string[];
    finishType: string;
    trafficLevel: string;
    specialRequirements: string[];
  };
  onChange: (scope: any) => void;
}

export function RestorationScope({ scope, onChange }: RestorationScopeProps) {
  const services = [
    'Deep Cleaning',
    'Stripping & Waxing',
    'Buffing & Polishing',
    'Scratch Removal',
    'Color Restoration',
    'Sealing',
    'Repairs',
    'Coating Application'
  ];

  const finishTypes = [
    'High Gloss',
    'Semi-Gloss',
    'Satin',
    'Matte',
    'Natural',
    'Custom'
  ];

  const trafficLevels = [
    'Light',
    'Moderate',
    'Heavy',
    'Extreme'
  ];

  const requirements = [
    'Non-Slip Treatment',
    'Antimicrobial Treatment',
    'Low VOC Products',
    'LEED Compliant',
    'After-Hours Service',
    'Dust Containment'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Sparkles className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Restoration Scope</h3>
          <p className="text-gray-600">Define your restoration requirements</p>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Services Needed <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {services.map((service) => (
              <label
                key={service}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.services.includes(service)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.services.includes(service)}
                  onChange={(e) => {
                    const newServices = e.target.checked
                      ? [...scope.services, service]
                      : scope.services.filter(s => s !== service);
                    onChange({ ...scope, services: newServices });
                  }}
                  className="sr-only"
                />
                <Grid className={`w-5 h-5 mr-3 ${
                  scope.services.includes(service) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{service}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Desired Finish <span className="text-red-500">*</span>
            </label>
            <select
              value={scope.finishType}
              onChange={(e) => onChange({ ...scope, finishType: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select finish type</option>
              {finishTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Traffic Level <span className="text-red-500">*</span>
            </label>
            <select
              value={scope.trafficLevel}
              onChange={(e) => onChange({ ...scope, trafficLevel: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select traffic level</option>
              {trafficLevels.map((level) => (
                <option key={level} value={level}>{level}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Special Requirements
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {requirements.map((req) => (
              <label
                key={req}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.specialRequirements.includes(req)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.specialRequirements.includes(req)}
                  onChange={(e) => {
                    const newReqs = e.target.checked
                      ? [...scope.specialRequirements, req]
                      : scope.specialRequirements.filter(r => r !== req);
                    onChange({ ...scope, specialRequirements: newReqs });
                  }}
                  className="sr-only"
                />
                <Shield className={`w-5 h-5 mr-3 ${
                  scope.specialRequirements.includes(req) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{req}</span>
              </label>
            ))}
          </div>
        </div>

        {scope.trafficLevel === 'Extreme' && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">High Traffic Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  For extreme traffic areas, we recommend our heavy-duty coating system and more frequent maintenance schedule.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
