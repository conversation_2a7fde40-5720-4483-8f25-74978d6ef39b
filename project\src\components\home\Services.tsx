import React from 'react';
import { CheckCircle2, Building2, <PERSON><PERSON>lock, <PERSON><PERSON>les, Shield, Leaf } from 'lucide-react';

const services = [
  {
    icon: Building2,
    title: 'Commercial Cleaning',
    description: 'Professional cleaning for offices, retail spaces, and commercial buildings'
  },
  {
    icon: CalendarClock,
    title: 'Regular Service',
    description: 'Scheduled maintenance cleaning on daily, weekly, or monthly basis'
  },
  {
    icon: Sparkles,
    title: 'Deep Cleaning',
    description: 'Thorough deep cleaning and sanitization services'
  },
  {
    icon: Shield,
    title: 'Special Services',
    description: 'Floor care, window cleaning, and post-construction cleanup'
  }
];

export function Services() {
  return (
    <section id="services" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <div className="inline-flex items-center justify-center space-x-2">
            <Leaf className="h-6 w-6 text-brand-600" />
            <span className="text-brand-600 font-semibold">Our Services</span>
          </div>
          <h2 className="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
            Professional Cleaning Solutions
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
            We provide eco-friendly commercial cleaning services that maintain the highest standards of cleanliness
          </p>
        </div>

        <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <div
                key={index}
                className="relative group bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="inline-flex items-center justify-center rounded-lg bg-brand-100 p-3 mb-5">
                  <Icon className="h-6 w-6 text-brand-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">{service.title}</h3>
                <p className="mt-2 text-gray-600">{service.description}</p>
              </div>
            );
          })}
        </div>

        <div className="mt-16 bg-white rounded-xl shadow-sm p-8">
          <h3 className="text-2xl font-semibold text-gray-900 text-center">
            The Empire Pro Advantage
          </h3>
          <div className="mt-8 grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {[
              'Green Cleaning Products',
              'Trained & Vetted Staff',
              'Flexible Scheduling',
              'Competitive Pricing',
              'Satisfaction Guaranteed',
              'Fully Insured & Bonded'
            ].map((feature, index) => (
              <div key={index} className="flex items-center space-x-2">
                <CheckCircle2 className="h-5 w-5 text-brand-500 flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
