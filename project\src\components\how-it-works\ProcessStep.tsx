import React from 'react';
import { Check } from 'lucide-react';

interface ProcessStepProps {
  title: string;
  description: string;
  stepNumber: number;
}

export function ProcessStep({ title, description, stepNumber }: ProcessStepProps) {
  return (
    <div className="flex items-start space-x-4">
      <div className="flex-shrink-0">
        <div className="rounded-full bg-brand-100 p-2">
          <Check className="w-5 h-5 text-brand-600" />
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-900">
          {stepNumber}. {title}
        </h3>
        <p className="mt-2 text-gray-600">{description}</p>
      </div>
    </div>
  );
}
