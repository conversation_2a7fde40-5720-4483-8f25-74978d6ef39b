import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, Calendar, Clock, CheckCircle, 
  Shield, Star, ArrowRight, Building2,
  Users, DollarSign, Heart, AlertTriangle,
  ChevronRight, ChevronLeft, Flame, Zap, Info,
  HomeIcon, Building, Wrench, ShieldCheck, Eye, Wind, ThermometerSun, AlertCircle, Lightbulb, Crown
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

interface BookingFormData {
  // Property Info
  propertyType: string;
  servicePackage: string;
  
  // Service Details  
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  
  // Add-ons
  addOns: string[];
  
  // Contact Info
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  
  // Marketing
  howDidYouHear: string;
  newsletter: boolean;
}

const BrandAlignedChimneyForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPriceDetails, setShowPriceDetails] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<BookingFormData>({
    propertyType: '',
    servicePackage: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    howDidYouHear: '',
    newsletter: false
  });

  // Check for form data restoration after login
  useEffect(() => {
    const savedFormData = localStorage.getItem('chimneyCleaningFormData');
    if (savedFormData && user) {
      try {
        const parsedData = JSON.parse(savedFormData);
        setFormData(parsedData);
        localStorage.removeItem('chimneyCleaningFormData');
        // Auto-open payment modal after restore
        setTimeout(() => {
          setShowPaymentModal(true);
        }, 1000);
      } catch (error) {
        console.error('Error parsing saved form data:', error);
        localStorage.removeItem('chimneyCleaningFormData');
      }
    }
  }, [user]);

  // Property types with safety focus
  const propertyTypes = [
    { 
      id: 'house', 
      name: 'Single Family Home', 
      description: 'Detached house with fireplace',
      details: 'Standard chimney access',
      time: '2-3 hours',
      badge: 'Most Common',
      icon: <Home className="w-6 h-6" />
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      description: 'Attached home with shared walls',
      details: 'May require neighbor coordination',
      time: '2-4 hours',
      icon: <Building className="w-6 h-6" />
    },
    { 
      id: 'condo', 
      name: 'Apartment/Condo', 
      description: 'Multi-unit building with fireplace',
      details: 'Building management coordination',
      time: '1-2 hours',
      icon: <Building2 className="w-6 h-6" />
    }
  ];

  // Enhanced service packages with safety focus
  const getServicePackages = () => {
    const packages = {
      'house': [
        { 
          id: 'cleaning-only', 
          name: 'Cleaning Only', 
          description: 'Basic chimney cleaning & debris removal',
          time: '2-3 hours',
          price: '$179',
          basePrice: 179,
          popular: false,
          icon: <Sparkles className="w-6 h-6" />
        },
        { 
          id: 'cleaning-inspection', 
          name: 'Cleaning + Safety Inspection', 
          description: 'Complete cleaning plus Level 1 safety inspection',
          time: '3-4 hours',
          price: '$249',
          basePrice: 249,
          popular: true,
          icon: <ShieldCheck className="w-6 h-6" />
        },
        { 
          id: 'full-service', 
          name: 'Full Safety Service', 
          description: 'Deep clean, inspection, and minor repairs',
          time: '4-5 hours',
          price: '$329',
          basePrice: 329,
          popular: false,
          icon: <Crown className="w-6 h-6" />
        }
      ],
      'townhouse': [
        { 
          id: 'cleaning-only', 
          name: 'Basic Cleaning', 
          description: 'Standard cleaning for attached homes',
          time: '2-3 hours',
          price: '$199',
          basePrice: 199,
          popular: false,
          icon: <Sparkles className="w-6 h-6" />
        },
        { 
          id: 'cleaning-inspection', 
          name: 'Cleaning + Inspection', 
          description: 'Cleaning plus safety inspection',
          time: '3-4 hours',
          price: '$269',
          basePrice: 269,
          popular: true,
          icon: <ShieldCheck className="w-6 h-6" />
        },
        { 
          id: 'full-service', 
          name: 'Complete Service', 
          description: 'Full cleaning, inspection & repairs',
          time: '4-5 hours',
          price: '$349',
          basePrice: 349,
          popular: false,
          icon: <Crown className="w-6 h-6" />
        }
      ],
      'condo': [
        { 
          id: 'cleaning-only', 
          name: 'Condo Cleaning', 
          description: 'Apartment/condo fireplace cleaning',
          time: '1-2 hours',
          price: '$149',
          basePrice: 149,
          popular: false,
          icon: <Sparkles className="w-6 h-6" />
        },
        { 
          id: 'cleaning-inspection', 
          name: 'Cleaning + Check', 
          description: 'Cleaning plus basic safety check',
          time: '2-3 hours',
          price: '$199',
          basePrice: 199,
          popular: true,
          icon: <ShieldCheck className="w-6 h-6" />
        },
        { 
          id: 'full-service', 
          name: 'Premium Service', 
          description: 'Complete condo fireplace service',
          time: '3-4 hours',
          price: '$269',
          basePrice: 269,
          popular: false,
          icon: <Crown className="w-6 h-6" />
        }
      ]
    };
    return packages[formData.propertyType as keyof typeof packages] || packages.house;
  };

  // Safety-focused add-on services
  const addOnServices = [
    {
      id: 'cap-inspection',
      name: 'Chimney Cap Inspection',
      description: 'Check & repair rain cap and spark arrestor',
      price: 45,
      icon: <Shield className="w-5 h-5" />,
      recommended: true
    },
    {
      id: 'damper-check',
      name: 'Damper Operation Check',
      description: 'Inspect & lubricate damper mechanism',
      price: 35,
      icon: <Wrench className="w-5 h-5" />
    },
    {
      id: 'creosote-removal',
      name: 'Heavy Creosote Removal',
      description: 'Extra cleaning for dangerous buildup',
      price: 85,
      icon: <Flame className="w-5 h-5" />
    },
    {
      id: 'carbon-monoxide-test',
      name: 'Carbon Monoxide Test',
      description: 'Test for deadly gas leaks and proper ventilation',
      price: 65,
      icon: <AlertTriangle className="w-5 h-5" />,
      recommended: true
    }
  ];

  // Dynamic price calculation
  const calculatePrice = () => {
    const addOnPrices: Record<string, number> = {
      'cap-inspection': 45,
      'damper-check': 35,
      'creosote-removal': 85,
      'carbon-monoxide-test': 65
    };

    // Get base price from selected service package
    const selectedPackage = getServicePackages().find(pkg => pkg.id === formData.servicePackage);
    let price = selectedPackage?.basePrice || 249;
    
    // Add-on costs
    const addOnTotal = formData.addOns?.reduce((total, addon) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;

    return Math.round(price + addOnTotal);
  };

  // Validation
  const isStepValid = (step: number) => {
    switch(step) {
      case 0: return formData.propertyType && formData.servicePackage;
      case 1: return formData.preferredDate && formData.preferredTime;
      case 2: return true; // Add-ons are optional
      case 3: return formData.firstName && formData.lastName && formData.email && formData.phone && formData.address;
      default: return false;
    }
  };

  const handleSubmit = async () => {
    // Check if user is authenticated
    if (!user) {
      // Save form data and redirect to login
      localStorage.setItem('chimneyCleaningFormData', JSON.stringify(formData));
      navigate('/auth/login');
      return;
    }
    
    // Open payment modal for authenticated users
    setShowPaymentModal(true);
  };

  // Calendar dates
  const getCalendarDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
      const monthDay = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const fullDate = date.toISOString().split('T')[0];
      
      dates.push({
        display: `${dayName}, ${monthDay}`,
        value: fullDate,
        available: i <= 12 // Almost all dates available for safety
      });
    }
    
    return dates;
  };

  // Navigation handlers
  const handleStepNavigation = (direction: 'next' | 'prev') => {
    if (direction === 'next' && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else if (direction === 'prev' && currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Enhanced time slots with safety messaging
  const timeSlots = [
    { 
      id: 'morning', 
      name: '8:00 AM - 11:00 AM', 
      label: 'Morning',
      value: '8:00 AM',
      available: true,
      slots: 3,
      urgency: 'Best for safety inspections',
      urgencyColor: 'text-green-600'
    },
    { 
      id: 'midday', 
      name: '11:00 AM - 2:00 PM', 
      label: 'Midday',
      value: '11:00 AM',
      available: true, 
      popular: true,
      slots: 2,
      urgency: 'Most popular time',
      urgencyColor: 'text-orange-600'
    },
    { 
      id: 'afternoon', 
      name: '2:00 PM - 5:00 PM', 
      label: 'Afternoon',
      value: '2:00 PM',
      available: true,
      slots: 4,
      urgency: 'Great availability',
      urgencyColor: 'text-blue-600'
    }
  ];

  const steps = [
    {
      title: 'Chimney Details',
      subtitle: 'Fireplace & property info',
      icon: <Flame className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date & timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Safety Services',
      subtitle: 'Inspection & add-ons',
      icon: <Shield className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ];

  const renderStep = () => {
    switch(currentStep) {
      case 0:
        return (
          <motion.div
            key="step0"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">What type of property needs service?</h2>
              <p className="text-gray-600">This helps us determine the right safety approach and team size.</p>
            </div>

            {/* Property Type Selection */}
            <div className="mb-8">
              <div className="grid grid-cols-1 gap-4">
                {propertyTypes.map((type) => (
                  <motion.button
                    key={type.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setFormData({ ...formData, propertyType: type.id, servicePackage: '' })}
                    className={`w-full p-6 rounded-2xl border-2 text-left transition-all ${
                      formData.propertyType === type.id
                        ? 'border-brand-500 bg-brand-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-xl ${
                        formData.propertyType === type.id ? 'bg-brand-100' : 'bg-gray-100'
                      }`}>
                        {type.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{type.name}</h4>
                        <p className="text-sm text-gray-600">{type.description}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{type.time}</span>
                          </div>
                          {type.badge && (
                            <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                              {type.badge}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Service Package Selection */}
            {formData.propertyType && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <label className="block text-sm font-semibold text-gray-700 mb-4">
                  Choose your safety service package
                </label>
                <div className="grid grid-cols-2 gap-4">
                  {getServicePackages().map((pkg) => (
                    <motion.button
                      key={pkg.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setFormData({ ...formData, servicePackage: pkg.id })}
                      className={`relative p-6 rounded-2xl border-2 text-left transition-all ${
                        formData.servicePackage === pkg.id
                          ? 'border-brand-500 bg-brand-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 bg-white'
                      }`}
                    >
                      {pkg.popular && (
                        <span className="absolute -top-2 right-4 bg-warm-500 text-white text-xs px-2 py-0.5 rounded-full">
                          Most Popular
                        </span>
                      )}
                      <div className={`mb-3 ${
                        formData.servicePackage === pkg.id ? 'text-brand-600' : 'text-gray-600'
                      }`}>
                        {pkg.icon}
                      </div>
                      <h3 className="font-semibold text-gray-900">{pkg.name}</h3>
                      <p className="text-sm text-gray-500 mt-1">{pkg.description}</p>
                      <div className="mt-3 space-y-1">
                        <div className="flex items-center gap-2 text-xs text-gray-600">
                          <Clock className="w-3 h-3" />
                          <span>{pkg.time}</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs font-medium text-brand-600">
                          <DollarSign className="w-3 h-3" />
                          <span>{pkg.price}</span>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Safety Information */}
            {formData.servicePackage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-6 bg-red-50 rounded-2xl border border-red-200"
              >
                <h3 className="font-semibold text-red-900 mb-4 flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  Important Safety Information
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm text-red-800">
                  <div className="flex items-center gap-2">
                    <Flame className="w-3 h-3 text-red-600" />
                    <span>Prevents chimney fires</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-3 h-3 text-red-600" />
                    <span>Stops carbon monoxide leaks</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Eye className="w-3 h-3 text-red-600" />
                    <span>Identifies structural issues</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ThermometerSun className="w-3 h-3 text-red-600" />
                    <span>Improves heating efficiency</span>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        );

      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Schedule Your Safety Service</h2>
              <p className="text-gray-600">Choose your preferred date and time for chimney service</p>
            </div>

            {/* Date Selection */}
            <div className="mb-8">
              <label className="block text-sm font-semibold text-gray-700 mb-4">
                When would you like us to come?
              </label>
              <div className="grid grid-cols-2 gap-3">
                {getCalendarDates().map((date) => (
                  <motion.button
                    key={date.value}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    disabled={!date.available}
                    className={`p-4 rounded-lg border-2 text-left transition-all ${
                      formData.preferredDate === date.value
                        ? 'border-brand-500 bg-brand-50'
                        : date.available
                        ? 'border-gray-200 hover:border-brand-300'
                        : 'border-gray-100 bg-gray-50 text-gray-400'
                    }`}
                    onClick={() => setFormData({ ...formData, preferredDate: date.value })}
                  >
                    <div className="font-semibold">{date.display}</div>
                    <div className="text-sm text-gray-500">
                      {date.available ? 'Available' : 'Fully Booked'}
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Time Selection */}
            {formData.preferredDate && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <label className="block text-sm font-semibold text-gray-700 mb-4">
                  What time works best?
                </label>
                <div className="grid grid-cols-1 gap-3">
                  {timeSlots.map((slot) => (
                    <motion.div
                      key={slot.value}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="relative"
                    >
                      {slot.popular && (
                        <div className="absolute -top-2 -right-2 bg-brand-600 text-white px-2 py-1 rounded-full text-xs font-medium z-10">
                          Popular
                        </div>
                      )}
                      <button
                        disabled={!slot.available}
                        className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
                          formData.preferredTime === slot.value
                            ? 'border-brand-500 bg-brand-50'
                            : slot.available
                            ? 'border-gray-200 hover:border-brand-300'
                            : 'border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed'
                        } ${slot.popular ? 'ring-2 ring-brand-200' : ''}`}
                        onClick={() => slot.available && setFormData({ ...formData, preferredTime: slot.value })}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <div className="font-semibold text-gray-900">{slot.name}</div>
                            <div className="text-sm text-gray-500">{slot.label}</div>
                          </div>
                          <Clock className="w-5 h-5 text-gray-400" />
                        </div>
                        <div className={`text-sm font-medium ${slot.urgencyColor || 'text-gray-500'}`}>
                          {slot.urgency} • {slot.slots} certified technicians available
                        </div>
                      </button>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Special Instructions */}
            {formData.preferredTime && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <label className="block text-sm font-semibold text-gray-700 mb-4">
                  Any special instructions or concerns?
                </label>
                <textarea
                  placeholder="Fireplace usage, access instructions, specific safety concerns..."
                  className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0 resize-none"
                  rows={4}
                  value={formData.specialInstructions}
                  onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })}
                />
              </motion.div>
            )}
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Additional Safety Services</h2>
              <p className="text-gray-600">Enhance your chimney safety with professional inspections and maintenance</p>
            </div>

            <div className="mb-8">
              <div className="space-y-4">
                {addOnServices.map((addon) => (
                  <motion.div
                    key={addon.id}
                    whileHover={{ scale: 1.02 }}
                    className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all ${
                      formData.addOns.includes(addon.id)
                        ? 'border-brand-500 bg-brand-50'
                        : 'border-gray-200 hover:border-brand-300'
                    }`}
                    onClick={() => {
                      const newAddOns = formData.addOns.includes(addon.id)
                        ? formData.addOns.filter(id => id !== addon.id)
                        : [...formData.addOns, addon.id];
                      setFormData({ ...formData, addOns: newAddOns });
                    }}
                  >
                    {addon.recommended && (
                      <div className="absolute -top-3 left-6 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Safety Recommended
                      </div>
                    )}
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-lg ${
                        formData.addOns.includes(addon.id) ? 'bg-brand-100 text-brand-600' : 'bg-gray-100'
                      }`}>
                        {addon.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-semibold text-gray-900">{addon.name}</h4>
                          <span className="text-lg font-bold text-brand-600">+${addon.price}</span>
                        </div>
                        <p className="text-gray-600 mt-1">{addon.description}</p>
                      </div>
                      {formData.addOns.includes(addon.id) && (
                        <CheckCircle className="w-6 h-6 text-brand-600" />
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Safety Reminder */}
            <div className="p-6 bg-amber-50 rounded-2xl border border-amber-200">
              <h3 className="font-semibold text-amber-900 mb-2 flex items-center gap-2">
                <Lightbulb className="w-4 h-4 text-amber-600" />
                Winter Safety Reminder
              </h3>
              <p className="text-sm text-amber-800">
                The National Fire Protection Association recommends annual chimney inspections. 
                Don't wait - protect your family from carbon monoxide and fire hazards.
              </p>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Contact Information</h2>
              <p className="text-gray-600">Complete your chimney safety service booking</p>
            </div>

            <div className="mb-8">
              <div className="grid grid-cols-2 gap-4">
                <input
                  type="text"
                  placeholder="First Name"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.firstName}
                  onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="Last Name"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.lastName}
                  onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                />
                <input
                  type="email"
                  placeholder="Email Address"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                />
                <input
                  type="tel"
                  placeholder="Phone Number"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="Street Address"
                  className="col-span-2 p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="City"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.city}
                  onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="ZIP Code"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.zipCode}
                  onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                />
              </div>
            </div>

            {/* How did you hear */}
            <div className="mb-8">
              <label className="block text-sm font-semibold text-gray-700 mb-4">
                How did you hear about us? (Optional)
              </label>
              <select
                className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                value={formData.howDidYouHear}
                onChange={(e) => setFormData({ ...formData, howDidYouHear: e.target.value })}
              >
                <option value="">Select an option</option>
                <option value="google">Google Search</option>
                <option value="referral">Friend/Family Referral</option>
                <option value="social">Social Media</option>
                <option value="nextdoor">Nextdoor</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Newsletter */}
            <div className="mb-8">
              <label className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.newsletter}
                  onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
                  className="w-5 h-5 text-brand-600 border-2 border-gray-300 rounded focus:ring-brand-500"
                />
                <div className="text-sm">
                  <div className="font-medium text-gray-900">
                    Get seasonal safety reminders
                  </div>
                  <div className="text-gray-600">
                    Receive chimney maintenance tips and safety updates (you can unsubscribe anytime)
                  </div>
                </div>
              </label>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ scale: [1, 1.1, 1], rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Flame className="w-8 h-8 text-red-600" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Professional Chimney Safety</h1>
                <p className="text-sm text-gray-600">Certified chimney cleaning & safety inspections</p>
              </div>
            </div>
            
            {/* Enhanced Trust badges */}
            <div className="hidden md:flex items-center gap-6">
              <div className="flex items-center gap-2">
                <ShieldCheck className="w-5 h-5 text-red-600" />
                <span className="text-sm font-medium">CSIA Certified</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium">5-Star Safety Rating</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium">Fully Insured</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex items-center">
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: currentStep === index ? 1.1 : 1,
                      backgroundColor: currentStep >= index ? '#dc2626' : '#e5e7eb'
                    }}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white transition-all`}
                  >
                    {currentStep > index ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className={currentStep >= index ? 'text-white' : 'text-gray-600'}>
                        {index + 1}
                      </span>
                    )}
                  </motion.div>
                  <div className="hidden md:block ml-3">
                    <p className={`font-medium ${currentStep >= index ? 'text-red-600' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500">{step.subtitle}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRight className={`mx-4 w-5 h-5 ${
                    currentStep > index ? 'text-red-600' : 'text-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              {renderStep()}
            </AnimatePresence>
          </div>

          {/* Right Sidebar - Price Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-soft p-6 sticky top-32">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Summary</h3>
              
              {formData.propertyType && formData.servicePackage ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">
                        {getServicePackages().find(pkg => pkg.id === formData.servicePackage)?.name}
                      </span>
                      <span className="font-bold text-red-600">
                        ${getServicePackages().find(pkg => pkg.id === formData.servicePackage)?.basePrice}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {propertyTypes.find(type => type.id === formData.propertyType)?.name}
                    </div>
                  </div>

                  {formData.addOns.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Safety Add-ons:</h4>
                      {formData.addOns.map(addonId => {
                        const addon = addOnServices.find(a => a.id === addonId);
                        return addon ? (
                          <div key={addonId} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{addon.name}</span>
                            <span className="font-medium text-gray-900">+${addon.price}</span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  )}

                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold text-gray-900">Total</span>
                      <span className="text-2xl font-bold text-red-600">${calculatePrice()}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center py-8 text-gray-500">
                    <Flame className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>Select property type to see pricing</p>
                    <p className="text-sm">Starting from $149</p>
                  </div>
                </div>
              )}

              {/* Special Offer */}
              <div className="mt-6 p-4 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <span className="font-semibold text-red-800">Fall Safety Special</span>
                </div>
                <div className="text-2xl font-bold text-red-600 mb-1">20% OFF</div>
                <div className="text-sm text-red-700 mb-2">
                  First chimney service + free safety inspection
                </div>
                <div className="text-xs text-red-600 font-medium">
                  Book before winter • Limited certified technicians
                </div>
              </div>

              {/* Features */}
              <div className="mt-6 space-y-3">
                <h4 className="font-medium text-gray-900">Safety Includes:</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-red-600" />
                    <span>Certified safety inspection</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Flame className="w-4 h-4 text-red-600" />
                    <span>Professional cleaning & debris removal</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <AlertTriangle className="w-4 h-4 text-red-600" />
                    <span>Carbon monoxide leak detection</span>
                  </div>
                </div>
              </div>

              {/* Navigation Buttons */}
              <div className="mt-8 space-y-3">
                {currentStep === 3 ? (
                  <Button
                    onClick={handleSubmit}
                    disabled={!isStepValid(currentStep)}
                    className="w-full bg-red-600 hover:bg-red-700"
                  >
                    Secure My Safety Service
                    <ShieldCheck className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={() => handleStepNavigation('next')}
                    disabled={!isStepValid(currentStep)}
                    className="w-full bg-red-600 hover:bg-red-700"
                  >
                    Continue
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
                
                {currentStep > 0 && (
                  <Button
                    variant="outline"
                    onClick={() => handleStepNavigation('prev')}
                    className="w-full"
                  >
                    <ChevronLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description={`Chimney Cleaning - ${getServicePackages().find(pkg => pkg.id === formData.servicePackage)?.name || 'Custom'}`}
          customerEmail={formData.email}
          formData={{
            ...formData,
            serviceType: 'chimney-cleaning'
          }}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedChimneyForm; 
