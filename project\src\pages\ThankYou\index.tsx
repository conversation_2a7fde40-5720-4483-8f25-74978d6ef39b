import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, ArrowRight, Calendar, Clock,
  Mail, Phone, Sparkles, Star,
  AlertCircle, RefreshCw, Home, User
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { BookingDetails } from './components/BookingDetails';
import { SuccessAnimation } from '../../components/animations/SuccessAnimation';

export function ThankYou() {
  const location = useLocation();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  // Extract data from location state
  const formData = location.state?.formData;
  const paymentStatus = location.state?.paymentStatus || 'pending';
  const serviceType = location.state?.serviceType || 'Cleaning Service';
  const bookingDetails = location.state?.bookingDetails;

  // Check URL parameters for payment success (fallback)
  const urlParams = new URLSearchParams(location.search);
  const urlPaymentSuccess = urlParams.get('payment_success') === 'true';

  // Determine final payment status
  const isPaid = paymentStatus === 'paid' || urlPaymentSuccess;
  const isProcessing = bookingDetails?.status === 'processing';

  useEffect(() => {
    // Show loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Auto-show details after animation
      setTimeout(() => setShowDetails(true), 1000);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Handle missing data gracefully
  useEffect(() => {
    if (!isLoading && !formData && !urlPaymentSuccess) {
      // Only redirect if we have no data at all
      const fallbackTimer = setTimeout(() => {
        navigate('/', { 
          state: { 
            message: 'Session expired. Please book your service again.' 
          }
        });
      }, 5000);

      return () => clearTimeout(fallbackTimer);
    }
  }, [isLoading, formData, urlPaymentSuccess, navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-brand-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <div className="relative">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-brand-200 border-t-brand-600 rounded-full mx-auto mb-4"
            />
            <Sparkles className="w-6 h-6 text-brand-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
          </div>
          <p className="text-brand-600 font-medium">Processing your booking...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-brand-50 via-white to-brand-50">
      <Header toggleSidebar={() => {}} />
      
      <main className="pt-20 pb-16 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-brand-100/30 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-emerald-100/20 rounded-full blur-3xl" />
        </div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          {/* Success Animation */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <SuccessAnimation />
          </motion.div>

          {/* Main Success Message */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-center mb-12"
          >
            {/* Status Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.5, type: "spring", stiffness: 200 }}
              className="inline-flex items-center justify-center w-24 h-24 rounded-full mb-6 relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/20 to-brand-400/20 rounded-full backdrop-blur-xl border border-emerald-400/30" />
              {isPaid ? (
                <CheckCircle className="w-12 h-12 text-emerald-600 relative z-10" />
              ) : isProcessing ? (
                <RefreshCw className="w-12 h-12 text-brand-600 relative z-10 animate-spin" />
              ) : (
                <AlertCircle className="w-12 h-12 text-amber-600 relative z-10" />
              )}
            </motion.div>
            
            <motion.h1
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.7 }}
              className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            >
              {isPaid ? 'Booking Confirmed!' : isProcessing ? 'Processing...' : 'Almost There!'}
            </motion.h1>
            
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.9 }}
              className="text-xl text-gray-600 max-w-2xl mx-auto"
            >
              {isPaid 
                ? `Thank you for choosing Empire Pro Cleaning. Your ${serviceType.toLowerCase()} is confirmed!`
                : isProcessing
                ? 'Your payment was successful. We\'re processing your booking details.'
                : 'Your booking information is being processed. Please check your email for updates.'
              }
            </motion.p>
          </motion.div>

          {/* Payment Success Card */}
          <AnimatePresence>
            {isPaid && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: 1.1 }}
                className="mb-8"
              >
                <div className="bg-white/40 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                  <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-emerald-100/80 backdrop-blur-xl mb-4">
                      <CheckCircle className="w-8 h-8 text-emerald-600" />
                    </div>
                    <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                      Payment Successful
                    </h3>
                    <p className="text-gray-600">
                      Your payment has been processed successfully
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div className="bg-white/60 backdrop-blur-xl p-4 rounded-2xl border border-white/30">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 bg-brand-100 rounded-lg flex items-center justify-center mr-3">
                          <Calendar className="w-4 h-4 text-brand-600" />
                        </div>
                        <h4 className="font-medium text-gray-900">Payment Date</h4>
                      </div>
                      <p className="text-gray-600">{new Date().toLocaleDateString()}</p>
                    </div>

                    <div className="bg-white/60 backdrop-blur-xl p-4 rounded-2xl border border-white/30">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 bg-brand-100 rounded-lg flex items-center justify-center mr-3">
                          <Clock className="w-4 h-4 text-brand-600" />
                        </div>
                        <h4 className="font-medium text-gray-900">Payment Time</h4>
                      </div>
                      <p className="text-gray-600">{new Date().toLocaleTimeString()}</p>
                    </div>

                    <div className="bg-white/60 backdrop-blur-xl p-4 rounded-2xl border border-white/30">
                      <div className="flex items-center mb-2">
                        <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
                          <Star className="w-4 h-4 text-emerald-600" />
                        </div>
                        <h4 className="font-medium text-gray-900">Status</h4>
                      </div>
                      <p className="text-emerald-600 font-medium">Confirmed</p>
                    </div>
                  </div>

                  {formData?.emailSent && (
                    <div className="bg-emerald-50/80 backdrop-blur-xl border border-emerald-200/50 rounded-2xl p-4">
                      <div className="flex items-start">
                        <Mail className="w-5 h-5 text-emerald-600 mt-0.5 mr-3" />
                        <div>
                          <h4 className="font-medium text-emerald-900">Confirmation Email Sent</h4>
                          <p className="text-emerald-600 text-sm mt-1">
                            A receipt and booking confirmation has been sent to {formData.email || 'your email address'}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Booking Details */}
          <AnimatePresence>
            {showDetails && formData && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mb-8"
              >
                <BookingDetails formData={formData} />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Next Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5 }}
            className="mb-8"
          >
            <div className="bg-white/40 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
              <h3 className="text-2xl font-semibold text-gray-900 mb-6 text-center">
                What Happens Next?
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.7 }}
                  className="text-center"
                >
                  <div className="w-12 h-12 bg-brand-100/80 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-brand-600 font-bold text-lg">1</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Confirmation Call</h4>
                  <p className="text-gray-600 text-sm">We'll call you within 2 hours to confirm your appointment details</p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.9 }}
                  className="text-center"
                >
                  <div className="w-12 h-12 bg-brand-100/80 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-brand-600 font-bold text-lg">2</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Team Assignment</h4>
                  <p className="text-gray-600 text-sm">Our experienced cleaning team will be assigned to your service</p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 2.1 }}
                  className="text-center"
                >
                  <div className="w-12 h-12 bg-emerald-100/80 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <span className="text-emerald-600 font-bold text-lg">3</span>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">Service Day</h4>
                  <p className="text-gray-600 text-sm">Our team arrives on time and delivers exceptional cleaning service</p>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 2.3 }}
            className="flex flex-col sm:flex-row justify-center gap-4"
          >
            <Button
              size="lg"
              onClick={() => navigate('/accountdashboard')}
              variant="outline"
              className="w-full sm:w-auto bg-white/40 backdrop-blur-xl border-white/30 hover:bg-white/60"
            >
              <User className="mr-2 w-5 h-5" />
              View Account Dashboard
            </Button>
            
            <Button
              size="lg"
              onClick={() => navigate('/')}
              className="w-full sm:w-auto bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800"
            >
              <Home className="mr-2 w-5 h-5" />
              Return to Home
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </motion.div>

          {/* Emergency Contact */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2.5 }}
            className="text-center mt-12"
          >
            <div className="bg-white/30 backdrop-blur-xl border border-white/20 rounded-2xl p-6 inline-block">
              <p className="text-gray-600 mb-2">Need immediate assistance?</p>
              <div className="flex items-center justify-center space-x-4">
                <a 
                  href="tel:+1234567890" 
                  className="flex items-center text-brand-600 hover:text-brand-700 font-medium"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  (*************
                </a>
                <span className="text-gray-300">|</span>
                <a 
                  href="mailto:<EMAIL>" 
                  className="flex items-center text-brand-600 hover:text-brand-700 font-medium"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Contact Support
                </a>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
