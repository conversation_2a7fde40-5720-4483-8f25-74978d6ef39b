import React from 'react';
import { Building2, MapPin, Building, Layers, Users, DoorOpen, Car, Shield } from 'lucide-react';
import { FormLayout } from '../../../../../components/forms/FormLayout';
import { FormSection } from '../../../../../components/forms/FormSection';
import { FormField } from '../../../../../components/forms/FormField';

interface PropertyDetailsProps {
  details: {
    propertyType: string;
    industryType: string;
    squareFootage: number;
    floors: number;
    workstations: number;
    conferenceRooms: number;
    restrooms: number;
    breakRooms: number;
    propertyAddress: string;
    accessHours: string;
    securityRequirements: string;
    parkingAvailable: boolean;
  };
  onChange: (details: any) => void;
}

export function PropertyDetails({ details, onChange }: PropertyDetailsProps) {
  const propertyTypes = [
    'Commercial Office',
    'Medical Office',
    'Executive Suite',
    'Coworking Space',
    'Business Center',
    'Mixed-Use Building',
    'Other'
  ];

  const industryTypes = [
    'Professional Services',
    'Financial Services',
    'Technology',
    'Healthcare',
    'Legal Services',
    'Real Estate',
    'Marketing/Creative',
    'Consulting',
    'Insurance',
    'Other'
  ];

  return (
    <FormLayout
      title="Property Details"
      description="Tell us about your office space"
      icon={<Building2 className="w-6 h-6 text-brand-600" />}
    >
      {/* Basic Information */}
      <FormSection title="Basic Information" required>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField label="Property Type" required>
            <div className="relative">
              <Building2 className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={details.propertyType}
                onChange={(e) => onChange({ ...details, propertyType: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              >
                <option value="">Select property type</option>
                {propertyTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </FormField>

          <FormField label="Industry Type" required>
            <div className="relative">
              <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={details.industryType}
                onChange={(e) => onChange({ ...details, industryType: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              >
                <option value="">Select industry type</option>
                {industryTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </FormField>
        </div>
      </FormSection>

      {/* Space Details */}
      <FormSection title="Space Details" description="Help us understand your facility layout" required>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <FormField label="Square Footage" required>
            <div className="relative">
              <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.squareFootage || ''}
                onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="Total area"
                min="1"
                required
              />
            </div>
          </FormField>

          <FormField label="Floors" required>
            <div className="relative">
              <Layers className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.floors || ''}
                onChange={(e) => onChange({ ...details, floors: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                min="1"
                required
              />
            </div>
          </FormField>

          <FormField label="Workstations" required>
            <div className="relative">
              <Users className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.workstations || ''}
                onChange={(e) => onChange({ ...details, workstations: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                min="0"
                required
              />
            </div>
          </FormField>

          <FormField label="Conference Rooms">
            <div className="relative">
              <Users className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.conferenceRooms || ''}
                onChange={(e) => onChange({ ...details, conferenceRooms: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                min="0"
              />
            </div>
          </FormField>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <FormField label="Restrooms" required>
            <div className="relative">
              <Users className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.restrooms || ''}
                onChange={(e) => onChange({ ...details, restrooms: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                min="1"
                required
              />
            </div>
          </FormField>

          <FormField label="Break Rooms">
            <div className="relative">
              <Users className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.breakRooms || ''}
                onChange={(e) => onChange({ ...details, breakRooms: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                min="0"
              />
            </div>
          </FormField>
        </div>
      </FormSection>

      {/* Location & Access */}
      <FormSection title="Location & Access" required>
        <FormField label="Property Address" required>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={details.propertyAddress}
              onChange={(e) => onChange({ ...details, propertyAddress: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Enter complete address"
              required
            />
          </div>
        </FormField>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField 
            label="Access Hours" 
            description="When can cleaners access the property?"
            required
          >
            <div className="relative">
              <DoorOpen className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={details.accessHours}
                onChange={(e) => onChange({ ...details, accessHours: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="e.g., After 6 PM weekdays"
                required
              />
            </div>
          </FormField>

          <FormField 
            label="Security Requirements"
            description="Any security protocols we need to follow?"
          >
            <div className="relative">
              <Shield className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <textarea
                value={details.securityRequirements}
                onChange={(e) => onChange({ ...details, securityRequirements: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                rows={3}
                placeholder="Security desk check-in, access cards, etc."
              />
            </div>
          </FormField>
        </div>

        <FormField 
          label="Parking"
          description="Is parking available for cleaning staff?"
        >
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={details.parkingAvailable}
              onChange={(e) => onChange({ ...details, parkingAvailable: e.target.checked })}
              className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
            />
            <div className="flex items-center space-x-2">
              <Car className="w-5 h-5 text-gray-400" />
              <span className="text-gray-700">Parking available for cleaning staff</span>
            </div>
          </label>
        </FormField>
      </FormSection>
    </FormLayout>
  );
}
