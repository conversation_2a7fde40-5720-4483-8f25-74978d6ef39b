import React from 'react';

interface FormSectionProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  required?: boolean;
  className?: string;
}

export function FormSection({ children, title, description, required, className }: FormSectionProps) {
  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <div className="flex items-center space-x-2">
          <h4 className="text-lg font-medium text-gray-900">{title}</h4>
          {required && <span className="text-red-500">*</span>}
        </div>
        {description && (
          <p className="text-gray-600 mt-1">{description}</p>
        )}
      </div>
      <div className="space-y-6">
        {children}
      </div>
    </div>
  );
}
