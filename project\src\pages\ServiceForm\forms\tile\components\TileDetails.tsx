import React from 'react';
import { <PERSON><PERSON>, Ruler, <PERSON>P<PERSON>, <PERSON><PERSON>, <PERSON>ize2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield } from 'lucide-react';

interface TileDetailsProps {
  details: {
    tileType: string;
    tileSize: string;
    surfaceArea: number;
    location: string[];
  };
  onChange: (details: any) => void;
}

export function TileDetails({ details, onChange }: TileDetailsProps) {
  const tileTypes = [
    { value: 'ceramic', label: 'Ceramic', description: 'Standard ceramic tiles' },
    { value: 'porcelain', label: 'Porcelain', description: 'Durable porcelain tiles' },
    { value: 'natural-stone', label: 'Natural Stone', description: 'Marble, granite, or slate' },
    { value: 'travertine', label: 'Travertine', description: 'Natural limestone-based stone' },
    { value: 'quarry', label: 'Quarry', description: 'Unglazed ceramic tiles' },
    { value: 'terracotta', label: 'Terracotta', description: 'Clay-based tiles' },
    { value: 'luxury-vinyl', label: 'Luxury Vinyl', description: 'High-end vinyl tiles' },
    { value: 'other', label: 'Other', description: 'Other tile types' }
  ];

  const tileSizes = [
    { value: '12x12', label: '12" x 12"', description: 'Standard square' },
    { value: '18x18', label: '18" x 18"', description: 'Large square' },
    { value: '24x24', label: '24" x 24"', description: 'Extra large square' },
    { value: '12x24', label: '12" x 24"', description: 'Large rectangle' },
    { value: '6x24', label: '6" x 24"', description: 'Plank style' },
    { value: '3x6', label: '3" x 6"', description: 'Subway tile' },
    { value: 'mosaic', label: 'Mosaic', description: '2" x 2" or smaller' },
    { value: 'mixed', label: 'Mixed Sizes', description: 'Various sizes' }
  ];

  const locations = [
    { id: 'lobby', label: 'Lobby/Entrance', icon: MapPin },
    { id: 'hallways', label: 'Hallways', icon: Maximize2 },
    { id: 'restrooms', label: 'Restrooms', icon: Shield },
    { id: 'kitchen', label: 'Kitchen/Break Room', icon: Grid },
    { id: 'offices', label: 'Office Areas', icon: Grid },
    { id: 'conference', label: 'Conference Rooms', icon: Grid },
    { id: 'common', label: 'Common Areas', icon: Grid }
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Grid className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Tile Details</h3>
          <p className="text-gray-600">Tell us about your tile surfaces</p>
        </div>
      </div>

      {/* Tile Type Selection */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Tile Type <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tileTypes.map((type) => (
            <label
              key={type.value}
              className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                details.tileType === type.value
                  ? 'border-brand-500 bg-brand-50 shadow-md'
                  : 'border-gray-200 hover:border-brand-300 hover:shadow-sm'
              }`}
            >
              <input
                type="radio"
                name="tileType"
                value={type.value}
                checked={details.tileType === type.value}
                onChange={(e) => onChange({ ...details, tileType: e.target.value })}
                className="sr-only"
              />
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900">{type.label}</span>
                <Palette className={`w-5 h-5 ${
                  details.tileType === type.value ? 'text-brand-600' : 'text-gray-400'
                }`} />
              </div>
              <p className="text-sm text-gray-600">{type.description}</p>
            </label>
          ))}
        </div>
      </div>

      {/* Tile Size Selection */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Tile Size <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tileSizes.map((size) => (
            <label
              key={size.value}
              className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                details.tileSize === size.value
                  ? 'border-brand-500 bg-brand-50 shadow-md'
                  : 'border-gray-200 hover:border-brand-300 hover:shadow-sm'
              }`}
            >
              <input
                type="radio"
                name="tileSize"
                value={size.value}
                checked={details.tileSize === size.value}
                onChange={(e) => onChange({ ...details, tileSize: e.target.value })}
                className="sr-only"
              />
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-gray-900">{size.label}</span>
                <Maximize2 className={`w-5 h-5 ${
                  details.tileSize === size.value ? 'text-brand-600' : 'text-gray-400'
                }`} />
              </div>
              <p className="text-sm text-gray-600">{size.description}</p>
            </label>
          ))}
        </div>
      </div>

      {/* Surface Area */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Surface Area (sq ft) <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <Ruler className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="number"
            value={details.surfaceArea || ''}
            onChange={(e) => onChange({ ...details, surfaceArea: Number(e.target.value) })}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            placeholder="Total tile area to be cleaned"
            min="1"
            required
          />
        </div>
        <p className="text-sm text-gray-500">
          Include all tiled areas that need cleaning
        </p>
      </div>

      {/* Locations */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Areas to Clean <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {locations.map((loc) => {
            const Icon = loc.icon;
            return (
              <label
                key={loc.id}
                className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  details.location.includes(loc.id)
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300 hover:shadow-sm'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.location.includes(loc.id)}
                  onChange={(e) => {
                    const newLocations = e.target.checked
                      ? [...details.location, loc.id]
                      : details.location.filter(l => l !== loc.id);
                    onChange({ ...details, location: newLocations });
                  }}
                  className="sr-only"
                />
                <Icon className={`w-5 h-5 mr-3 ${
                  details.location.includes(loc.id) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{loc.label}</span>
              </label>
            );
          })}
        </div>
      </div>

      {/* Warning for natural stone */}
      {details.tileType === 'natural-stone' && (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
          <div className="flex items-start">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Natural Stone Care Notice</h4>
              <p className="mt-1 text-sm text-yellow-700">
                Natural stone requires specialized cleaning solutions and techniques. Our team will use appropriate pH-neutral cleaners and sealers specifically designed for your stone type.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
