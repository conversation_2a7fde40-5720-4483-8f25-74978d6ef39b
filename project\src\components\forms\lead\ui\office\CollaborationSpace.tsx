import React from 'react';
import { Users } from 'lucide-react';
import { motion } from 'framer-motion';

interface CollaborationSpaceProps {
  employeeCount: number;
}

export function CollaborationSpace({ employeeCount }: CollaborationSpaceProps) {
  const capacity = Math.ceil(employeeCount * 0.3);
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-blue-50/80 backdrop-blur rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300"
    >
      <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
        <Users className="w-4 h-4 mr-2 text-blue-600" />
        Collaboration Space
      </h4>

      <div className="flex items-center justify-between">
        <div className="flex -space-x-2">
          {Array.from({ length: Math.min(5, capacity) }).map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, x: -20 }}
              animate={{ scale: 1, x: 0 }}
              transition={{ delay: i * 0.1 }}
              className="w-8 h-8 rounded-full bg-blue-100 border-2 border-white 
                        flex items-center justify-center shadow-sm"
            >
              <Users className="w-4 h-4 text-blue-600" />
            </motion.div>
          ))}
        </div>
        <span className="text-sm text-gray-600">
          Capacity: {capacity} people
        </span>
      </div>
    </motion.div>
  );
}
