import React from 'react';

interface CarpetDetailsProps {
  details: {
    material: string;
    condition: string;
    age: string;
  };
  onChange: (details: any) => void;
}

export function CarpetDetails({ details, onChange }: CarpetDetailsProps) {
  return (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Carpet Material
        </label>
        <select
          value={details.material}
          onChange={(e) => onChange({ ...details, material: e.target.value })}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="">Select material type</option>
          <option value="nylon">Nylon</option>
          <option value="polyester">Polyester</option>
          <option value="wool">Wool</option>
          <option value="olefin">Olefin</option>
          <option value="blend">Blend</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Current Condition
        </label>
        <select
          value={details.condition}
          onChange={(e) => onChange({ ...details, condition: e.target.value })}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="">Select condition</option>
          <option value="excellent">Excellent</option>
          <option value="good">Good</option>
          <option value="fair">Fair</option>
          <option value="poor">Poor</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Carpet Age
        </label>
        <select
          value={details.age}
          onChange={(e) => onChange({ ...details, age: e.target.value })}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="">Select carpet age</option>
          <option value="0-2">0-2 years</option>
          <option value="3-5">3-5 years</option>
          <option value="6-10">6-10 years</option>
          <option value="10+">10+ years</option>
        </select>
      </div>
    </div>
  );
}
