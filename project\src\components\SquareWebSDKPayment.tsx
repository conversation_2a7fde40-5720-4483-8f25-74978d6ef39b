/**
 * Square Web SDK Payment Component
 * Clean, modular payment form using Square Web SDK
 */

import React, { useEffect, useRef, useState } from 'react';
import { squarePaymentService } from '../lib/square';
import type { PaymentRequest, PaymentResult } from '../lib/square';

interface SquareWebSDKPaymentProps {
  amount: number; // Amount in cents
  currency?: string;
  description?: string;
  customerEmail?: string;
  orderId?: string;
  onPaymentSuccess: (result: PaymentResult) => void;
  onPaymentError: (error: string) => void;
  className?: string;
}

export const SquareWebSDKPayment: React.FC<SquareWebSDKPaymentProps> = ({
  amount,
  currency = 'USD',
  description,
  customerEmail,
  orderId,
  onPaymentSuccess,
  onPaymentError,
  className = ''
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    const initializePaymentForm = async () => {
      try {
        console.log('Starting Square SDK initialization');
        await squarePaymentService.initialize();
        console.log('SDK initialized successfully');
        
        if (cardRef.current) {
          console.log('Attaching card form');
          await squarePaymentService.attachCard(cardRef.current);
          console.log('Card form attached');
          setIsInitialized(true);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to initialize payment form';
        console.error('Initialization error:', error);
        setInitError(errorMessage);
      }
    };

    initializePaymentForm();

    // Cleanup on unmount
    return () => {
      squarePaymentService.destroy();
    };
  }, [onPaymentError]);

  const handlePayment = async () => {
    if (!isInitialized) {
      onPaymentError('Payment form not ready');
      return;
    }

    setIsLoading(true);

    try {
      const paymentRequest: PaymentRequest = {
        amount,
        currency,
        description,
        customerEmail,
        orderId
      };

      const result = await squarePaymentService.processPayment(paymentRequest);
      
      if (result.success) {
        onPaymentSuccess(result);
      } else {
        onPaymentError(result.error || 'Payment failed');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment processing failed';
      onPaymentError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  if (initError) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-lg ${className}`}>
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Payment System Error</h3>
            <p className="text-sm text-red-700 mt-1">{initError}</p>
            <p className="text-xs text-red-600 mt-2">Please check your connection and try again, or contact support if the issue persists.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">
          Secure Payment
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Amount: <span className="font-medium">${(amount / 100).toFixed(2)} {currency}</span>
        </p>
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
      </div>

      {/* Payment Form */}
      <div className="px-6 py-6">
        {/* Square Card Form Container */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Information
          </label>
          <div 
            ref={cardRef}
            className="border border-gray-300 rounded-md p-4 min-h-[120px] bg-white"
            style={{ 
              minHeight: '120px',
              border: '1px solid #d1d5db',
              borderRadius: '6px'
            }}
          />
        </div>

        {/* Payment Button */}
        <button
          onClick={handlePayment}
          disabled={!isInitialized || isLoading}
          className={`w-full py-3 px-4 rounded-md font-medium transition-colors duration-200 ${
            !isInitialized || isLoading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing Payment...
            </div>
          ) : (
            `Pay $${(amount / 100).toFixed(2)}`
          )}
        </button>

        {/* Security Notice */}
        <div className="mt-4 text-center">
          <div className="flex items-center justify-center text-sm text-gray-500">
            <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            Secured by Square
          </div>
          <p className="text-xs text-gray-400 mt-1">
            Your payment information is encrypted and secure
          </p>
        </div>
      </div>
    </div>
  );
};
