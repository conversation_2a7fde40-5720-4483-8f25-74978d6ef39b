import React from 'react';
import { FormField } from '../../components/FormFields';

export function ServiceScheduling() {
  const today = new Date().toISOString().split('T')[0];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField label="Preferred Date" required>
          <input
            type="date"
            name="serviceDate"
            min={today}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg"
            required
          />
        </FormField>

        <FormField label="Preferred Time" required>
          <select
            name="serviceTime"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg"
            required
          >
            <option value="">Select time</option>
            <option value="morning">Morning (8AM - 12PM)</option>
            <option value="afternoon">Afternoon (12PM - 4PM)</option>
            <option value="evening">Evening (4PM - 8PM)</option>
          </select>
        </FormField>
      </div>

      <FormField label="Furniture Moving">
        <select
          name="furnitureMoving"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="none">No furniture moving needed</option>
          <option value="light">Light furniture only</option>
          <option value="full">Full furniture moving service</option>
        </select>
      </FormField>
    </div>
  );
}
