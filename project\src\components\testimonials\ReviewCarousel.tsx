import React, { useState } from 'react';
import { Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const reviews = [
  { author: "<PERSON>", content: "Exceptional service! They transformed our office space completely. The team was professional, efficient, and thorough." },
  { author: "<PERSON>", content: "Outstanding medical facility cleaning. Their attention to sanitization protocols is impressive." },
  { author: "<PERSON>", content: "Best commercial cleaning service in NYC! Professional and reliable every time." },
  { author: "<PERSON>", content: "Great service results. Very satisfied with their work and attention to detail." },
  { author: "<PERSON>", content: "Fantastic service! The booking process was seamless and the team was punctual." },
  { author: "<PERSON>", content: "They've been cleaning our retail spaces for years. Consistently excellent service!" },
];

export function ReviewCarousel() {
  const [index, setIndex] = useState(0);

  const paginate = (newDirection: number) => {
    setIndex(prevIndex => (prevIndex + newDirection + reviews.length) % reviews.length);
  };

  return (
    <section className="py-24 sm:py-32 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">What Our Clients Say</h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto">
            We're proud to have earned the trust of businesses and homeowners across the country.
          </p>
        </motion.div>

        <div className="relative h-48 sm:h-56 flex items-center justify-center">
          <AnimatePresence custom={index}>
          <motion.div 
              key={index}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}
              className="absolute w-full max-w-2xl text-center px-4 sm:px-8"
          >
              <div className="flex justify-center mb-3 sm:mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-lg sm:text-xl italic text-white/90 mb-4 sm:mb-6 px-2">"{reviews[index].content}"</p>
              <p className="font-semibold text-white text-sm sm:text-base">- {reviews[index].author}</p>
              </motion.div>
            </AnimatePresence>
          </div>

        <div className="flex items-center justify-center mt-6 sm:mt-8 gap-3 sm:gap-4">
          <motion.button
            onClick={() => paginate(-1)}
            className="p-2 sm:p-3 rounded-full bg-white/10 border border-white/20 transition-colors hover:bg-white/20"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            >
            <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </motion.button>
          <motion.button
            onClick={() => paginate(1)}
            className="p-2 sm:p-3 rounded-full bg-white/10 border border-white/20 transition-colors hover:bg-white/20"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronRight className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </motion.button>
        </div>
      </div>
    </section>
  );
}
