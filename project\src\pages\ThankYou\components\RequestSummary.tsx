import React from 'react';
import { Calendar, MapPin, Clock, ClipboardList } from 'lucide-react';
import { motion } from 'framer-motion';
import type { FormData } from '../../ServiceForm/forms/carpet/types';

interface RequestSummaryProps {
  formData?: FormData;
}

export function RequestSummary({ formData }: RequestSummaryProps) {
  if (!formData) return null;

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.2 }}
      className="bg-white rounded-2xl shadow-xl p-8 mb-8"
    >
      <h2 className="text-2xl font-semibold text-gray-900 mb-6">
        Request Details
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="flex items-start space-x-3">
            <ClipboardList className="w-5 h-5 text-brand-600 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">Service Type</h3>
              <p className="text-gray-600">{formData.serviceType}</p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <MapPin className="w-5 h-5 text-brand-600 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">Location</h3>
              <p className="text-gray-600">{formData.propertyDetails.propertyAddress}</p>
              <p className="text-sm text-gray-500">
                {formData.propertyDetails.squareFootage} sq ft
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <div className="flex items-start space-x-3">
            <Calendar className="w-5 h-5 text-brand-600 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">Scheduled Date</h3>
              <p className="text-gray-600">
                {new Date(formData.schedule.date).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Clock className="w-5 h-5 text-brand-600 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">Time Preference</h3>
              <p className="text-gray-600">{formData.schedule.timeSlot}</p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
