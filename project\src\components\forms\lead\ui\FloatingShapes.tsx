import React from 'react';

export function FloatingShapes() {
  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      <div className="absolute inset-0 bg-gradient-to-br from-brand-50/20 via-white to-brand-50/20" />
      
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-brand-100/20 rounded-full animate-float" />
        <div className="absolute top-1/3 right-1/3 w-72 h-72 bg-brand-200/20 rounded-full animate-float-delay-2" />
        <div className="absolute bottom-1/4 left-1/3 w-96 h-96 bg-brand-50/20 rounded-full animate-float" />
      </div>
    </div>
  );
}
