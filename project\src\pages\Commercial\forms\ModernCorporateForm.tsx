import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Briefcase, Heart, Phone, Mail, User,
  ArrowRight, Sparkles, CheckCircle, Clock, X, AlertCircle
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { AnimatedBackground } from '../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../lib/auth/AuthProvider';
import { submitBookingForm } from '../../../lib/api/bookingForms';

interface CorporateFormData {
  servicePackage: string;
  facilityType: string;
  companySize: string;
  squareFootage: number;
  floors: number;
  workstations: number;
  meetingRooms: number;
  restrooms: number;
  facilityAddress: string;
  accessHours: string;
  securityRequirements: string;
  parkingAvailable: boolean;
  serviceFrequency: string;
  preferredTime: string;
  priorityAreas: string[];
  additionalServices: string[];
  specialInstructions: string;
  startDate: string;
  contractLength: string;
  budgetRange: string;
  // Contact information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName: string;
  jobTitle: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  facilityAddress?: string;
  facilityType?: string;
  companySize?: string;
  squareFootage?: string;
  serviceFrequency?: string;
  preferredTime?: string;
  servicePackage?: string;
}

const steps = [
  { id: 1, name: 'Service Type' },
  { id: 2, name: 'Facility Details' },
  { id: 3, name: 'Cleaning Scope' },
  { id: 4, name: 'Services' },
  { id: 5, name: 'Schedule' },
  { id: 6, name: 'Contact' },
];

const servicePackages = [
  { id: 'standard', name: 'Standard Corporate Package', description: 'Regular maintenance for corporate offices', icon: <Building /> },
  { id: 'executive', name: 'Executive Corporate Suite', description: 'Premium cleaning for executive facilities', icon: <Briefcase /> },
  { id: 'healthcare', name: 'Healthcare Corporate', description: 'Specialized cleaning for medical corporations', icon: <Heart /> },
  { id: 'enterprise', name: 'Enterprise Package', description: 'Full-service cleaning for large corporations', icon: <Sparkles /> },
];

// TODO: These will be used in steps 2-5 (to be implemented)
/*
const facilityTypes = [
  { id: 'headquarters', name: 'Corporate Headquarters' },
  { id: 'branch-office', name: 'Branch Office' },
  { id: 'regional-office', name: 'Regional Office' },
  { id: 'satellite-office', name: 'Satellite Office' },
  { id: 'co-working', name: 'Co-working Space' },
  { id: 'mixed-use', name: 'Mixed-Use Building' },
];

const companySizes = [
  { id: 'startup', name: 'Startup (1-50 employees)' },
  { id: 'small', name: 'Small Business (51-200 employees)' },
  { id: 'medium', name: 'Medium Enterprise (201-1000 employees)' },
  { id: 'large', name: 'Large Corporation (1000+ employees)' },
];

const serviceFrequencies = [
  { id: 'daily', name: 'Daily Service' },
  { id: 'weekly', name: 'Weekly Service' },
  { id: 'bi-weekly', name: 'Bi-Weekly Service' },
  { id: 'monthly', name: 'Monthly Service' },
];

const preferredTimes = [
  { id: 'early-morning', name: 'Early Morning (5AM - 8AM)' },
  { id: 'after-hours', name: 'After Hours (6PM - 10PM)' },
  { id: 'weekend', name: 'Weekend Service' },
  { id: 'overnight', name: 'Overnight (10PM - 5AM)' },
];

const priorityAreas = [
  { id: 'executive', name: 'Executive Offices' },
  { id: 'lobby', name: 'Lobby & Reception' },
  { id: 'meeting', name: 'Meeting Rooms' },
  { id: 'workspace', name: 'Open Workspace' },
  { id: 'cafeteria', name: 'Cafeteria/Break Areas' },
  { id: 'server', name: 'Server Rooms' },
];

const additionalServices = [
  { id: 'carpet-deep', name: 'Deep Carpet Cleaning' },
  { id: 'window-exterior', name: 'Exterior Window Cleaning' },
  { id: 'floor-refinishing', name: 'Floor Refinishing' },
  { id: 'sanitization-deep', name: 'Deep Sanitization' },
  { id: 'supply-management', name: 'Supply Management' },
  { id: 'waste-recycling', name: 'Waste & Recycling Management' },
];
*/

// Enhanced Email Validation
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Enhanced Phone Number Formatting
const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

// Phone Number Validation
const validatePhoneNumber = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length === 10 || (cleaned.length === 11 && cleaned[0] === '1');
};

// Address Validation
const validateAddress = (address: string): boolean => {
  return address.trim().length >= 10 && /\d/.test(address) && /[a-zA-Z]/.test(address);
};

// TODO: Modern custom select component (to be used in steps 2-5)
/*
const ModernSelect = ({ label, value, onChange, options, placeholder }: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void, 
  options: { id: string, name: string }[], 
  placeholder: string 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-semibold text-white block">{label}</label>
      <div className="relative">
        <motion.button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-left text-white focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300 flex items-center justify-between"
          whileTap={{ scale: 0.98 }}
        >
          <span className={value ? 'text-white' : 'text-gray-400'}>
            {value ? options.find(opt => opt.id === value)?.name : placeholder}
          </span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRight className="w-5 h-5 rotate-90" />
          </motion.div>
        </motion.button>
        
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl overflow-hidden z-50 shadow-2xl"
            >
              {options.map((option) => (
                <motion.button
                  key={option.id}
                  type="button"
                  onClick={() => {
                    onChange(option.id);
                    setIsOpen(false);
                  }}
                  className="w-full p-4 text-left text-white hover:bg-white/10 transition-colors duration-200 border-b border-white/10 last:border-b-0"
                  whileHover={{ backgroundColor: 'rgba(255,255,255,0.1)' }}
                >
                  {option.name}
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};
*/

const ModernCorporateForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<Partial<CorporateFormData>>({
    servicePackage: 'standard',
    priorityAreas: [],
    additionalServices: [],
    parkingAvailable: false,
    squareFootage: 5000,
    floors: 2,
    workstations: 50,
    meetingRooms: 3,
    restrooms: 4,
  });

  // Save form data to localStorage on changes
  useEffect(() => {
    localStorage.setItem('corporateFormData', JSON.stringify(formData));
  }, [formData]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('corporateFormData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Enhanced form field validation
  const validateField = (fieldName: keyof FormErrors, value: string): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value) return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
        if (!value || value.trim().length < 2) return 'First name must be at least 2 characters';
        break;
      case 'lastName':
        if (!value || value.trim().length < 2) return 'Last name must be at least 2 characters';
        break;
      case 'companyName':
        if (!value || value.trim().length < 2) return 'Company name is required';
        break;
      case 'facilityAddress':
        if (!value) return 'Facility address is required';
        if (!validateAddress(value)) return 'Please enter a complete address with street number and name';
        break;
      case 'facilityType':
        if (!value) return 'Please select a facility type';
        break;
      case 'companySize':
        if (!value) return 'Please select a company size';
        break;
      case 'squareFootage':
        if (!value || parseInt(value) < 500) return 'Square footage must be at least 500 sq ft';
        break;
      case 'serviceFrequency':
        if (!value) return 'Please select a service frequency';
        break;
      case 'preferredTime':
        if (!value) return 'Please select a preferred time';
        break;
    }
    return undefined;
  };

  // Enhanced handleNext with validation
  const handleNext = () => {
    if (currentStep < steps.length) {
      setIsLoading(true);
      
      // Validate current step
      const errors: FormErrors = {};
      
      if (currentStep === 1) {
        if (!formData.servicePackage) {
          // Show a temporary error message for step 1
          setFormErrors({ servicePackage: 'Please select a service package' });
          setIsLoading(false);
          return;
        }
      } else if (currentStep === 2) {
        errors.companySize = validateField('companySize', formData.companySize || '');
        errors.squareFootage = validateField('squareFootage', formData.squareFootage?.toString() || '');
        errors.facilityAddress = validateField('facilityAddress', formData.facilityAddress || '');
      } else if (currentStep === 3) {
        errors.serviceFrequency = validateField('serviceFrequency', formData.serviceFrequency || '');
        errors.preferredTime = validateField('preferredTime', formData.preferredTime || '');
      } else if (currentStep === 6) {
        errors.firstName = validateField('firstName', formData.firstName || '');
        errors.lastName = validateField('lastName', formData.lastName || '');
        errors.email = validateField('email', formData.email || '');
        errors.phone = validateField('phone', formData.phone || '');
        errors.companyName = validateField('companyName', formData.companyName || '');
      }
      
      // Filter out undefined errors
      const filteredErrors = Object.fromEntries(
        Object.entries(errors).filter((entry) => entry[1] !== undefined)
      );
      
      setFormErrors(filteredErrors);
      
      if (Object.keys(filteredErrors).length === 0) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1);
          setIsLoading(false);
        }, 500);
      } else {
        setIsLoading(false);
        // Scroll to first error
        setTimeout(() => {
          const firstErrorElement = document.querySelector('.text-red-400');
          if (firstErrorElement) {
            firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.servicePackage && !formErrors.servicePackage;
      case 2:
        return formData.companySize && 
               formData.squareFootage && 
               formData.facilityAddress &&
               !formErrors.companySize &&
               !formErrors.squareFootage &&
               !formErrors.facilityAddress;
      case 3:
        return formData.serviceFrequency && 
               formData.preferredTime &&
               !formErrors.serviceFrequency &&
               !formErrors.preferredTime;
      case 6:
        return formData.firstName && 
               formData.lastName && 
               formData.email && 
               formData.phone && 
               formData.companyName &&
               !formErrors.firstName &&
               !formErrors.lastName &&
               !formErrors.email &&
               !formErrors.phone &&
               !formErrors.companyName;
      default:
        return true;
    }
  };

  // Enhanced input handlers with validation
  const handleEmailChange = (value: string) => {
    setFormData({...formData, email: value});
    const error = validateField('email', value);
    setFormErrors(prev => ({ ...prev, email: error }));
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    setFormData({...formData, phone: formatted});
    const error = validateField('phone', value);
    setFormErrors(prev => ({ ...prev, phone: error }));
  };

  // TODO: These handlers will be used in steps 2-5 (to be implemented)
  /*
  const handleAddressChange = (value: string) => {
    setFormData({...formData, facilityAddress: value});
    const error = validateField('facilityAddress', value);
    setFormErrors(prev => ({ ...prev, facilityAddress: error }));
  };
  */

  const handleNameChange = (field: 'firstName' | 'lastName', value: string) => {
    setFormData({...formData, [field]: value});
    const error = validateField(field, value);
    setFormErrors(prev => ({ ...prev, [field]: error }));
  };

  const handleCompanyNameChange = (value: string) => {
    setFormData({...formData, companyName: value});
    const error = validateField('companyName', value);
    setFormErrors(prev => ({ ...prev, companyName: error }));
  };

  /*
  const handlePriorityAreaToggle = (areaId: string) => {
    const currentAreas = formData.priorityAreas || [];
    if (currentAreas.includes(areaId)) {
      setFormData({
        ...formData,
        priorityAreas: currentAreas.filter(id => id !== areaId)
      });
    } else {
      setFormData({
        ...formData,
        priorityAreas: [...currentAreas, areaId]
      });
    }
  };

  const handleAdditionalServiceToggle = (serviceId: string) => {
    const currentServices = formData.additionalServices || [];
    if (currentServices.includes(serviceId)) {
      setFormData({
        ...formData,
        additionalServices: currentServices.filter(id => id !== serviceId)
      });
    } else {
      setFormData({
        ...formData,
        additionalServices: [...currentServices, serviceId]
      });
    }
  };
  */

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate all contact fields before submission
      const contactErrors: FormErrors = {};
      contactErrors.firstName = validateField('firstName', formData.firstName || '');
      contactErrors.lastName = validateField('lastName', formData.lastName || '');
      contactErrors.email = validateField('email', formData.email || '');
      contactErrors.phone = validateField('phone', formData.phone || '');
      contactErrors.companyName = validateField('companyName', formData.companyName || '');
      
      const filteredContactErrors = Object.fromEntries(
        Object.entries(contactErrors).filter((entry) => entry[1] !== undefined)
      );
      
      if (Object.keys(filteredContactErrors).length > 0) {
        setFormErrors(filteredContactErrors);
        setIsLoading(false);
        return;
      }

      // Submit form data using the centralized API
      const submissionData = {
        serviceType: 'corporate-services',
        requestType: 'estimate',
        submittedAt: new Date().toISOString(),
        contactInfo: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle
        },
        facilityInfo: {
          address: formData.facilityAddress,
          facilityType: formData.facilityType,
          companySize: formData.companySize,
          squareFootage: formData.squareFootage
        },
        serviceDetails: {
          servicePackage: formData.servicePackage,
          serviceFrequency: formData.serviceFrequency,
          preferredTime: formData.preferredTime,
          priorityAreas: formData.priorityAreas,
          additionalServices: formData.additionalServices,
          specialInstructions: formData.specialInstructions,
          startDate: formData.startDate,
          contractLength: formData.contractLength,
          budgetRange: formData.budgetRange
        }
      };
      
      console.log('Submitting corporate booking with data:', submissionData);
      
      // Use the centralized booking API
      const result = await submitBookingForm(submissionData);
      
      if (result.success) {
        // Clear form data from localStorage after successful submission
        localStorage.removeItem('corporateFormData');
        
        // Show success popup
        setShowSuccessPopup(true);
      } else {
        console.error('Booking submission failed:', result.error);
        // Handle API error - show user-friendly message
        alert('There was an error submitting your booking. Please try again or contact support.');
      }
      
    } catch (error) {
      console.error('Error submitting corporate booking:', error);
      // Save form data to localStorage for retry
      localStorage.setItem('corporateFormData', JSON.stringify(formData));
      // Show user-friendly error message
      alert('There was an error submitting your booking. Your data has been saved and you can try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClosePopup = () => {
    setShowSuccessPopup(false);
    // If user is authenticated, take them to dashboard; otherwise to login page
    if (user) {
      navigate('/accountdashboard');
    } else {
      // Store the intent to redirect to dashboard after login
      localStorage.setItem('redirectAfterLogin', '/accountdashboard');
      navigate('/auth/login');
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress Bar */}
          <div className="mb-12">
            {/* Step Counter */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">
                  Step {currentStep} of {steps.length}
                </span>
              </div>
            </div>
            
            {/* Progress Line */}
            <div className="relative">
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"
                  initial={{ width: '16.67%' }}
                  animate={{ width: `${(currentStep / steps.length) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                />
              </div>
              
              {/* Step Dots */}
              <div className="absolute inset-0 flex items-center justify-between px-1">
                {steps.map((step) => (
                  <motion.div
                    key={step.id}
                    className={`w-4 h-4 rounded-full border-2 ${
                      currentStep >= step.id 
                        ? 'bg-emerald-400 border-emerald-400' 
                        : 'bg-white/10 border-white/30'
                    }`}
                    animate={{
                      scale: currentStep === step.id ? 1.2 : 1,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </div>
            
            {/* Current Step Label */}
            <div className="text-center mt-4">
              <h1 className="text-xl font-semibold text-white">
                {steps[currentStep - 1]?.name}
              </h1>
            </div>
          </div>

          {/* Form Content */}
          <motion.div 
            className="rounded-3xl shadow-2xl p-8 md:p-12"
            style={{
              background: 'rgba(255, 255, 255, 0.08)',
              border: '1px solid rgba(255, 255, 255, 0.15)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-white mb-3">Choose Your Corporate Package</h2>
                    <p className="text-gray-300 text-lg">Select the corporate cleaning package that best fits your business needs</p>
                    {formErrors.servicePackage && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center justify-center gap-2 mt-4 text-red-400 text-sm"
                      >
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.servicePackage}
                      </motion.div>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {servicePackages.map((pkg) => (
                      <motion.div
                        key={pkg.id}
                        whileHover={{ scale: 1.02, y: -4 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          setFormData({ ...formData, servicePackage: pkg.id });
                          // Clear any service package error
                          if (formErrors.servicePackage) {
                            setFormErrors(prev => ({ ...prev, servicePackage: undefined }));
                          }
                        }}
                        className={`group relative p-8 rounded-2xl cursor-pointer transition-all duration-300 ${
                          formData.servicePackage === pkg.id 
                            ? 'bg-gradient-to-br from-emerald-500/20 to-green-600/20 border-2 border-emerald-400 shadow-lg shadow-emerald-400/20' 
                            : 'bg-white/5 border-2 border-white/10 hover:border-white/30 hover:bg-white/10'
                        }`}
                      >
                        {/* Selection Indicator */}
                        {formData.servicePackage === pkg.id && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-4 right-4 w-6 h-6 bg-emerald-400 rounded-full flex items-center justify-center"
                          >
                            <div className="w-3 h-3 bg-white rounded-full" />
                          </motion.div>
                        )}
                        
                        <div className="flex items-start gap-4">
                          <div className={`p-3 rounded-xl ${
                            formData.servicePackage === pkg.id ? 'bg-emerald-400/20 text-blue-300' : 'bg-white/10 text-gray-300 group-hover:text-white'
                          } transition-colors duration-300`}>
                            {pkg.icon}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-bold text-white text-lg mb-2">{pkg.name}</h3>
                            <p className="text-gray-300 text-sm leading-relaxed">{pkg.description}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={() => navigate('/')}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Facility Details</h2>
                  <p className="text-gray-300 mb-6">Tell us about your corporate facility and requirements.</p>
                  
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Facility Address <span className="text-red-400">*</span>
                        </label>
                        <input 
                          type="text" 
                          placeholder="123 Corporate Blvd, Suite 400"
                          className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                            formErrors.facilityAddress 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-white/20 focus:border-emerald-400'
                          }`}
                          value={formData.facilityAddress || ''}
                          onChange={(e) => {
                            setFormData({...formData, facilityAddress: e.target.value});
                            const error = validateField('facilityAddress', e.target.value);
                            setFormErrors(prev => ({ ...prev, facilityAddress: error }));
                          }}
                        />
                        {formErrors.facilityAddress && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.facilityAddress}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Company Size <span className="text-red-400">*</span>
                        </label>
                                                 <select 
                           className={`w-full bg-white/10 p-3 rounded-lg border text-white focus:outline-none transition-colors duration-300 ${
                             formErrors.companySize 
                               ? 'border-red-400 focus:border-red-400' 
                               : 'border-white/20 focus:border-emerald-400'
                           }`}
                           style={{ backgroundColor: 'rgba(255,255,255,0.1)' }}
                           value={formData.companySize || ''}
                           onChange={(e) => {
                             setFormData({...formData, companySize: e.target.value});
                             const error = validateField('companySize', e.target.value);
                             setFormErrors(prev => ({ ...prev, companySize: error }));
                           }}
                         >
                           <option value="" style={{ backgroundColor: '#1e293b', color: '#9ca3af' }}>Select company size</option>
                           <option value="startup" style={{ backgroundColor: '#1e293b', color: '#ffffff' }}>Startup (1-50 employees)</option>
                           <option value="small" style={{ backgroundColor: '#1e293b', color: '#ffffff' }}>Small Business (51-200 employees)</option>
                           <option value="medium" style={{ backgroundColor: '#1e293b', color: '#ffffff' }}>Medium Enterprise (201-1000 employees)</option>
                           <option value="large" style={{ backgroundColor: '#1e293b', color: '#ffffff' }}>Large Corporation (1000+ employees)</option>
                         </select>
                        {formErrors.companySize && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.companySize}
                          </motion.div>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Square Footage <span className="text-red-400">*</span>
                        </label>
                        <input 
                          type="number" 
                          placeholder="5000"
                          min="500"
                          className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                            formErrors.squareFootage 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-white/20 focus:border-emerald-400'
                          }`}
                          value={formData.squareFootage || ''}
                          onChange={(e) => {
                            setFormData({...formData, squareFootage: parseInt(e.target.value) || 0});
                            const error = validateField('squareFootage', e.target.value);
                            setFormErrors(prev => ({ ...prev, squareFootage: error }));
                          }}
                        />
                        {formErrors.squareFootage && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.squareFootage}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">Floors</label>
                        <input 
                          type="number" 
                          placeholder="2"
                          min="1"
                          className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400"
                          value={formData.floors || ''}
                          onChange={(e) => setFormData({...formData, floors: parseInt(e.target.value) || 1})}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Validation Summary for Step 2 */}
                  {(formErrors.companySize || formErrors.squareFootage || formErrors.facilityAddress) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-lg p-4 mt-6"
                    >
                      <div className="flex items-start gap-3">
                        <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="text-red-400 font-medium mb-2">Please complete the following:</h4>
                          <ul className="space-y-1 text-sm text-red-300">
                            {formErrors.facilityAddress && <li>• {formErrors.facilityAddress}</li>}
                            {formErrors.companySize && <li>• {formErrors.companySize}</li>}
                            {formErrors.squareFootage && <li>• {formErrors.squareFootage}</li>}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Service Schedule</h2>
                  <p className="text-gray-300 mb-6">When and how often would you like cleaning service?</p>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-medium text-white mb-3 block">
                        Service Frequency <span className="text-red-400">*</span>
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[
                          { id: 'daily', name: 'Daily Service', desc: 'Monday through Friday' },
                          { id: 'weekly', name: 'Weekly Service', desc: 'Once per week' },
                          { id: 'bi-weekly', name: 'Bi-Weekly Service', desc: 'Every two weeks' },
                          { id: 'monthly', name: 'Monthly Service', desc: 'Once per month' }
                        ].map((freq) => (
                          <motion.div
                            key={freq.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => {
                              setFormData({ ...formData, serviceFrequency: freq.id });
                              const error = validateField('serviceFrequency', freq.id);
                              setFormErrors(prev => ({ ...prev, serviceFrequency: error }));
                            }}
                            className={`p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                              formData.serviceFrequency === freq.id 
                                ? 'bg-gradient-to-br from-emerald-500/20 to-green-600/20 border-2 border-emerald-400' 
                                : 'bg-white/5 border-2 border-white/10 hover:border-white/30'
                            }`}
                          >
                            <h3 className="font-semibold text-white">{freq.name}</h3>
                            <p className="text-gray-300 text-sm">{freq.desc}</p>
                          </motion.div>
                        ))}
                      </div>
                      {formErrors.serviceFrequency && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4" />
                          {formErrors.serviceFrequency}
                        </motion.div>
                      )}
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-white mb-3 block">
                        Preferred Time <span className="text-red-400">*</span>
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[
                          { id: 'early-morning', name: 'Early Morning', desc: '5AM - 8AM' },
                          { id: 'after-hours', name: 'After Hours', desc: '6PM - 10PM' },
                          { id: 'weekend', name: 'Weekend Service', desc: 'Saturday or Sunday' },
                          { id: 'overnight', name: 'Overnight', desc: '10PM - 5AM' }
                        ].map((time) => (
                          <motion.div
                            key={time.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => {
                              setFormData({ ...formData, preferredTime: time.id });
                              const error = validateField('preferredTime', time.id);
                              setFormErrors(prev => ({ ...prev, preferredTime: error }));
                            }}
                            className={`p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                              formData.preferredTime === time.id 
                                ? 'bg-gradient-to-br from-emerald-500/20 to-green-600/20 border-2 border-emerald-400' 
                                : 'bg-white/5 border-2 border-white/10 hover:border-white/30'
                            }`}
                          >
                            <h3 className="font-semibold text-white">{time.name}</h3>
                            <p className="text-gray-300 text-sm">{time.desc}</p>
                          </motion.div>
                        ))}
                      </div>
                      {formErrors.preferredTime && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4" />
                          {formErrors.preferredTime}
                        </motion.div>
                      )}
                    </div>
                  </div>

                  {/* Validation Summary for Step 3 */}
                  {(formErrors.serviceFrequency || formErrors.preferredTime) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-lg p-4 mt-6"
                    >
                      <div className="flex items-start gap-3">
                        <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="text-red-400 font-medium mb-2">Please complete the following:</h4>
                          <ul className="space-y-1 text-sm text-red-300">
                            {formErrors.serviceFrequency && <li>• {formErrors.serviceFrequency}</li>}
                            {formErrors.preferredTime && <li>• {formErrors.preferredTime}</li>}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Priority Areas</h2>
                  <p className="text-gray-300 mb-6">Select the areas that need special attention in your facility.</p>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-medium text-white mb-3 block">Priority Areas (Optional)</label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {[
                          { id: 'executive', name: 'Executive Offices' },
                          { id: 'lobby', name: 'Lobby & Reception' },
                          { id: 'meeting', name: 'Meeting Rooms' },
                          { id: 'workspace', name: 'Open Workspace' },
                          { id: 'cafeteria', name: 'Cafeteria/Break Areas' },
                          { id: 'server', name: 'Server Rooms' }
                        ].map((area) => (
                          <motion.div
                            key={area.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => {
                              const currentAreas = formData.priorityAreas || [];
                              if (currentAreas.includes(area.id)) {
                                setFormData({
                                  ...formData,
                                  priorityAreas: currentAreas.filter(id => id !== area.id)
                                });
                              } else {
                                setFormData({
                                  ...formData,
                                  priorityAreas: [...currentAreas, area.id]
                                });
                              }
                            }}
                            className={`p-3 rounded-lg cursor-pointer transition-all duration-300 text-center ${
                              (formData.priorityAreas || []).includes(area.id)
                                ? 'bg-gradient-to-br from-emerald-500/20 to-green-600/20 border-2 border-emerald-400' 
                                : 'bg-white/5 border-2 border-white/10 hover:border-white/30'
                            }`}
                          >
                            <span className="text-white text-sm font-medium">{area.name}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Additional Services</h2>
                  <p className="text-gray-300 mb-6">Select any additional services you'd like to include.</p>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-medium text-white mb-3 block">Additional Services (Optional)</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[
                          { id: 'carpet-deep', name: 'Deep Carpet Cleaning', desc: 'Quarterly deep cleaning' },
                          { id: 'window-exterior', name: 'Exterior Window Cleaning', desc: 'External window service' },
                          { id: 'floor-refinishing', name: 'Floor Refinishing', desc: 'Professional floor care' },
                          { id: 'sanitization-deep', name: 'Deep Sanitization', desc: 'Enhanced disinfection' },
                          { id: 'supply-management', name: 'Supply Management', desc: 'Restroom & kitchen supplies' },
                          { id: 'waste-recycling', name: 'Waste & Recycling', desc: 'Complete waste management' }
                        ].map((service) => (
                          <motion.div
                            key={service.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => {
                              const currentServices = formData.additionalServices || [];
                              if (currentServices.includes(service.id)) {
                                setFormData({
                                  ...formData,
                                  additionalServices: currentServices.filter(id => id !== service.id)
                                });
                              } else {
                                setFormData({
                                  ...formData,
                                  additionalServices: [...currentServices, service.id]
                                });
                              }
                            }}
                            className={`p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                              (formData.additionalServices || []).includes(service.id)
                                ? 'bg-gradient-to-br from-emerald-500/20 to-green-600/20 border-2 border-emerald-400' 
                                : 'bg-white/5 border-2 border-white/10 hover:border-white/30'
                            }`}
                          >
                            <h3 className="font-semibold text-white text-sm">{service.name}</h3>
                            <p className="text-gray-300 text-xs">{service.desc}</p>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-white mb-2 block">Special Instructions</label>
                      <textarea 
                        placeholder="Any special requirements, security protocols, or additional information..."
                        className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 h-24 resize-none"
                        value={formData.specialInstructions || ''}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 6 && (
                <motion.div
                  key="step6"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
                  <p className="text-gray-300 mb-6">Let's get your contact details to finalize your corporate estimate.</p>
                  
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          First Name <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <User className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="text" 
                            placeholder="John"
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.firstName 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-emerald-400'
                            }`}
                            value={formData.firstName || ''}
                            onChange={(e) => handleNameChange('firstName', e.target.value)}
                          />
                        </div>
                        {formErrors.firstName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.firstName}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Last Name <span className="text-red-400">*</span>
                        </label>
                        <input 
                          type="text" 
                          placeholder="Doe"
                          className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                            formErrors.lastName 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-white/20 focus:border-emerald-400'
                          }`}
                          value={formData.lastName || ''}
                          onChange={(e) => handleNameChange('lastName', e.target.value)}
                        />
                        {formErrors.lastName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.lastName}
                          </motion.div>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Email Address <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="email" 
                            placeholder="<EMAIL>"
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.email 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-emerald-400'
                            }`}
                            value={formData.email || ''}
                            onChange={(e) => handleEmailChange(e.target.value)}
                          />
                        </div>
                        {formErrors.email && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.email}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Phone Number <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="tel" 
                            placeholder="(*************"
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.phone 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-emerald-400'
                            }`}
                            value={formData.phone || ''}
                            onChange={(e) => handlePhoneChange(e.target.value)}
                          />
                        </div>
                        {formErrors.phone && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.phone}
                          </motion.div>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Company Name <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <Building className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="text" 
                            placeholder="Corporation Inc."
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.companyName 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-emerald-400'
                            }`}
                            value={formData.companyName || ''}
                            onChange={(e) => handleCompanyNameChange(e.target.value)}
                          />
                        </div>
                        {formErrors.companyName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.companyName}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">Job Title</label>
                        <input 
                          type="text" 
                          placeholder="Facilities Manager"
                          className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400"
                          value={formData.jobTitle || ''}
                          onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Validation Summary for Step 6 */}
                  {(formErrors.firstName || formErrors.lastName || formErrors.email || formErrors.phone || formErrors.companyName) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-lg p-4 mt-6"
                    >
                      <div className="flex items-start gap-3">
                        <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <h4 className="text-red-400 font-medium mb-2">Please complete the following:</h4>
                          <ul className="space-y-1 text-sm text-red-300">
                            {formErrors.firstName && <li>• {formErrors.firstName}</li>}
                            {formErrors.lastName && <li>• {formErrors.lastName}</li>}
                            {formErrors.email && <li>• {formErrors.email}</li>}
                            {formErrors.phone && <li>• {formErrors.phone}</li>}
                            {formErrors.companyName && <li>• {formErrors.companyName}</li>}
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Schedule Free Estimate <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* Success Popup */}
        <AnimatePresence>
          {showSuccessPopup && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={handleClosePopup}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: 20 }}
                transition={{ duration: 0.3 }}
                className="rounded-3xl shadow-2xl p-8 max-w-md w-full mx-4"
                onClick={(e) => e.stopPropagation()}
                style={{
                  background: 'rgba(255, 255, 255, 0.12)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.15)',
                  backdropFilter: 'blur(20px)',
                }}
              >
                {/* Close Button */}
                <button
                  onClick={handleClosePopup}
                  className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>

                {/* Success Content */}
                <div className="text-center">
                  {/* Success Icon */}
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-20 h-20 bg-emerald-400 rounded-full flex items-center justify-center mx-auto mb-6"
                  >
                    <CheckCircle className="w-10 h-10 text-white" />
                  </motion.div>

                  {/* Title */}
                  <motion.h2
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-2xl font-bold text-white mb-3"
                  >
                    Corporate Estimate Scheduled!
                  </motion.h2>

                  {/* Description */}
                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-gray-300 mb-6 leading-relaxed"
                  >
                    Thank you for choosing our corporate cleaning services! {user 
                      ? 'Your estimate request has been added to your bookings dashboard where you can track its progress.'
                      : 'Sign in to access your bookings dashboard and track your estimate progress.'
                    }
                  </motion.p>

                  {/* Details */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="bg-white/5 rounded-xl p-4 mb-6 text-left"
                  >
                    <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
                      <Clock className="w-4 h-4 text-emerald-400" />
                      What happens next?
                    </h3>
                    <ul className="space-y-2 text-sm text-gray-300">
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full mt-2 flex-shrink-0"></div>
                        {user ? 'Track your estimate status in your dashboard' : 'Sign in to track your estimate status'}
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full mt-2 flex-shrink-0"></div>
                        Our corporate team will contact you within 24 hours
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full mt-2 flex-shrink-0"></div>
                        Receive your customized corporate cleaning proposal
                      </li>
                    </ul>
                  </motion.div>

                  {/* Action Button */}
                  <motion.button
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    onClick={handleClosePopup}
                    className="w-full px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg"
                  >
                    {user ? 'View Your Bookings' : 'Sign In to View Bookings'}
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </AnimatedBackground>
  );
};

export default ModernCorporateForm;

