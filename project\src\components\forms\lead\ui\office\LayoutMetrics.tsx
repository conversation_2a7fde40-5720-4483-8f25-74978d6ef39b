import React from 'react';
import { motion } from 'framer-motion';

interface LayoutMetricsProps {
  squareFootage: number;
  employeeCount: number;
}

export function LayoutMetrics({ squareFootage, employeeCount }: LayoutMetricsProps) {
  const metrics = [
    {
      label: 'Space Efficiency',
      value: Math.min((squareFootage / employeeCount) / 150 * 100, 100),
      color: 'green'
    },
    {
      label: 'Capacity Utilization',
      value: (employeeCount / (squareFootage / 100)) * 100,
      color: 'blue'
    },
    {
      label: 'Comfort Score',
      value: Math.min(100 - (employeeCount / (squareFootage / 200)) * 100, 100),
      color: 'purple'
    }
  ];

  return (
    <div className="mt-6 grid grid-cols-3 gap-4">
      {metrics.map((metric) => (
        <motion.div
          key={metric.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 bg-gray-50 rounded-lg hover:shadow-sm transition-all duration-300"
        >
          <div className="text-sm font-medium text-gray-700 mb-1">{metric.label}</div>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              className={`h-full bg-${metric.color}-500`}
              initial={{ width: 0 }}
              animate={{ width: `${metric.value}%` }}
              transition={{ delay: 0.5, duration: 1 }}
            />
          </div>
          <div className="mt-1 text-xs text-gray-500 text-right">
            {Math.round(metric.value)}%
          </div>
        </motion.div>
      ))}
    </div>
  );
}
