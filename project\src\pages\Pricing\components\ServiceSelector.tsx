import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Building2, Sparkles, GlassWater, Brush, Construction, Sprout } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import type { ServiceSelection } from '../types';

const services = [
  {
    id: 'office',
    icon: Building2,
    name: 'Office Cleaning',
    description: 'Regular maintenance for office spaces',
    basePrice: 0.15
  },
  {
    id: 'deep',
    icon: Sparkles,
    name: 'Deep Cleaning',
    description: 'Thorough cleaning and sanitization',
    basePrice: 0.25
  },
  {
    id: 'windows',
    icon: GlassWater,
    name: 'Window Cleaning',
    description: 'Interior & exterior windows',
    basePrice: 2.50
  },
  {
    id: 'carpet',
    icon: Brush,
    name: 'Carpet Cleaning',
    description: 'Deep carpet shampooing',
    basePrice: 0.20
  },
  {
    id: 'construction',
    icon: Construction,
    name: 'Post-Construction',
    description: 'Post-construction cleanup',
    basePrice: 0.30
  },
  {
    id: 'sanitization',
    icon: Sprout,
    name: 'Sanitization',
    description: 'Medical-grade disinfection',
    basePrice: 0.25
  }
];

export function ServiceSelector() {
  const [selectedServices, setSelectedServices] = useState<ServiceSelection[]>([]);
  const [promoCode, setPromoCode] = useState('');

  const toggleService = (serviceId: string) => {
    if (selectedServices.some(s => s.id === serviceId)) {
      setSelectedServices(selectedServices.filter(s => s.id !== serviceId));
    } else {
      const service = services.find(s => s.id === serviceId)!;
      setSelectedServices([...selectedServices, { 
        id: service.id,
        name: service.name,
        basePrice: service.basePrice,
        squareFootage: 1000 // Default value
      }]);
    }
  };

  const updateServiceSquareFootage = (serviceId: string, squareFootage: number) => {
    setSelectedServices(selectedServices.map(service => 
      service.id === serviceId 
        ? { ...service, squareFootage }
        : service
    ));
  };

  const calculateTotal = () => {
    const subtotal = selectedServices.reduce((total, service) => 
      total + (service.basePrice * service.squareFootage), 0);
    
    // Apply promo code discount if valid
    const discount = promoCode === 'SAVE20' ? 0.2 : 0;
    
    return {
      subtotal,
      discount: subtotal * discount,
      total: subtotal * (1 - discount)
    };
  };

  const { subtotal, discount, total } = calculateTotal();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Service Selection */}
      <div className="lg:col-span-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {services.map((service) => {
            const isSelected = selectedServices.some(s => s.id === service.id);
            const Icon = service.icon;
            
            return (
              <motion.button
                key={service.id}
                onClick={() => toggleService(service.id)}
                className={`p-6 rounded-xl text-left transition-all ${
                  isSelected 
                    ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                    : 'bg-white border border-gray-200 hover:border-brand-300'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-start space-x-4">
                  <div className="rounded-lg bg-brand-100 p-3">
                    <Icon className="w-6 h-6 text-brand-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{service.name}</h3>
                    <p className="text-sm text-gray-600">{service.description}</p>
                    <p className="mt-2 text-brand-600 font-medium">
                      ${service.basePrice.toFixed(2)}/sq ft
                    </p>
                  </div>
                </div>
              </motion.button>
            );
          })}
        </div>
      </div>

      {/* Order Summary */}
      <div className="lg:col-span-1">
        <div className="bg-white rounded-xl shadow-lg p-6 sticky top-24">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            Order Summary
          </h3>

          {selectedServices.length > 0 ? (
            <div className="space-y-6">
              {selectedServices.map((service) => (
                <div key={service.id} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{service.name}</span>
                    <span>${(service.basePrice * service.squareFootage).toFixed(2)}</span>
                  </div>
                  <input
                    type="number"
                    value={service.squareFootage}
                    onChange={(e) => updateServiceSquareFootage(service.id, Number(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                    placeholder="Square footage"
                  />
                </div>
              ))}

              <div className="pt-4 border-t border-gray-200">
                <div className="flex justify-between text-sm mb-2">
                  <span>Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                {discount > 0 && (
                  <div className="flex justify-between text-sm text-green-600 mb-2">
                    <span>Discount</span>
                    <span>-${discount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>${total.toFixed(2)}</span>
                </div>
              </div>

              <div className="space-y-4">
                <input
                  type="text"
                  value={promoCode}
                  onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg"
                  placeholder="Promo code"
                />
                <Button className="w-full">
                  Get Detailed Quote
                </Button>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 text-center">
              Select services to see pricing
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
