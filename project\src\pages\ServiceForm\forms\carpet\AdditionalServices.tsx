import React from 'react';
import { FormField } from '../../components/FormFields';
import { Shield, Droplets, Zap } from 'lucide-react';

const additionalServices = [
  {
    id: 'deodorizing',
    icon: Droplets,
    label: 'Deodorizing Treatment',
    price: '+$0.10/sq ft'
  },
  {
    id: 'scotchgard',
    icon: Shield,
    label: 'Scotchgard Protection',
    price: '+$0.15/sq ft'
  },
  {
    id: 'express',
    icon: Zap,
    label: 'Express Service',
    price: '+25%'
  }
];

export function AdditionalServices() {
  return (
    <FormField label="Additional Services">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {additionalServices.map(({ id, icon: Icon, label, price }) => (
          <label
            key={id}
            className="relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-brand-50 transition-colors"
          >
            <input
              type="checkbox"
              name="additionalServices"
              value={id}
              className="absolute opacity-0"
            />
            <div className="flex items-center space-x-3">
              <Icon className="w-5 h-5 text-brand-600" />
              <div>
                <p className="font-medium text-gray-900">{label}</p>
                <p className="text-sm text-gray-500">{price}</p>
              </div>
            </div>
          </label>
        ))}
      </div>
    </FormField>
  );
}
