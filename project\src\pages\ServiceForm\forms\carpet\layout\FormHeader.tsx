import React from 'react';
import { ArrowLeft, Building2 } from 'lucide-react';

interface FormHeaderProps {
  onBack: () => void;
}

export function FormHeader({ onBack }: FormHeaderProps) {
  return (
    <div className="bg-white rounded-2xl shadow-sm p-6">
      <div className="flex items-center space-x-4">
        <button
          onClick={onBack}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          aria-label="Go back"
        >
          <ArrowLeft className="w-5 h-5 text-gray-600" />
        </button>
        
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-brand-100 rounded-xl">
            <Building2 className="w-8 h-8 text-brand-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Professional Carpet Cleaning</h1>
            <p className="text-gray-600">
              Expert cleaning solutions for your commercial space
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
