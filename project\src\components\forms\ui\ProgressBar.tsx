import React from 'react';

interface ProgressBarProps {
  steps: string[];
  currentStep: number;
  color: string;
}

export function ProgressBar({ steps, currentStep, color }: ProgressBarProps) {
  const progress = (currentStep / (steps.length - 1)) * 100;

  return (
    <div className="relative">
      <div className="h-2 bg-gray-200 rounded-full">
        <div 
          className="h-full rounded-full transition-all duration-300"
          style={{ 
            width: `${progress}%`,
            backgroundColor: color
          }}
        />
      </div>
      <div className="mt-4 flex justify-between">
        {steps.map((step, index) => (
          <div 
            key={step}
            className={`text-sm font-medium ${
              index <= currentStep ? 'text-gray-900' : 'text-gray-400'
            }`}
          >
            {step}
          </div>
        ))}
      </div>
    </div>
  );
}
