import React from 'react';
import { Form, FormField } from './FormFields';

interface WindowCleaningFormProps {
  onSubmit: (data: any) => void;
}

export function WindowCleaningForm({ onSubmit }: WindowCleaningFormProps) {
  return (
    <Form onSubmit={onSubmit}>
      <FormField label="Number of Windows" required>
        <input
          type="number"
          name="windowCount"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Service Type" required>
        <select
          name="serviceType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="interior">Interior Only</option>
          <option value="exterior">Exterior Only</option>
          <option value="both">Interior & Exterior</option>
        </select>
      </FormField>

      <FormField label="Building Height" required>
        <select
          name="buildingHeight"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="1-2">1-2 Stories</option>
          <option value="3-5">3-5 Stories</option>
          <option value="6+">6+ Stories</option>
        </select>
      </FormField>

      <FormField label="Frequency">
        <select
          name="frequency"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="one-time">One-time Service</option>
          <option value="monthly">Monthly</option>
          <option value="quarterly">Quarterly</option>
          <option value="annually">Annually</option>
        </select>
      </FormField>

      <FormField label="Additional Notes">
        <textarea
          name="notes"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any special requirements or concerns?"
        />
      </FormField>
    </Form>
  );
}
