import React, { useState } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from '../ui/Button';
import { motion } from 'framer-motion';

interface SearchBarProps {
  onSearch: (serviceId: string, zipCode: string) => void;
  onViewAll: () => void;
}

export function SearchBar({ onViewAll }: SearchBarProps) {
  const [zipCode, setZipCode] = useState('');

  return (
    <div className="relative max-w-3xl mx-auto px-4 sm:px-6">
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="bg-white rounded-2xl shadow-xl overflow-hidden"
      >
        <div className="p-2 sm:p-3 flex flex-col sm:flex-row gap-2 sm:gap-3">
          {/* View All Services Button */}
          <div className="relative flex-grow">
            <motion.button
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              onClick={onViewAll}
              className="w-full h-14 pl-12 pr-4 text-left text-gray-900 bg-gray-50
                       ring-1 ring-gray-200 rounded-xl
                       hover:ring-brand-300 hover:bg-gray-100
                       transition-all"
            >
              <Search className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
              View All Services
            </motion.button>
          </div>
          
          {/* ZIP Code Input */}
          <div className="relative sm:w-48">
            <motion.div
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              className="relative"
            >
              <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={5}
                value={zipCode}
                onChange={(e) => setZipCode(e.target.value.replace(/\D/g, '').slice(0, 5))}
                placeholder="ZIP Code"
                className="w-full h-14 pl-12 pr-4 text-gray-900 rounded-xl
                         bg-gray-50 ring-1 ring-gray-200 
                         focus:ring-2 focus:ring-brand-500 
                         hover:ring-brand-300
                         transition-all appearance-none"
              />
            </motion.div>
          </div>

          {/* Submit Button - Mobile Only */}
          <div className="block sm:hidden">
            <motion.div
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
            >
              <Button 
                onClick={onViewAll}
                fullWidth 
                className="h-14"
              >
                Get Started
              </Button>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
