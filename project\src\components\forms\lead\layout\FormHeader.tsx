import React from 'react';
import { ArrowLeft, Phone } from 'lucide-react';
import { Logo } from '../../../ui/Logo';

interface FormHeaderProps {
  onBack: () => void;
}

export function FormHeader({ onBack }: FormHeaderProps) {
  return (
    <header className="bg-white/80 backdrop-blur-sm shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <button
              onClick={onBack}
              className="mr-4 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              aria-label="Go back"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <Logo textColor="text-gray-900" />
          </div>
          
          <div className="flex items-center">
            <a
              href="tel:+15551234567"
              className="flex items-center px-4 py-2 rounded-lg bg-green-50 text-green-700 hover:bg-green-100 transition-colors"
            >
              <Phone className="w-4 h-4 mr-2" />
              <span className="font-medium">(*************</span>
            </a>
          </div>
        </div>
      </div>
    </header>
  );
}
