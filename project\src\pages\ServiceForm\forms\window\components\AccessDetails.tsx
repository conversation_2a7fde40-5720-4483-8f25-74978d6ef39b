import React from 'react';
import { KeyRound, Car, ArrowBigUp, Shield, Clock } from 'lucide-react';
import { FormLayout } from '../../../../../components/forms/FormLayout';
import { FormSection } from '../../../../../components/forms/FormSection';
import { FormField } from '../../../../../components/forms/FormField';

interface AccessDetailsProps {
  details: {
    accessInstructions: string;
    parkingAvailable: boolean;
    heightAccess: string;
    safetyRequirements: string[];
    equipmentRestrictions: string[];
    workingHours: string;
    securityClearance: boolean;
  };
  onChange: (details: any) => void;
}

export function AccessDetails({ details, onChange }: AccessDetailsProps) {
  const heightAccessOptions = [
    'Ground Level Only',
    'Ladder Accessible',
    'Lift Required',
    'Scaffolding Required',
    'Rope Access Required',
    'Boom Lift Required'
  ];

  const safetyRequirements = [
    'Safety Harness Required',
    'Hard Hat Required',
    'Safety Glasses Required',
    'Steel-Toed Boots Required',
    'High-Vis Vest Required',
    'Fall Protection Required'
  ];

  const equipmentRestrictions = [
    'No Heavy Equipment',
    'No Gas-Powered Equipment',
    'No Noisy Equipment',
    'Limited Water Usage',
    'No Chemical Products',
    'Equipment Time Restrictions'
  ];

  return (
    <FormLayout
      title="Access Details"
      description="Help us understand access requirements"
      icon={<KeyRound className="w-6 h-6 text-brand-600" />}
    >
      {/* Height Access */}
      <FormSection 
        title="Height Access Requirements" 
        description="How will cleaners access the windows?"
        required
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {heightAccessOptions.map((option) => (
            <label
              key={option}
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                details.heightAccess === option
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="heightAccess"
                value={option}
                checked={details.heightAccess === option}
                onChange={(e) => onChange({ ...details, heightAccess: e.target.value })}
                className="sr-only"
              />
              <ArrowBigUp className={`w-5 h-5 mr-3 ${
                details.heightAccess === option ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-gray-700">{option}</span>
            </label>
          ))}
        </div>
      </FormSection>

      {/* Safety Requirements */}
      <FormSection 
        title="Safety Requirements"
        description="Select all required safety equipment"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {safetyRequirements.map((req) => (
            <label
              key={req}
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                details.safetyRequirements.includes(req)
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="checkbox"
                checked={details.safetyRequirements.includes(req)}
                onChange={(e) => {
                  const newReqs = e.target.checked
                    ? [...details.safetyRequirements, req]
                    : details.safetyRequirements.filter(r => r !== req);
                  onChange({ ...details, safetyRequirements: newReqs });
                }}
                className="sr-only"
              />
              <Shield className={`w-5 h-5 mr-3 ${
                details.safetyRequirements.includes(req) ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-gray-700">{req}</span>
            </label>
          ))}
        </div>
      </FormSection>

      {/* Working Hours & Access */}
      <FormSection title="Working Hours & Access">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField label="Working Hours" required>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={details.workingHours}
                onChange={(e) => onChange({ ...details, workingHours: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="e.g., Mon-Fri 9AM-5PM"
                required
              />
            </div>
          </FormField>

          <FormField label="Access Instructions" required>
            <div className="relative">
              <KeyRound className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
              <textarea
                value={details.accessInstructions}
                onChange={(e) => onChange({ ...details, accessInstructions: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                rows={3}
                placeholder="Please provide any special instructions for accessing the property..."
                required
              />
            </div>
          </FormField>
        </div>

        <div className="space-y-4 mt-6">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={details.parkingAvailable}
              onChange={(e) => onChange({ ...details, parkingAvailable: e.target.checked })}
              className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
            />
            <div className="flex items-center space-x-2">
              <Car className="w-5 h-5 text-gray-400" />
              <span className="text-gray-700">Parking available for service vehicles</span>
            </div>
          </label>

          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={details.securityClearance}
              onChange={(e) => onChange({ ...details, securityClearance: e.target.checked })}
              className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
            />
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-gray-400" />
              <span className="text-gray-700">Security clearance required</span>
            </div>
          </label>
        </div>
      </FormSection>

      {/* Equipment Restrictions */}
      <FormSection 
        title="Equipment Restrictions"
        description="Select any restrictions on equipment usage"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {equipmentRestrictions.map((restriction) => (
            <label
              key={restriction}
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                details.equipmentRestrictions.includes(restriction)
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="checkbox"
                checked={details.equipmentRestrictions.includes(restriction)}
                onChange={(e) => {
                  const newRestrictions = e.target.checked
                    ? [...details.equipmentRestrictions, restriction]
                    : details.equipmentRestrictions.filter(r => r !== restriction);
                  onChange({ ...details, equipmentRestrictions: newRestrictions });
                }}
                className="sr-only"
              />
              <Shield className={`w-5 h-5 mr-3 ${
                details.equipmentRestrictions.includes(restriction) ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-gray-700">{restriction}</span>
            </label>
          ))}
        </div>
      </FormSection>
    </FormLayout>
  );
}
