import React from 'react';
import { ProcessStep } from './ProcessStep';
import { ArrowDown } from 'lucide-react';

const steps = [
  {
    title: 'Book Online',
    description: 'Select your cleaning needs and get an instant quote.'
  },
  {
    title: 'Schedule Service',
    description: 'Choose a date and time that works for you.'
  },
  {
    title: 'Enjoy a Clean Space',
    description: 'Our professionals handle the rest.'
  }
];

export function HowItWorksSection() {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
              alt="Building being cleaned"
              className="rounded-lg shadow-2xl transform perspective-1000 rotate-y-6 hover:rotate-y-0 transition-transform duration-500"
            />
          </div>
          
          <div className="space-y-8">
            <div className="space-y-6">
              {steps.map((step, index) => (
                <React.Fragment key={step.title}>
                  <ProcessStep
                    {...step}
                    stepNumber={index + 1}
                  />
                  {index < steps.length - 1 && (
                    <div className="flex justify-center">
                      <ArrowDown className="w-5 h-5 text-brand-500 animate-bounce" />
                    </div>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
