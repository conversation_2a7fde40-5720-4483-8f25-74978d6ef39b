import React from 'react';
import { Form, FormField } from '../components/FormFields';

export function PressureWashingForm() {
  return (
    <Form onSubmit={(data) => console.log(data)}>
      <FormField label="Surface Type" required>
        <select
          name="surfaceType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="concrete">Concrete</option>
          <option value="brick">Brick</option>
          <option value="stone">Natural Stone</option>
          <option value="wood">Wood</option>
          <option value="vinyl">Vinyl/Siding</option>
        </select>
      </FormField>

      <FormField label="Area Size" required>
        <div className="grid grid-cols-2 gap-4">
          <input
            type="number"
            name="areaSize"
            placeholder="Size"
            className="px-4 py-2 border border-gray-300 rounded-lg"
            required
          />
          <select
            name="unit"
            className="px-4 py-2 border border-gray-300 rounded-lg"
            required
          >
            <option value="sqft">Square Feet</option>
            <option value="linear">Linear Feet</option>
          </select>
        </div>
      </FormField>

      <FormField label="Service Areas">
        <div className="space-y-2">
          {[
            'Sidewalks',
            'Driveways',
            'Building Exterior',
            'Parking Lot',
            'Loading Dock',
            'Dumpster Area'
          ].map((area) => (
            <label key={area} className="flex items-center">
              <input
                type="checkbox"
                name="areas"
                value={area.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{area}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Special Instructions">
        <textarea
          name="instructions"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific areas of concern or access restrictions?"
        />
      </FormField>
    </Form>
  );
}
