import React from 'react';
import { motion } from 'framer-motion';
import { MousePointerClick, Calendar, Sparkles } from 'lucide-react';

const steps = [
  {
    icon: MousePointerClick,
    title: 'Choose a Service',
    description: 'Select from our range of professional cleaning services',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: Calendar,
    title: 'Schedule Your Cleaning',
    description: 'Pick a date and time that works best for you',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: Spark<PERSON>,
    title: 'Relax While We Work',
    description: 'Our professional team handles everything',
    color: 'from-purple-500 to-purple-600'
  }
];

export function HowItWorks() {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-gray-900"
          >
            How We Deliver Exceptional Cleaning Services
          </motion.h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <motion.div
              key={step.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2 }}
              className="relative group"
            >
              <div className="relative bg-white rounded-2xl shadow-xl overflow-hidden">
                {/* Gradient Background */}
                <div className={`absolute inset-0 bg-gradient-to-br ${step.color} opacity-10 
                              group-hover:opacity-20 transition-opacity duration-300`} />
                
                <div className="relative p-8">
                  {/* Step Number */}
                  <div className="absolute top-4 right-4 w-8 h-8 rounded-full bg-gray-100 
                                flex items-center justify-center text-sm font-medium text-gray-600">
                    {index + 1}
                  </div>

                  {/* Icon */}
                  <div className={`inline-flex items-center justify-center p-3 rounded-xl 
                                bg-gradient-to-br ${step.color} mb-6 group-hover:scale-110 
                                transition-transform duration-300`}>
                    <step.icon className="w-6 h-6 text-white" />
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600">
                    {step.description}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
