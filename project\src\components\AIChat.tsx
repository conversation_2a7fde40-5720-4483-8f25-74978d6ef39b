import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, X, Bot, User, Send, Sparkles, Calendar, 
  MapPin, Home, Building2, ArrowRight, CheckCircle, Clock,
  DollarSign, Phone, Mail, Camera, FileText
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface Message {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: Date;
  suggestions?: string[];
  serviceType?: string;
  quote?: {
    service: string;
    price: number;
    duration: string;
    includes: string[];
  };
}

const AIChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentStep, setCurrentStep] = useState<'greeting' | 'service' | 'details' | 'quote' | 'booking'>('greeting');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (sender: 'user' | 'ai', text: string, extras?: Partial<Message>) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      sender,
      text,
      timestamp: new Date(),
      ...extras
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleOpen = () => {
    setIsOpen(true);
    if (messages.length === 0) {
      setTimeout(() => {
        addMessage('ai', "👋 Hi there! I'm your AI cleaning assistant. I can help you get an instant quote and book cleaning services. What type of cleaning do you need today?", {
          suggestions: ["House Cleaning", "Deep Cleaning", "Office Cleaning", "Post-Construction", "Move-in/Move-out"]
        });
      }, 500);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  const simulateTyping = (callback: () => void, delay = 1000) => {
    setIsTyping(true);
    setTimeout(() => {
      setIsTyping(false);
      callback();
    }, delay);
  };

  const handleServiceSelection = (service: string) => {
    addMessage('user', service);
    setCurrentStep('details');
    
    simulateTyping(() => {
      addMessage('ai', `Great choice! I'll help you with ${service.toLowerCase()}. To give you an accurate quote, I need a few details:`, {
        suggestions: ["1-2 bedrooms", "3-4 bedrooms", "5+ bedrooms", "Small office", "Large office", "Custom size"]
      });
    });
  };

  const handleSizeSelection = (size: string) => {
    addMessage('user', size);
    setCurrentStep('quote');
    
    simulateTyping(() => {
      // Generate a mock quote based on selection
      const mockQuote = generateMockQuote(size);
      addMessage('ai', `Perfect! Based on your requirements, here's your instant quote:`, {
        quote: mockQuote,
        suggestions: ["Book Now", "Modify Details", "Ask Questions", "Get More Info"]
      });
    }, 1500);
  };

  const generateMockQuote = (size: string) => {
    const quotes = {
      "1-2 bedrooms": { service: "Regular House Cleaning (1-2 bed)", price: 89, duration: "1.5-2 hours", includes: ["All rooms cleaned", "Kitchen & bathroom", "Dusting & vacuuming"] },
      "3-4 bedrooms": { service: "Regular House Cleaning (3-4 bed)", price: 149, duration: "2.5-3 hours", includes: ["All rooms cleaned", "Kitchen & bathrooms", "Dusting & vacuuming", "Window sills"] },
      "5+ bedrooms": { service: "Large House Cleaning (5+ bed)", price: 229, duration: "3.5-4 hours", includes: ["All rooms cleaned", "Multiple bathrooms", "Kitchen deep clean", "Full house dusting"] },
      "Small office": { service: "Small Office Cleaning", price: 129, duration: "2-3 hours", includes: ["Desk areas", "Common spaces", "Restrooms", "Trash removal"] },
      "Large office": { service: "Large Office Cleaning", price: 299, duration: "4-5 hours", includes: ["All workstations", "Conference rooms", "Break rooms", "Restrooms", "Reception area"] }
    };
    return quotes[size as keyof typeof quotes] || quotes["3-4 bedrooms"];
  };

  const handleBookingAction = (action: string) => {
    addMessage('user', action);
    
    if (action === "Book Now") {
      setCurrentStep('booking');
      simulateTyping(() => {
        addMessage('ai', "Excellent! Let's get your cleaning scheduled. I'll redirect you to our booking form where you can select your preferred date and time, and provide your contact details.", {
          suggestions: ["Continue to Booking", "Need More Info", "Change Service"]
        });
      });
    } else if (action === "Continue to Booking") {
      navigate('/service-form');
    }
  };

  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;

    const message = inputValue.trim();
    addMessage('user', message);
    setInputValue('');

    // Simple AI response logic
    simulateTyping(() => {
      if (currentStep === 'greeting') {
        addMessage('ai', "I can help you with that! Let me know more details about your cleaning needs, or choose from the options above.", {
          suggestions: ["House Cleaning", "Deep Cleaning", "Office Cleaning", "Get Quote"]
        });
      } else {
        addMessage('ai', "Got it! Let me process that information. You can also use the quick options above for faster service.", {
          suggestions: ["Get Quote", "Schedule Now", "More Info"]
        });
      }
    });
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (currentStep === 'greeting' && ["House Cleaning", "Deep Cleaning", "Office Cleaning", "Post-Construction", "Move-in/Move-out"].includes(suggestion)) {
      handleServiceSelection(suggestion);
    } else if (currentStep === 'details' && suggestion.includes("bedroom") || suggestion.includes("office") || suggestion.includes("Custom")) {
      handleSizeSelection(suggestion);
    } else if (currentStep === 'quote' && ["Book Now", "Modify Details", "Ask Questions", "Get More Info"].includes(suggestion)) {
      handleBookingAction(suggestion);
    } else if (currentStep === 'booking') {
      handleBookingAction(suggestion);
    } else {
      addMessage('user', suggestion);
      simulateTyping(() => {
        addMessage('ai', "Thank you for that information! How else can I help you today?");
      });
    }
  };

  return (
    <>
      <style>{`
        .ai-chat-glass {
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.15);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .ai-chat-modal {
          background: rgba(15, 23, 42, 0.95);
          backdrop-filter: blur(24px);
          -webkit-backdrop-filter: blur(24px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
        }
        .message-bubble-ai {
          background: rgba(30, 41, 59, 0.8);
          backdrop-filter: blur(8px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .message-bubble-user {
          background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 0.95));
          backdrop-filter: blur(8px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .suggestion-chip {
          background: rgba(16, 185, 129, 0.1);
          backdrop-filter: blur(8px);
          border: 1px solid rgba(16, 185, 129, 0.2);
          transition: all 0.2s ease;
        }
        .suggestion-chip:hover {
          background: rgba(16, 185, 129, 0.2);
          border-color: rgba(16, 185, 129, 0.4);
          transform: translateY(-1px);
        }
        .typing-indicator {
          animation: typing-pulse 1.5s infinite;
        }
        @keyframes typing-pulse {
          0%, 100% { opacity: 0.4; }
          50% { opacity: 1; }
        }
        .quote-card {
          background: rgba(16, 185, 129, 0.1);
          backdrop-filter: blur(8px);
          border: 1px solid rgba(16, 185, 129, 0.2);
        }
      `}</style>

      {/* Chat Widget Button */}
      <motion.div onClick={handleOpen} className="cursor-pointer">
        <div className="relative flex justify-center items-center">
          <motion.div
            className="ai-chat-glass rounded-full p-2 flex items-center justify-between w-full max-w-xl"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-12 h-12 rounded-full ml-2 bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <button className="text-white rounded-full px-12 py-3 flex items-center gap-3 hover:bg-white/5 transition-all duration-300">
              <MessageCircle size={18} />
              <span className="font-semibold text-sm tracking-wider">AI CHAT BOOKING</span>
            </button>
            <button className="ai-chat-glass rounded-full p-3 flex items-center justify-center mr-2 w-12 h-12 transition-all duration-300 hover:bg-white/10">
              <Sparkles size={22} className="text-white/80" />
            </button>
          </motion.div>
        </div>
      </motion.div>

      {/* Chat Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm p-4"
            onClick={handleClose}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              onClick={(e) => e.stopPropagation()}
              className="ai-chat-modal w-full max-w-lg h-[80vh] rounded-2xl flex flex-col relative overflow-hidden"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-white/10">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                    <Bot className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-white">AI Booking Assistant</h2>
                    <p className="text-xs text-white/60">Get instant quotes & book cleaning</p>
                  </div>
                </div>
                <button
                  onClick={handleClose}
                  className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-lg"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Messages Container */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.map((message) => (
                  <div key={message.id}>
                    {/* Message */}
                    <motion.div
                      className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : ''}`}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      {message.sender === 'ai' && (
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center flex-shrink-0">
                          <Bot size={14} className="text-white" />
                        </div>
                      )}
                      
                      <div className={`max-w-xs lg:max-w-md ${message.sender === 'user' ? 'order-1' : ''}`}>
                        <div className={`p-3 rounded-2xl text-sm ${
                          message.sender === 'ai' ? 'message-bubble-ai text-white/90' : 'message-bubble-user text-white'
                        }`}>
                          {message.text}
                        </div>
                        
                        {/* Quote Card */}
                        {message.quote && (
                          <motion.div
                            className="quote-card mt-3 p-4 rounded-xl"
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.2 }}
                          >
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-semibold text-white text-lg">Your Quote</h4>
                              <span className="text-2xl font-bold text-emerald-400">
                                ${message.quote.price}
                              </span>
                            </div>
                            <div className="space-y-2 text-sm text-white/80">
                              <div className="flex items-center gap-2">
                                <CheckCircle size={14} className="text-emerald-400" />
                                <span>{message.quote.service}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Clock size={14} className="text-emerald-400" />
                                <span>Duration: {message.quote.duration}</span>
                              </div>
                              {message.quote.includes.map((item, idx) => (
                                <div key={idx} className="flex items-center gap-2">
                                  <CheckCircle size={14} className="text-emerald-400" />
                                  <span>{item}</span>
                                </div>
                              ))}
                            </div>
                          </motion.div>
                        )}
                        
                        {/* Suggestions */}
                        {message.suggestions && (
                          <div className="flex flex-wrap gap-2 mt-3">
                            {message.suggestions.map((suggestion, idx) => (
                              <button
                                key={idx}
                                onClick={() => handleSuggestionClick(suggestion)}
                                className="suggestion-chip px-3 py-1.5 rounded-full text-xs font-medium text-emerald-300 hover:text-white"
                              >
                                {suggestion}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      {message.sender === 'user' && (
                        <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center flex-shrink-0">
                          <User size={14} className="text-white" />
                        </div>
                      )}
                    </motion.div>
                  </div>
                ))}
                
                {/* Typing Indicator */}
                {isTyping && (
                  <motion.div
                    className="flex gap-3"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-emerald-400 to-emerald-600 flex items-center justify-center">
                      <Bot size={14} className="text-white" />
                    </div>
                    <div className="message-bubble-ai p-3 rounded-2xl">
                      <div className="flex gap-1">
                        <div className="w-2 h-2 bg-white/60 rounded-full typing-indicator"></div>
                        <div className="w-2 h-2 bg-white/60 rounded-full typing-indicator" style={{ animationDelay: '0.2s' }}></div>
                        <div className="w-2 h-2 bg-white/60 rounded-full typing-indicator" style={{ animationDelay: '0.4s' }}></div>
                      </div>
                    </div>
                  </motion.div>
                )}
                
                <div ref={messagesEndRef} />
              </div>

              {/* Input Area */}
              <div className="p-4 border-t border-white/10">
                <div className="flex items-center gap-2">
                  <button className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-colors">
                    <Camera size={18} />
                  </button>
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      placeholder="Type your message or use quick options above..."
                      className="w-full bg-white/5 border border-white/10 rounded-xl py-3 px-4 text-white placeholder-white/40 focus:outline-none focus:border-emerald-400/50 focus:bg-white/10 transition-all"
                    />
                  </div>
                  <button
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim()}
                    className="p-2 bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg transition-colors"
                  >
                    <Send size={18} className="text-white" />
                  </button>
                </div>
                <p className="text-xs text-white/40 mt-2 text-center">
                  Get instant quotes • Book in seconds • Available 24/7
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default AIChatWidget; 
