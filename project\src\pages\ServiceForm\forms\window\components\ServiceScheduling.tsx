import React from 'react';
import { Calendar, Clock, AlertTriangle } from 'lucide-react';
import { FormLayout } from '../../../../../components/forms/FormLayout';
import { FormSection } from '../../../../../components/forms/FormSection';
import { FormField } from '../../../../../components/forms/FormField';

interface ServiceSchedulingProps {
  schedule: {
    date: string;
    timeSlot: string;
    frequency: string;
    preferredDays: string[];
    duration: string;
    urgency: string;
  };
  onChange: (schedule: any) => void;
}

export function ServiceScheduling({ schedule, onChange }: ServiceSchedulingProps) {
  const today = new Date().toISOString().split('T')[0];
  
  const timeSlots = [
    { value: 'early-morning', label: 'Early Morning (5AM - 8AM)', description: 'Before business hours' },
    { value: 'morning', label: 'Morning (8AM - 12PM)', description: 'During morning hours' },
    { value: 'afternoon', label: 'Afternoon (12PM - 4PM)', description: 'During afternoon hours' },
    { value: 'evening', label: 'Evening (4PM - 8PM)', description: 'After business hours' },
    { value: 'night', label: 'Night (8PM - 12AM)', description: 'Night service' }
  ];

  const frequencies = [
    { value: 'one-time', label: 'One-Time Service', description: 'Single service' },
    { value: 'weekly', label: 'Weekly', description: 'Every week' },
    { value: 'biweekly', label: 'Bi-Weekly', description: 'Every two weeks' },
    { value: 'monthly', label: 'Monthly', description: 'Once per month' },
    { value: 'quarterly', label: 'Quarterly', description: 'Every three months' }
  ];

  const urgencyLevels = [
    { value: 'normal', label: 'Normal', description: 'Standard scheduling' },
    { value: 'priority', label: 'Priority', description: '24-48 hour service' },
    { value: 'urgent', label: 'Urgent', description: 'Same/Next day service' }
  ];

  return (
    <FormLayout
      title="Schedule Service"
      description="Choose your preferred service time"
      icon={<Calendar className="w-6 h-6 text-brand-600" />}
    >
      {/* Service Date & Time */}
      <FormSection title="Service Date & Time" required>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField label="Preferred Date" required>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="date"
                value={schedule.date}
                onChange={(e) => onChange({ ...schedule, date: e.target.value })}
                min={today}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              />
            </div>
          </FormField>

          <FormField label="Preferred Time" required>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={schedule.timeSlot}
                onChange={(e) => onChange({ ...schedule, timeSlot: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              >
                <option value="">Select time slot</option>
                {timeSlots.map((slot) => (
                  <option key={slot.value} value={slot.value}>
                    {slot.label}
                  </option>
                ))}
              </select>
            </div>
          </FormField>
        </div>
      </FormSection>

      {/* Service Frequency */}
      <FormSection title="Service Frequency">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {frequencies.map((freq) => (
            <label
              key={freq.value}
              className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                schedule.frequency === freq.value
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="frequency"
                value={freq.value}
                checked={schedule.frequency === freq.value}
                onChange={(e) => onChange({ ...schedule, frequency: e.target.value })}
                className="sr-only"
              />
              <div className="font-medium text-gray-900">{freq.label}</div>
              <p className="text-sm text-gray-600 mt-1">{freq.description}</p>
            </label>
          ))}
        </div>
      </FormSection>

      {/* Service Urgency */}
      <FormSection title="Service Urgency">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {urgencyLevels.map((level) => (
            <label
              key={level.value}
              className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                schedule.urgency === level.value
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="urgency"
                value={level.value}
                checked={schedule.urgency === level.value}
                onChange={(e) => onChange({ ...schedule, urgency: e.target.value })}
                className="sr-only"
              />
              <div className="font-medium text-gray-900">{level.label}</div>
              <p className="text-sm text-gray-600 mt-1">{level.description}</p>
            </label>
          ))}
        </div>

        {schedule.urgency === 'urgent' && (
          <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Urgent Service Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Additional fees may apply for urgent service requests. Our team will contact you to confirm availability.
                </p>
              </div>
            </div>
          </div>
        )}
      </FormSection>
    </FormLayout>
  );
}
