import React from 'react';
import { Form, FormField } from '../components/FormFields';

export function OfficeCleaningForm() {
  return (
    <Form onSubmit={(data) => console.log(data)}>
      <FormField label="Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Number of Offices/Rooms" required>
        <input
          type="number"
          name="roomCount"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Service Frequency" required>
        <select
          name="frequency"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="biweekly">Bi-Weekly</option>
          <option value="monthly">Monthly</option>
        </select>
      </FormField>

      <FormField label="Additional Services">
        <div className="space-y-2">
          {[
            'Kitchen Cleaning',
            'Restroom Restocking',
            'Window Cleaning',
            'Carpet Vacuuming',
            'Floor Mopping',
            'Trash Removal'
          ].map((service) => (
            <label key={service} className="flex items-center">
              <input
                type="checkbox"
                name="additionalServices"
                value={service.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{service}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Special Instructions">
        <textarea
          name="instructions"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific requirements or areas that need special attention?"
        />
      </FormField>
    </Form>
  );
}
