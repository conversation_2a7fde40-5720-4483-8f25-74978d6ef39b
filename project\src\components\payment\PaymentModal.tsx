import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, ExternalLink, AlertCircle } from 'lucide-react';
import { PaymentForm } from './PaymentForm';
import { Button } from '../ui/Button';
import { User } from '@supabase/supabase-js';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  description: string;
  customerEmail?: string;
  formData: any;
  user: User | null;
  onPaymentComplete?: () => void;
}

export function PaymentModal({ 
  isOpen, 
  onClose, 
  amount, 
  description, 
  customerEmail,
  formData,
  user,
  onPaymentComplete
}: PaymentModalProps) {
  const [paymentLink, setPaymentLink] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (link: string) => {
    if (!link) {
      console.error('Payment link is missing');
      return;
    }
    
    setPaymentLink(link);
    setIsSuccess(true);
    if (onPaymentComplete) {
      onPaymentComplete();
    }
  };

  const handleRedirect = () => {
    if (paymentLink) {
      window.open(paymentLink, '_blank');
    }
  };

  const handlePaymentError = (error: Error) => {
    console.error('Payment error:', error.message);
    setError(error.message);
  };

  const handleClose = () => {
    if (!isSuccess && !error) {
      if (!window.confirm('Are you sure you want to cancel? Your progress will be saved.')) {
        return;
      }
    }
    setPaymentLink(null);
    setIsSuccess(false);
    setError(null);
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-2 sm:p-4">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm"
              onClick={handleClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="relative w-full max-w-xs sm:max-w-md lg:max-w-lg mx-2 sm:mx-4 bg-white rounded-xl sm:rounded-2xl shadow-2xl overflow-hidden"
            >
              {/* Close Button */}
              <button
                onClick={handleClose}
                className="absolute top-3 right-3 sm:top-4 sm:right-4 p-2 rounded-lg hover:bg-gray-100 transition-colors z-10 touch-manipulation"
              >
                <X className="w-5 h-5 sm:w-6 sm:h-6 text-gray-500" />
              </button>

              {/* Content */}
              <div className="p-4 sm:p-6 lg:p-8">
                {isSuccess ? (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-4 sm:py-6 lg:py-8"
                  >
                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                      <CheckCircle className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
                    </div>
                    <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-3 sm:mb-4">
                      Thank You! Payment Link Created
                    </h3>
                    <p className="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8 leading-relaxed">
                      Thank you for choosing our service. Your payment link is ready - click below to complete payment and confirm your booking.
                    </p>
                    <Button 
                      onClick={handleRedirect} 
                      className="flex items-center justify-center w-full sm:w-auto py-3 sm:py-2 text-sm sm:text-base touch-manipulation"
                    >
                      Complete Payment
                      <ExternalLink className="ml-2 w-4 h-4 sm:w-4 sm:h-4" />
                    </Button>
                  </motion.div>
                ) : (
                  <>
                    <h2 className="text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900 mb-4 sm:mb-6">
                      Secure Payment
                    </h2>
                    {error && (
                      <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 rounded-lg flex items-start gap-2">
                        <AlertCircle className="w-4 h-4 text-red-600 mt-0.5" />
                        <div>
                          <p className="text-red-600 text-xs sm:text-sm font-medium">Payment Error</p>
                          <p className="text-red-600 text-xs sm:text-sm leading-relaxed">{error}. Please check your details and try again, or contact support if the issue persists.</p>
                        </div>
                      </div>
                    )}
                    <PaymentForm
                      amount={amount}
                      description={description}
                      customerEmail={customerEmail}
                      formData={formData}
                      onSuccess={handlePaymentSuccess}
                      onError={handlePaymentError}
                    />
                  </>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
}