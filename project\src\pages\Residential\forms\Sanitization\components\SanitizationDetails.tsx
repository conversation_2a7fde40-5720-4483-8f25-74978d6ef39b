import React from 'react';
import { Droplets, Shield, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface SanitizationDetailsData {
  spaceType: string;
  highTouchAreas: string[];
  healthConcerns: string[];
}

interface SanitizationDetailsProps {
  details: SanitizationDetailsData;
  onChange: (details: SanitizationDetailsData) => void;
}

export function SanitizationDetails({ details, onChange }: SanitizationDetailsProps) {
  const spaceTypes = [
    'Home',
    'Office',
    'Gym',
    'Daycare',
    'Medical Office',
    'Retail Space',
    'Restaurant',
    'School',
    'Other'
  ];

  const highTouchAreas = [
    'Door Handles',
    'Light Switches',
    'Countertops',
    'Bathroom Fixtures',
    'Kitchen Appliances',
    'Remote Controls',
    'Keyboards/Phones',
    'Handrails',
    'Elevator Buttons',
    'Shared Equipment'
  ];

  const healthConcerns = [
    'Recent Illness',
    'Allergies',
    'COVID-19 Exposure',
    'Immunocompromised Residents',
    'Mold Concerns',
    'Asthma',
    'General Preventative',
    'Other'
  ];



  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Droplets className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Sanitization Details</h3>
          <p className="text-gray-600">Tell us about your sanitization needs</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            What type of space needs to be sanitized? <span className="text-red-500">*</span>
          </label>
          <select
            value={details.spaceType}
            onChange={(e) => onChange({ ...details, spaceType: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select space type</option>
            {spaceTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Are there specific high-touch areas to focus on? <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {highTouchAreas.map((area) => (
              <label
                key={area}
                className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  details.highTouchAreas.includes(area)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.highTouchAreas.includes(area)}
                  onChange={(e) => {
                    const newAreas = e.target.checked
                      ? [...details.highTouchAreas, area]
                      : details.highTouchAreas.filter(a => a !== area);
                    onChange({ ...details, highTouchAreas: newAreas });
                  }}
                  className="sr-only"
                />
                <Shield className={`w-5 h-5 mr-2 ${
                  details.highTouchAreas.includes(area) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm">{area}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Are there any known health concerns?
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {healthConcerns.map((concern) => (
              <label
                key={concern}
                className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  details.healthConcerns.includes(concern)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.healthConcerns.includes(concern)}
                  onChange={(e) => {
                    const newConcerns = e.target.checked
                      ? [...details.healthConcerns, concern]
                      : details.healthConcerns.filter(c => c !== concern);
                    onChange({ ...details, healthConcerns: newConcerns });
                  }}
                  className="sr-only"
                />
                <AlertCircle className={`w-5 h-5 mr-2 ${
                  details.healthConcerns.includes(concern) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm">{concern}</span>
              </label>
            ))}
          </div>
        </div>



        {details.healthConcerns.includes('COVID-19 Exposure') && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">COVID-19 Protocol</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Our team follows CDC guidelines for COVID-19 disinfection. Please ensure the space has been vacant for at least 24 hours before our service.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
