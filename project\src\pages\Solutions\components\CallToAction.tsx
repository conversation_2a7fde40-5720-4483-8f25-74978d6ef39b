import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { ServiceMenu } from '../../../components/services/ServiceMenu';

export function CallToAction() {
  const [showServiceMenu, setShowServiceMenu] = useState(false);

  const handleGetQuote = () => {
    setShowServiceMenu(true);
  };

  return (
    <>
      <section className="py-24 bg-brand-600 relative overflow-hidden">
        {/* Background remains the same */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-white mb-6"
          >
            Ready to Transform Your Space?
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-brand-100 mb-8 max-w-2xl mx-auto"
          >
            Get in touch for a customized quote and free consultation
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="flex flex-col sm:flex-row justify-center gap-4"
          >
            <Button
              size="lg"
              onClick={handleGetQuote}
              className="bg-white text-brand-600 hover:bg-brand-50"
            >
              Get Your Free Quote
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10"
              onClick={() => window.location.href = '/contact'}
            >
              Contact Us
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Service Menu */}
      <ServiceMenu
        isOpen={showServiceMenu}
        onClose={() => setShowServiceMenu(false)}
        onServiceSelect={(serviceId) => {
          setShowServiceMenu(false);
          // Navigation will be handled by the ServiceMenu component
        }}
      />
    </>
  );
}
