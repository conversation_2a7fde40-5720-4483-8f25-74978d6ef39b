import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Check, AlertCircle } from 'lucide-react';

interface ServiceMapProps {
  selectedZipCode: string | null;
  isValid: boolean;
}

export function ServiceMap({ selectedZipCode, isValid }: ServiceMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const googleMapRef = useRef<google.maps.Map | null>(null);
  const markerRef = useRef<google.maps.Marker | null>(null);
  const [mapError, setMapError] = useState<string | null>(null);

  useEffect(() => {
    // Check if Google Maps API key is configured
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      setMapError('Google Maps API key is not configured');
      return;
    }

    // Initialize map
    if (mapRef.current && !googleMapRef.current && window.google?.maps) {
      try {
        const defaultCenter = { lat: 40.7128, lng: -74.0060 }; // New York City
        googleMapRef.current = new google.maps.Map(mapRef.current, {
          zoom: 10,
          center: defaultCenter,
          styles: [
            {
              featureType: "all",
              elementType: "geometry",
              stylers: [{ color: "#f5f5f5" }]
            },
            {
              featureType: "water",
              elementType: "geometry",
              stylers: [{ color: "#e9e9e9" }]
            },
            {
              featureType: "water",
              elementType: "labels.text.fill",
              stylers: [{ color: "#9e9e9e" }]
            }
          ],
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false
        });
      } catch (error) {
        setMapError('Error initializing map');
        console.error('Map initialization error:', error);
      }
    }
  }, []);

  useEffect(() => {
    if (selectedZipCode && googleMapRef.current && window.google?.maps) {
      // Use Geocoding service to convert ZIP code to coordinates
      const geocoder = new google.maps.Geocoder();
      geocoder.geocode(
        { address: selectedZipCode },
        (results, status) => {
          if (status === 'OK' && results && results[0]) {
            const location = results[0].geometry.location;
            
            // Center map on location
            googleMapRef.current?.panTo(location);
            googleMapRef.current?.setZoom(13);

            // Add or update marker
            if (markerRef.current) {
              markerRef.current.setPosition(location);
            } else {
              markerRef.current = new google.maps.Marker({
                position: location,
                map: googleMapRef.current,
                icon: {
                  path: google.maps.SymbolPath.CIRCLE,
                  scale: 10,
                  fillColor: isValid ? '#38944E' : '#EF4444',
                  fillOpacity: 0.6,
                  strokeWeight: 2,
                  strokeColor: isValid ? '#2C7A3D' : '#DC2626'
                }
              });
            }

            // Add circle for service area if valid
            if (isValid) {
              new google.maps.Circle({
                map: googleMapRef.current,
                center: location,
                radius: 2000, // 2km radius
                fillColor: '#38944E',
                fillOpacity: 0.1,
                strokeColor: '#2C7A3D',
                strokeWeight: 2,
                strokeOpacity: 0.3
              });
            }
          }
        }
      );
    }
  }, [selectedZipCode, isValid]);

  if (mapError) {
    return (
      <div className="aspect-[16/9] w-full bg-gray-50 rounded-3xl flex items-center justify-center">
        <div className="text-center p-8">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">{mapError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative bg-white rounded-3xl shadow-xl overflow-hidden">
      {/* Google Map */}
      <div ref={mapRef} className="aspect-[16/9] w-full" />

      {/* Map Legend */}
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg">
        <div className="text-sm font-medium text-gray-900 mb-2">Service Areas</div>
        <div className="space-y-2">
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-brand-500 mr-2" />
            <span className="text-sm text-gray-600">Active Coverage</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-brand-200 mr-2" />
            <span className="text-sm text-gray-600">Coming Soon</span>
          </div>
        </div>
      </div>

      {/* Selected ZIP Code Info */}
      {selectedZipCode && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-4 right-4 bg-white rounded-xl p-4 shadow-lg"
        >
          <div className="flex items-center space-x-2">
            <MapPin className="w-5 h-5 text-brand-600" />
            <div>
              <div className="font-medium text-gray-900">ZIP Code: {selectedZipCode}</div>
              {isValid ? (
                <div className="flex items-center text-sm text-green-600">
                  <Check className="w-4 h-4 mr-1" />
                  Service Available
                </div>
              ) : (
                <div className="text-sm text-red-500">
                  Service Not Available
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
