import React from 'react';
import { ArrowLeft, Brush } from 'lucide-react';
import { motion } from 'framer-motion';

interface FormHeaderProps {
  onBack: () => void;
}

export function FormHeader({ onBack }: FormHeaderProps) {
  return (
    <div className="bg-white rounded-2xl shadow-sm p-6 mb-8">
      <div className="flex items-center space-x-4">
        <button
          onClick={onBack}
          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          aria-label="Go back"
        >
          <ArrowLeft className="w-5 h-5 text-gray-600" />
        </button>
        
        <div className="flex items-center space-x-4">
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="p-3 bg-brand-100 rounded-xl"
          >
            <Brush className="w-8 h-8 text-brand-600" />
          </motion.div>
          <div>
            <motion.h1 
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-2xl font-bold text-gray-900"
            >
              Residential Carpet Cleaning
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-gray-600"
            >
              Professional carpet cleaning for your home
            </motion.p>
          </div>
        </div>
      </div>
    </div>
  );
}
