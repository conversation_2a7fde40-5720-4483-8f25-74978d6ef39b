import React from 'react';
import { Building2, MapPin, Building, Layers, Users, Clock, Car, Shield } from 'lucide-react';
import { motion } from 'framer-motion';

interface PropertyDetailsProps {
  details: {
    propertyType: string;
    industryType: string;
    propertyAddress: string;
    squareFootage: number;
    floors: number;
  };
  onChange: (details: any) => void;
}

export function PropertyDetails({ details, onChange }: PropertyDetailsProps) {
  const propertyTypes = [
    'Commercial Building',
    'Industrial Facility',
    'Retail Center',
    'Medical Facility',
    'Educational Institution',
    'Mixed-Use Development',
    'Other'
  ];

  const industryTypes = [
    'Corporate',
    'Manufacturing',
    'Healthcare',
    'Education',
    'Retail',
    'Hospitality',
    'Government',
    'Other'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Building2 className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Property Details</h3>
          <p className="text-gray-600">Tell us about your facility</p>
        </div>
      </motion.div>

      {/* Basic Property Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Property Type <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Building2 className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={details.propertyType}
              onChange={(e) => onChange({ ...details, propertyType: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select property type</option>
              {propertyTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Industry Type <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={details.industryType}
              onChange={(e) => onChange({ ...details, industryType: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select industry type</option>
              {industryTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
        </motion.div>
      </div>

      {/* Space Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Square Footage <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.squareFootage || ''}
              onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Total floor area"
              min="1"
              required
            />
          </div>
          <p className="text-sm text-gray-500 mt-1">Total area to be restored</p>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Number of Floors <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Layers className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.floors || ''}
              onChange={(e) => onChange({ ...details, floors: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Number of floors"
              min="1"
              required
            />
          </div>
          <p className="text-sm text-gray-500 mt-1">Total number of floors needing service</p>
        </motion.div>
      </div>

      {/* Location */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Property Address <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={details.propertyAddress}
            onChange={(e) => onChange({ ...details, propertyAddress: e.target.value })}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            placeholder="Enter complete address"
            required
          />
        </div>
      </motion.div>
    </div>
  );
}
