import { useState, useEffect } from 'react';
import { AlertTriangle, CheckCircle, XCircle, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { isSupabaseConfigured } from '../../lib/supabase/client';
import { validateSquareConfig } from '../../lib/square/config';

export function EnvironmentChecker() {
  const [showDetails, setShowDetails] = useState(false);
  const [showSecrets, setShowSecrets] = useState(false);

  // Security warning for development
  useEffect(() => {
    if (!import.meta.env.PROD) {
      console.warn('🔒 SECURITY WARNING: Environment checker is active. This component exposes configuration details and should NEVER be enabled in production.');
    }
  }, []);

  // Only show in development - NEVER in production for security
  if (import.meta.env.PROD) {
    return null;
  }

  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  const squareValidation = validateSquareConfig();

  const issues = [
    !isSupabaseConfigured && 'Supabase not configured',
    !squareValidation.isValid && 'Square not configured',
    ...squareValidation.errors
  ].filter(Boolean);

  const getStatusIcon = (isValid: boolean) => {
    return isValid ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const maskValue = (value: string | undefined) => {
    if (!value) return 'Not Set';
    if (!showSecrets) {
      // Show only first 8 characters and last 4 for better security
      if (value.length > 12) {
        return value.substring(0, 8) + '***' + value.substring(value.length - 4);
      }
      return value.substring(0, 6) + '***';
    }
    // Even when showing secrets, mask sensitive parts
    if (value.length > 20) {
      return value.substring(0, 12) + '***' + value.substring(value.length - 8);
    }
    return value;
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={() => setShowDetails(!showDetails)}
        className={`
          px-3 py-2 rounded-lg shadow-lg text-sm font-medium transition-all
          ${issues.length > 0 
            ? 'bg-red-500 text-white hover:bg-red-600' 
            : 'bg-green-500 text-white hover:bg-green-600'
          }
        `}
      >
        <div className="flex items-center gap-2">
          {issues.length > 0 ? (
            <AlertTriangle className="w-4 h-4" />
          ) : (
            <CheckCircle className="w-4 h-4" />
          )}
          {issues.length > 0 ? `${issues.length} Issues` : 'All Good'}
        </div>
      </button>

      {showDetails && (
        <div className="absolute bottom-12 left-0 w-96 bg-white rounded-lg shadow-xl border p-4 text-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Environment Status</h3>
            <div className="flex gap-1">
              <button
                onClick={() => window.location.reload()}
                className="p-1 hover:bg-gray-100 rounded"
                title="Refresh"
              >
                <RefreshCw className="w-4 h-4 text-gray-500" />
              </button>
              <button
                onClick={() => setShowSecrets(!showSecrets)}
                className="p-1 hover:bg-gray-100 rounded"
                title="Toggle secrets"
              >
                {showSecrets ? (
                  <EyeOff className="w-4 h-4 text-gray-500" />
                ) : (
                  <Eye className="w-4 h-4 text-gray-500" />
                )}
              </button>
            </div>
          </div>

          <div className="space-y-3">
            {/* Supabase Status */}
            <div className="border rounded p-3">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(isSupabaseConfigured)}
                <span className="font-medium">Supabase Database</span>
              </div>
              <div className="text-xs text-gray-600 space-y-1">
                <div>URL: {supabaseUrl ? '✓ Set' : '✗ Missing'}</div>
                <div>Key: {supabaseKey ? '✓ Set' : '✗ Missing'}</div>
                {showSecrets && (
                  <div className="mt-2 p-2 bg-gray-50 rounded font-mono text-xs">
                    <div>URL: {maskValue(supabaseUrl)}</div>
                    <div>Key: {maskValue(supabaseKey)}</div>
                  </div>
                )}
              </div>
            </div>

            {/* Square Status */}
            <div className="border rounded p-3">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(squareValidation.isValid)}
                <span className="font-medium">Square Payments</span>
              </div>
              <div className="text-xs text-gray-600 space-y-1">
                {squareValidation.isValid ? (
                  <div className="text-green-600">✓ All credentials configured</div>
                ) : (
                  <div className="space-y-1">
                    {squareValidation.errors.map((error, index) => (
                      <div key={index} className="text-red-600">✗ {error}</div>
                    ))}
                  </div>
                )}
                {showSecrets && (
                  <div className="mt-2 p-2 bg-gray-50 rounded font-mono text-xs">
                    <div>App ID: {maskValue(import.meta.env.VITE_SQUARE_APPLICATION_ID)}</div>
                    <div>Location: {maskValue(import.meta.env.VITE_SQUARE_LOCATION_ID)}</div>
                    <div>Token: {maskValue(import.meta.env.VITE_SQUARE_ACCESS_TOKEN)}</div>
                    <div>Env: {import.meta.env.VITE_SQUARE_ENVIRONMENT || 'sandbox'}</div>
                  </div>
                )}
              </div>
            </div>

            {/* Localhost Settings */}
            <div className="border rounded p-3">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="w-4 h-4 text-blue-500" />
                <span className="font-medium">Localhost Setup</span>
              </div>
              <div className="text-xs text-gray-600 space-y-1">
                <div>Mode: Development</div>
                <div>URL: http://localhost:5173</div>
                <div>Hot Reload: Active</div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="border-t pt-3">
              <div className="text-xs text-gray-500 mb-2">Quick Actions:</div>
              <div className="space-y-1">
                <button
                  onClick={() => {
                    console.log('🔧 Environment Status:', {
                      supabase: {
                        configured: isSupabaseConfigured,
                        url: supabaseUrl || 'MISSING',
                        key: supabaseKey ? 'SET' : 'MISSING'
                      },
                      square: {
                        configured: squareValidation.isValid,
                        errors: squareValidation.errors
                      },
                      localhost: {
                        port: window.location.port,
                        protocol: window.location.protocol
                      }
                    });
                  }}
                  className="text-blue-600 hover:text-blue-800 text-xs block"
                >
                  📋 Log Status to Console
                </button>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(`
# Current Environment Status
Supabase: ${isSupabaseConfigured ? 'OK' : 'NEEDS SETUP'}
Square: ${squareValidation.isValid ? 'OK' : 'NEEDS CREDENTIALS'}
Issues: ${issues.join(', ') || 'None'}
                    `.trim());
                  }}
                  className="text-blue-600 hover:text-blue-800 text-xs block"
                >
                  📋 Copy Status Report
                </button>
              </div>
            </div>

            {/* Next Steps */}
            {issues.length > 0 && (
              <div className="border-t pt-3">
                <div className="text-xs font-medium text-gray-700 mb-2">Next Steps:</div>
                <div className="text-xs text-gray-600 space-y-1">
                  {!squareValidation.isValid && (
                    <div>1. Get Square credentials from developer.squareup.com</div>
                  )}
                  {!isSupabaseConfigured && (
                    <div>2. Check Supabase configuration</div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
} 
