/**
 * Square Payment Modal
 * Integrated Square Web SDK payment modal for seamless payment processing
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Loader, CreditCard, Shield } from 'lucide-react';
import { SquareWebSDKPayment } from './SquareWebSDKPayment';
import type { PaymentResult } from '../lib/square';

interface SquarePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  description: string;
  customerEmail: string;
  orderId: string;
  paymentRecordId: string;
  bookingId: string;
  onPaymentSuccess: (result: PaymentResult) => void;
  onPaymentError: (error: string) => void;
}

export const SquarePaymentModal: React.FC<SquarePaymentModalProps> = ({
  isOpen,
  onClose,
  amount,
  description,
  customerEmail,
  orderId,
  paymentRecordId,
  bookingId,
  onPaymentSuccess,
  onPaymentError
}) => {
  const [paymentStatus, setPaymentStatus] = useState<'form' | 'processing' | 'success' | 'error'>('form');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handlePaymentSuccess = async (result: PaymentResult) => {
    setPaymentStatus('success');
    
    // Update payment record in database
    try {
      // Here you would typically update the payment record status
      console.log('Payment successful:', result);
      
      // Call parent success handler
      onPaymentSuccess(result);
      
      // Auto-close after success
      setTimeout(() => {
        onClose();
      }, 3000);
      
    } catch (error) {
      console.error('Error updating payment record:', error);
      setPaymentStatus('error');
      setErrorMessage('Failed to update payment record. Please contact support.');
    }
  };

  const handlePaymentError = (error: string) => {
    setPaymentStatus('error');
    setErrorMessage(error);
    onPaymentError(error);
    // Do not close modal, allow retry
  };

  const handleClose = () => {
    setPaymentStatus('form');
    setErrorMessage('');
    onClose();
  };

  const handleRetry = async () => {
    setPaymentStatus('form');
    setErrorMessage('');
    // Reinitialize payment form on retry
    try {
      await squarePaymentService.initialize();
    } catch (error) {
      handlePaymentError('Failed to reinitialize payment form');
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm"
              onClick={handleClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", duration: 0.5 }}
              className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl overflow-hidden"
            >
              {/* Close Button */}
              <button
                onClick={handleClose}
                className="absolute top-4 right-4 p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors z-10"
              >
                <X className="w-5 h-5 text-gray-600" />
              </button>

              {/* Content */}
              <div className="p-6">
                <AnimatePresence mode="wait">
                  {paymentStatus === 'form' && (
                    <motion.div
                      key="form"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                    >
                      {/* Header */}
                      <div className="text-center mb-6">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <CreditCard className="w-8 h-8 text-blue-600" />
                        </div>
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">
                          Complete Payment
                        </h2>
                        <p className="text-gray-600">
                          Secure payment for your cleaning service
                        </p>
                      </div>

                      {/* Payment Details */}
                      <div className="bg-gray-50 rounded-lg p-4 mb-6">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600">Amount:</span>
                          <span className="text-xl font-bold text-gray-900">
                            ${(amount / 100).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600">Service:</span>
                          <span className="text-gray-900">{description}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Order ID:</span>
                          <span className="text-gray-900 font-mono text-sm">{orderId}</span>
                        </div>
                      </div>

                      {/* Square Payment Form */}
                      <SquareWebSDKPayment
                        amount={amount}
                        description={description}
                        customerEmail={customerEmail}
                        orderId={orderId}
                        onPaymentSuccess={handlePaymentSuccess}
                        onPaymentError={handlePaymentError}
                        className="mb-4"
                      />

                      {/* Security Notice */}
                      <div className="flex items-center justify-center text-sm text-gray-500 mt-4">
                        <Shield className="w-4 h-4 mr-2 text-green-500" />
                        <span>Secured by Square • PCI Compliant</span>
                      </div>
                    </motion.div>
                  )}

                  {paymentStatus === 'success' && (
                    <motion.div
                      key="success"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="text-center py-8"
                    >
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: "spring" }}
                        className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
                      >
                        <CheckCircle className="w-10 h-10 text-green-600" />
                      </motion.div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">
                        Payment Successful!
                      </h3>
                      <p className="text-gray-600 mb-6">
                        Your payment has been processed successfully. You will receive a confirmation email shortly.
                      </p>
                      <div className="bg-green-50 rounded-lg p-4">
                        <p className="text-green-800 font-medium">
                          Amount Paid: ${(amount / 100).toFixed(2)}
                        </p>
                        <p className="text-green-700 text-sm mt-1">
                          Booking ID: {bookingId}
                        </p>
                      </div>
                    </motion.div>
                  )}

                  {paymentStatus === 'error' && (
                    <motion.div
                      key="error"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="text-center py-8"
                    >
                      <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <AlertCircle className="w-10 h-10 text-red-600" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">
                        Payment Failed
                      </h3>
                      <p className="text-gray-600 mb-6">
                        {errorMessage || 'There was an issue processing your payment. Please try again.'}
                      </p>
                      <div className="space-y-3">
                        <button
                          onClick={handleRetry}
                          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                        >
                          Try Again
                        </button>
                        <button
                          onClick={handleClose}
                          className="w-full bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};
