import React from 'react';
import { Building2, Building, GraduationCap, Stethoscope, ShoppingBag, Warehouse } from 'lucide-react';
import { FormField } from '../../components/FormFields';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'office',
    icon: Building2,
    label: 'Office Buildings',
    description: 'Corporate offices & business centers'
  },
  {
    id: 'medical',
    icon: Stethoscope,
    label: 'Medical Facilities',
    description: 'Hospitals, clinics & medical offices'
  },
  {
    id: 'education',
    icon: GraduationCap,
    label: 'Educational',
    description: 'Schools, universities & institutions'
  },
  {
    id: 'retail',
    icon: ShoppingBag,
    label: 'Retail & Commercial',
    description: 'Stores, malls & shopping centers'
  },
  {
    id: 'industrial',
    icon: Warehouse,
    label: 'Industrial',
    description: 'Warehouses & manufacturing facilities'
  },
  {
    id: 'government',
    icon: Building,
    label: 'Government',
    description: 'Government & municipal buildings'
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h3 className="text-xl font-semibold text-gray-900">
          Select Your Industry
        </h3>
        <p className="text-gray-600 mt-2">
          Choose your facility type for specialized cleaning solutions
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`relative w-full p-6 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex flex-col items-center text-center">
                <div className={`p-4 rounded-full mb-4 transition-colors ${
                  isSelected ? 'bg-brand-100' : 'bg-gray-50'
                }`}>
                  <Icon className={`w-8 h-8 ${
                    isSelected ? 'text-brand-600' : 'text-gray-600'
                  }`} />
                </div>
                <h4 className="font-medium text-gray-900 mb-2">
                  {service.label}
                </h4>
                <p className="text-sm text-gray-600">
                  {service.description}
                </p>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
