import React from 'react';
import { Users, Monitor } from 'lucide-react';
import { motion } from 'framer-motion';

interface WorkAreaProps {
  employeeCount: number;
  squareFootage: number;
}

export function WorkArea({ employeeCount, squareFootage }: WorkAreaProps) {
  const scale = Math.min(squareFootage / 5000, 1);
  const desks = Math.min(employeeCount, 16);

  return (
    <div className="bg-green-50/80 backdrop-blur rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300">
      <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
        <Users className="w-4 h-4 mr-2 text-green-600" />
        Work Area ({Math.ceil(squareFootage * 0.6)} sq ft)
      </h4>

      <div className="grid grid-cols-4 gap-3">
        {Array.from({ length: desks }).map((_, i) => (
          <motion.div
            key={i}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 0.8 + (scale * 0.2), opacity: 1 }}
            transition={{ delay: i * 0.1, duration: 0.3 }}
            className="group relative"
          >
            <div className="aspect-square bg-white rounded-lg border border-green-200 shadow-sm 
                          transition-all duration-300 hover:shadow-md hover:scale-105">
              <div className="absolute inset-0 flex items-center justify-center opacity-0 
                            group-hover:opacity-100 transition-opacity">
                <Monitor className="w-4 h-4 text-green-600" />
              </div>
            </div>
          </motion.div>
        ))}
        {employeeCount > 16 && (
          <div className="flex items-center justify-center">
            <span className="text-sm text-gray-500">+{employeeCount - 16} more</span>
          </div>
        )}
      </div>
    </div>
  );
}
