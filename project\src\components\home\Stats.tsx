import React from 'react';

const stats = [
  { label: 'Eco-Friendly', value: '100% Green Products' },
  { label: '24/7 Service', value: 'Always Available' },
  { label: 'Satisfaction', value: '5-Star Rated' }
];

export function Stats() {
  return (
    <div className="grid grid-cols-3 gap-8">
      {stats.map((stat) => (
        <div key={stat.label} className="text-center">
          <dt className="text-base font-medium text-gray-600">{stat.label}</dt>
          <dd className="mt-1 text-2xl font-semibold tracking-tight text-brand-600">
            {stat.value}
          </dd>
        </div>
      ))}
    </div>
  );
}
