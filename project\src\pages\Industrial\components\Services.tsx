import React from 'react';
import { motion } from 'framer-motion';
import { 
  Sparkles, Brush, Construction, Sprout, 
  Grid, <PERSON>s, Hammer, ArrowRight 
} from 'lucide-react';

const services = [
  {
    icon: Sparkles,
    title: 'Deep Cleaning',
    description: 'Thorough cleaning of industrial spaces',
    features: ['Equipment cleaning', 'High dusting', 'Floor maintenance']
  },
  {
    icon: Construction,
    title: 'Industrial Maintenance',
    description: 'Regular maintenance cleaning',
    features: ['Daily/weekly service', 'OSHA compliance', 'Safety protocols']
  },
  {
    icon: Brush,
    title: 'Floor Care',
    description: 'Specialized floor cleaning services',
    features: ['Concrete polishing', 'Epoxy floors', 'Anti-slip treatment']
  },
  {
    icon: Sprout,
    title: 'Sanitization',
    description: 'Industrial-grade sanitization',
    features: ['EPA-approved products', 'Surface disinfection', 'Air quality']
  },
  {
    icon: Grid,
    title: 'Equipment Cleaning',
    description: 'Machinery and equipment cleaning',
    features: ['Safe cleaning methods', 'Preventive maintenance', 'Compliance']
  },
  {
    icon: Waves,
    title: 'Pressure Washing',
    description: 'High-pressure cleaning solutions',
    features: ['Loading docks', 'Exterior walls', 'Concrete surfaces']
  }
];

export function Services() {
  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-gray-900 mb-4"
          >
            Our Industrial Services
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Comprehensive cleaning solutions for industrial facilities
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className="p-8">
                    <div className="rounded-xl bg-brand-100 p-3 w-fit mb-6">
                      <Icon className="w-6 h-6 text-brand-600" />
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 mb-6">
                      {service.description}
                    </p>

                    <div className="space-y-3">
                      {service.features.map((feature, i) => (
                        <div key={i} className="flex items-center text-gray-600">
                          <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="px-8 py-4 bg-gray-50 group-hover:bg-brand-50 transition-colors">
                    <div className="flex items-center justify-between text-brand-600">
                      <span className="font-medium">Learn More</span>
                      <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
