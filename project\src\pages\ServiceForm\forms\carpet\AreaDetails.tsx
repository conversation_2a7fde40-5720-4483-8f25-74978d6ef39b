import React from 'react';
import { FormField } from '../../components/FormFields';
import { Building2, MapPin, Building } from 'lucide-react';

interface AreaDetailsData {
  squareFootage: number;
  propertyAddress?: string;
  propertyType?: string;
  industryType?: string;
}

interface AreaDetailsProps {
  details: AreaDetailsData;
  onChange: (details: AreaDetailsData) => void;
}

export function AreaDetails({ details, onChange }: AreaDetailsProps) {
  const industries = [
    'Office Building',
    'Medical Facility',
    'Educational Institution',
    'Retail Space',
    'Restaurant',
    'Hotel/Hospitality',
    'Industrial Facility',
    'Other'
  ];

  const propertyTypes = [
    'Commercial',
    'Industrial',
    'Institutional',
    'Multi-Family',
    'Other'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-6">
        <div className="p-3 rounded-full bg-brand-100">
          <Building2 className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Property Details</h3>
          <p className="text-gray-600">Tell us about your space</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField label="Property Type" required>
          <select
            value={details.propertyType}
            onChange={(e) => onChange({ ...details, propertyType: e.target.value })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg"
            required
          >
            <option value="">Select property type</option>
            {propertyTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </FormField>

        <FormField label="Industry Type" required>
          <select
            value={details.industryType}
            onChange={(e) => onChange({ ...details, industryType: e.target.value })}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg"
            required
          >
            <option value="">Select industry type</option>
            {industries.map((industry) => (
              <option key={industry} value={industry}>{industry}</option>
            ))}
          </select>
        </FormField>
      </div>

      <FormField label="Property Address" required>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={details.propertyAddress}
            onChange={(e) => onChange({ ...details, propertyAddress: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
            placeholder="Enter complete address"
            required
          />
        </div>
      </FormField>

      <FormField label="Square Footage" required>
        <div className="relative">
          <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="number"
            value={details.squareFootage || ''}
            onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
            placeholder="Total area to be cleaned"
            required
          />
        </div>
      </FormField>
    </div>
  );
}
