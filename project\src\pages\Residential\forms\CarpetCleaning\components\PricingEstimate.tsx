import React from 'react';
import { DollarSign, Check, Info } from 'lucide-react';
import { motion } from 'framer-motion';
import type { FormData } from '../types';

interface PricingEstimateProps {
  formData: FormData;
}

export function PricingEstimate({ formData }: PricingEstimateProps) {
  const calculateBasePrice = () => {
    const { serviceType, propertyDetails, carpetDetails, stainTreatment } = formData;
    
    // Base price calculation
    let basePrice = 0;
    
    // Calculate by square footage ($0.50 per sq ft)
    const sqftPrice = (propertyDetails.squareFootage || 0) * 0.50;
    
    // Calculate by rooms ($90 per room)
    const roomPrice = (propertyDetails.bedrooms || 0) * 90;
    
    // Use the higher of the two calculations
    basePrice = Math.max(sqftPrice, roomPrice);
    
    // Minimum price based on service type
    const minimumPrice = {
      'standard': 99,
      'deep': 149,
      'pet': 129,
      'move': 179,
      'area': 89
    };
    
    basePrice = Math.max(basePrice, minimumPrice[serviceType as keyof typeof minimumPrice] || 99);
    
    // Adjustments based on carpet condition
    if (carpetDetails.carpetCondition === 'poor') basePrice *= 1.2; // 20% more for poor condition
    
    // Adjustments based on traffic level
    if (carpetDetails.trafficLevel === 'heavy') basePrice *= 1.1; // 10% more for heavy traffic
    if (carpetDetails.trafficLevel === 'extreme') basePrice *= 1.15; // 15% more for extreme traffic
    
    // Adjustments for stain treatment
    if (stainTreatment.hasStains && stainTreatment.stainTypes.length > 0) {
      basePrice += stainTreatment.stainTypes.length * 15; // $15 per stain type
    }
    
    return Math.round(basePrice);
  };

  const getServiceName = () => {
    switch (formData.serviceType) {
      case 'standard': return 'Standard Carpet Cleaning';
      case 'deep': return 'Deep Carpet Cleaning';
      case 'pet': return 'Pet Treatment';
      case 'move': return 'Move-In/Out Cleaning';
      case 'area': return 'Area Rug Cleaning';
      default: return 'Carpet Cleaning';
    }
  };

  const basePrice = calculateBasePrice();
  const hasDiscount = formData.schedule.promoCode?.toLowerCase() === 'save10';
  const discount = hasDiscount ? Math.round(basePrice * 0.1) : 0;
  const totalPrice = basePrice - discount;

  if (!formData.serviceType || !formData.propertyDetails.squareFootage) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-lg p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <div className="p-2 rounded-lg bg-brand-100">
          <DollarSign className="w-5 h-5 text-brand-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900">Price Estimate</h3>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center pb-2 border-b border-gray-100">
          <span className="text-gray-600">{getServiceName()}</span>
          <span className="font-medium">${basePrice}</span>
        </div>

        {hasDiscount && (
          <div className="flex justify-between items-center pb-2 border-b border-gray-100 text-green-600">
            <span>Promo Code (SAVE10)</span>
            <span>-${discount}</span>
          </div>
        )}

        <div className="flex justify-between items-center pt-2">
          <span className="font-semibold text-gray-900">Estimated Total</span>
          <span className="text-xl font-bold text-brand-600">${totalPrice}</span>
        </div>

        <div className="text-xs text-gray-500 mt-2">
          * Final price may vary based on inspection and additional services requested
        </div>
      </div>

      <div className="mt-6 space-y-3">
        <h4 className="font-medium text-gray-900 text-sm">Included in Every Service:</h4>
        {[
          'Pre-inspection of all areas',
          'Pre-treatment of spots and stains',
          'Hot water extraction cleaning',
          'Post-cleaning inspection',
          'Fast drying process'
        ].map((item, index) => (
          <div key={index} className="flex items-start">
            <Check className="w-4 h-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
            <span className="text-sm text-gray-600">{item}</span>
          </div>
        ))}
      </div>

      <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-start">
        <Info className="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
        <div className="text-xs text-blue-700">
          <p className="font-medium mb-1">Pricing Information:</p>
          <ul className="list-disc pl-4 space-y-1">
            <li>$90 per room</li>
            <li>$0.50 per square foot</li>
            <li>Minimum charges apply based on service type</li>
          </ul>
        </div>
      </div>

      {formData.carpetDetails.carpetCondition === 'poor' && (
        <div className="mt-4 p-3 bg-yellow-50 rounded-lg flex items-start">
          <Info className="w-4 h-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
          <span className="text-xs text-yellow-700">
            Based on your carpet condition, additional treatments may be recommended during inspection.
          </span>
        </div>
      )}
    </motion.div>
  );
}
