import React from 'react';
import { Check, DollarSign } from 'lucide-react';

interface OrderSummaryProps {
  formData: any;
}

export function OrderSummary({ formData }: OrderSummaryProps) {
  const calculatePrice = () => {
    let basePrice = formData.squareFootage * 0.50; // $0.50 per sq ft
    
    // Add service type multiplier
    if (formData.serviceType === 'commercial') basePrice *= 1.2;
    if (formData.serviceType === 'deep-steam') basePrice *= 1.5;
    
    // Add additional services
    formData.additionalServices.forEach(() => {
      basePrice += 50; // $50 per additional service
    });
    
    return basePrice;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 sticky top-24">
      <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <DollarSign className="w-5 h-5 mr-2 text-brand-600" />
        Order Summary
      </h3>

      {formData.serviceType && (
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Service Type:</span>
            <span className="font-medium">{formData.serviceType}</span>
          </div>

          {formData.squareFootage > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Area:</span>
              <span className="font-medium">{formData.squareFootage} sq ft</span>
            </div>
          )}

          {formData.additionalServices.length > 0 && (
            <div className="border-t pt-4">
              <p className="text-sm text-gray-600 mb-2">Additional Services:</p>
              {formData.additionalServices.map((service: string) => (
                <div key={service} className="flex items-center text-sm">
                  <Check className="w-4 h-4 text-brand-600 mr-2" />
                  <span>{service}</span>
                </div>
              ))}
            </div>
          )}

          <div className="border-t pt-4">
            <div className="flex justify-between text-lg font-semibold">
              <span>Estimated Total:</span>
              <span>${calculatePrice().toFixed(2)}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Final price may vary based on inspection
            </p>
          </div>
        </div>
      )}

      {!formData.serviceType && (
        <p className="text-gray-500 text-center">
          Select services to see pricing
        </p>
      )}
    </div>
  );
}
