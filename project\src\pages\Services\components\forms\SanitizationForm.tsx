import React from 'react';
import { Form, FormField } from './FormFields';

interface SanitizationFormProps {
  onSubmit: (data: any) => void;
}

export function SanitizationForm({ onSubmit }: SanitizationFormProps) {
  return (
    <Form onSubmit={onSubmit}>
      <FormField label="Approximate Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Facility Type" required>
        <select
          name="facilityType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="office">Office Space</option>
          <option value="medical">Medical Facility</option>
          <option value="daycare">Daycare/School</option>
          <option value="gym">Gym/Fitness Center</option>
          <option value="retail">Retail Space</option>
          <option value="restaurant">Restaurant</option>
        </select>
      </FormField>

      <FormField label="Service Level" required>
        <select
          name="serviceLevel"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="standard">Standard Sanitization</option>
          <option value="advanced">Advanced Disinfection</option>
          <option value="medical">Medical-Grade Sanitization</option>
          <option value="covid">COVID-19 Deep Sanitization</option>
        </select>
      </FormField>

      <FormField label="Frequency">
        <select
          name="frequency"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="one-time">One-time Service</option>
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="biweekly">Bi-weekly</option>
          <option value="monthly">Monthly</option>
        </select>
      </FormField>

      <FormField label="High-Touch Areas">
        <div className="space-y-2">
          {[
            'Door Handles',
            'Light Switches',
            'Elevator Buttons',
            'Handrails',
            'Countertops',
            'Shared Equipment',
            'Restroom Fixtures'
          ].map((area) => (
            <label key={area} className="flex items-center">
              <input
                type="checkbox"
                name="highTouchAreas"
                value={area.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{area}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Additional Notes">
        <textarea
          name="notes"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific sanitization requirements or concerns?"
        />
      </FormField>
    </Form>
  );
}
