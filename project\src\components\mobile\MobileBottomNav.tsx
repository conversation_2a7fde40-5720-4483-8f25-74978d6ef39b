import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Home, 
  Calendar, 
  Phone, 
  User, 
  MessageCircle,
  MapPin
} from 'lucide-react';
import { useDeviceInfo } from '../../utils/deviceCompatibility';

interface NavItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  path: string;
  badge?: number;
}

const navItems: NavItem[] = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    path: '/'
  },
  {
    id: 'book',
    label: 'Book',
    icon: Calendar,
    path: '/residential'
  },
  {
    id: 'contact',
    label: 'Contact',
    icon: Phone,
    path: '/contact'
  },
  {
    id: 'account',
    label: 'Account',
    icon: User,
    path: '/account'
  }
];

const MobileBottomNav: React.FC = () => {
  const location = useLocation();
  const { isMobile, isTouch } = useDeviceInfo();

  // Only show on mobile devices
  if (!isMobile) {
    return null;
  }

  return (
    <motion.nav
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
      className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-lg border-t border-gray-200/50 safe-area-bottom"
      style={{
        paddingBottom: 'env(safe-area-inset-bottom)',
        boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.1)'
      }}
    >
      <div className="flex items-center justify-around px-2 py-2">
        {navItems.map((item) => {
          const isActive = location.pathname === item.path || 
                          (item.path !== '/' && location.pathname.startsWith(item.path));
          const Icon = item.icon;

          return (
            <Link
              key={item.id}
              to={item.path}
              className="relative flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-200 min-w-[60px] min-h-[60px]"
              style={{
                minHeight: isTouch ? '48px' : '44px', // Touch-friendly targets
                minWidth: isTouch ? '48px' : '44px'
              }}
            >
              <motion.div
                className={`relative flex flex-col items-center justify-center w-full h-full rounded-xl transition-all duration-200 ${
                  isActive 
                    ? 'bg-brand-600 text-white shadow-lg' 
                    : 'text-gray-600 hover:bg-gray-100 active:bg-gray-200'
                }`}
                whileTap={{ scale: 0.95 }}
                layout
              >
                {/* Active indicator */}
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 bg-brand-600 rounded-xl"
                    transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                  />
                )}
                
                {/* Icon */}
                <motion.div
                  className="relative z-10"
                  animate={{
                    scale: isActive ? 1.1 : 1,
                    y: isActive ? -1 : 0
                  }}
                  transition={{ duration: 0.2 }}
                >
                  <Icon 
                    size={20} 
                    className={`transition-colors duration-200 ${
                      isActive ? 'text-white' : 'text-gray-600'
                    }`}
                  />
                </motion.div>
                
                {/* Label */}
                <motion.span
                  className={`relative z-10 text-xs font-medium mt-1 transition-colors duration-200 ${
                    isActive ? 'text-white' : 'text-gray-600'
                  }`}
                  animate={{
                    opacity: isActive ? 1 : 0.8,
                    fontWeight: isActive ? 600 : 500
                  }}
                >
                  {item.label}
                </motion.span>
                
                {/* Badge */}
                {item.badge && item.badge > 0 && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center font-bold"
                  >
                    {item.badge > 99 ? '99+' : item.badge}
                  </motion.div>
                )}
              </motion.div>
            </Link>
          );
        })}
      </div>
      
      {/* Quick Action Button */}
      <motion.div
        className="absolute -top-8 left-1/2 transform -translate-x-1/2"
        initial={{ scale: 0, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        transition={{ delay: 0.2, type: 'spring', stiffness: 300 }}
      >
        <Link
          to="/residential"
          className="bg-brand-600 text-white p-4 rounded-full shadow-lg hover:bg-brand-700 transition-colors duration-200 flex items-center justify-center"
          style={{
            minHeight: isTouch ? '56px' : '52px',
            minWidth: isTouch ? '56px' : '52px'
          }}
        >
          <motion.div
            whileTap={{ scale: 0.9 }}
            className="flex items-center justify-center"
          >
            <Calendar size={24} />
          </motion.div>
        </Link>
      </motion.div>
    </motion.nav>
  );
};

export default MobileBottomNav;
