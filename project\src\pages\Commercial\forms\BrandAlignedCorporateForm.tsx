import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Sparkles, Calendar, CheckCircle, 
  Shield, Briefcase, Users, Heart, ShoppingBag,
  ArrowRight
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { useAuth } from '../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../components/PaymentOptionsModal';
import { AnimatedBackground } from '../../../components/layout/AnimatedBackground';

interface CorporateFormData {
  serviceType: string;
  facilitySize: string;
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  squareFootage: number;
  employees: number;
  floors: number;
}

const BrandAlignedCorporateForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState<CorporateFormData>({
    serviceType: '',
    facilitySize: '',
    serviceFrequency: 'monthly',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    address: '',
    squareFootage: 5000,
    employees: 50,
    floors: 2,
  });

  const serviceTypes = [
    { id: 'headquarters', name: 'Corporate Headquarters', icon: <Building className="w-6 h-6" />, description: 'Full-service corporate cleaning' },
    { id: 'branch', name: 'Branch Office', icon: <Briefcase className="w-6 h-6" />, description: 'Professional office maintenance' },
    { id: 'coworking', name: 'Co-working Space', icon: <Users className="w-6 h-6" />, description: 'Flexible workspace cleaning' },
    { id: 'mixed-use', name: 'Mixed-Use Facility', icon: <ShoppingBag className="w-6 h-6" />, description: 'Multi-purpose building care' },
  ];

  const facilitySizes = [
    { id: 'small', name: 'Small (Under 5,000 sq ft)', description: 'Perfect for growing businesses' },
    { id: 'medium', name: 'Medium (5,000 - 20,000 sq ft)', description: 'Ideal for established companies' },
    { id: 'large', name: 'Large (20,000 - 50,000 sq ft)', description: 'Corporate facility cleaning' },
    { id: 'enterprise', name: 'Enterprise (50,000+ sq ft)', description: 'Large-scale corporate services' },
  ];

  const serviceFrequencies = [
    { id: 'daily', name: 'Daily Cleaning' },
    { id: 'weekly', name: 'Weekly Service' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly Deep Clean' },
  ];

  const timeSlots = [
    { id: 'after-hours', name: 'After Hours (6PM - 10PM)', popular: true },
    { id: 'early-morning', name: 'Early Morning (5AM - 8AM)' },
    { id: 'weekend', name: 'Weekend Service' },
    { id: 'overnight', name: 'Overnight (10PM - 5AM)' },
  ];

  const addOnServices = [
    { id: 'carpet', name: 'Deep Carpet Cleaning', price: 200 },
    { id: 'windows', name: 'Exterior Window Cleaning', price: 150 },
    { id: 'sanitization', name: 'Deep Sanitization', price: 100 },
    { id: 'supply-management', name: 'Supply Management', price: 75 },
  ];

  const calculatePrice = () => {
    let basePrice = 0;
    if (formData.facilitySize === 'small') basePrice = 300;
    else if (formData.facilitySize === 'medium') basePrice = 600;
    else if (formData.facilitySize === 'large') basePrice = 1200;
    else if (formData.facilitySize === 'enterprise') basePrice = 2000;

    const addOnPrice = formData.addOns.length * 100;
    return basePrice + addOnPrice;
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 0: return !!formData.serviceType && !!formData.facilitySize && !!formData.serviceFrequency;
      case 1: return !!formData.preferredDate && !!formData.preferredTime;
      case 2: return true;
      case 3: return !!formData.companyName && !!formData.contactName && !!formData.email && !!formData.phone && !!formData.address;
      default: return false;
    }
  };

  const handleSubmit = async () => {
    if (!isStepValid(3)) return;
    if (!user) {
      navigate('/auth/login', { state: { from: '/commercial/corporate' } });
      return;
    }
    setShowPaymentModal(true);
  };

  const steps = [
    { title: 'Service Details', subtitle: 'Corporate facility info' },
    { title: 'Schedule', subtitle: 'Date and time preferences' },
    { title: 'Add-ons', subtitle: 'Additional services' },
    { title: 'Contact', subtitle: 'Finalize your booking' }
  ];

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress */}
          <div className="mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">Step {currentStep + 1} of {steps.length}</span>
              </div>
            </div>
            
            <div className="relative">
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"
                  animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
            
            <div className="text-center mt-4">
              <h1 className="text-xl font-semibold text-white">{steps[currentStep]?.title}</h1>
              <p className="text-gray-300">{steps[currentStep]?.subtitle}</p>
            </div>
          </div>

          {/* Form Content */}
          <motion.div 
            className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl shadow-2xl shadow-black/20 p-8 md:p-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
            }}
          >
            <AnimatePresence mode="wait">
              {/* Step 0: Service Details */}
              {currentStep === 0 && (
                <motion.div key="step0" initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: 20 }}>
                  <div className="space-y-8">
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-6">Corporate Service Type</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {serviceTypes.map(type => (
                          <motion.button 
                            key={type.id} 
                            onClick={() => setFormData({...formData, serviceType: type.id})} 
                            whileHover={{ scale: 1.02 }}
                            className={`p-6 rounded-2xl border-2 text-left transition-all duration-300 ${
                              formData.serviceType === type.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-green-400' 
                                : 'bg-white/5 border-white/20 hover:border-white/40'
                            }`}
                          >
                            <div className="flex items-start gap-4">
                              <div className="text-green-300">{type.icon}</div>
                              <div>
                                <h3 className="font-bold text-white text-lg">{type.name}</h3>
                                <p className="text-gray-300 text-sm">{type.description}</p>
                              </div>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h2 className="text-2xl font-bold text-white mb-6">Facility Size</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {facilitySizes.map(size => (
                          <motion.button 
                            key={size.id} 
                            onClick={() => setFormData({...formData, facilitySize: size.id})} 
                            whileHover={{ scale: 1.02 }}
                            className={`p-6 rounded-2xl border-2 text-left transition-all duration-300 ${
                              formData.facilitySize === size.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-green-400' 
                                : 'bg-white/5 border-white/20 hover:border-white/40'
                            }`}
                          >
                            <h3 className="font-bold text-white">{size.name}</h3>
                            <p className="text-gray-300 text-sm">{size.description}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h2 className="text-2xl font-bold text-white mb-6">Service Frequency</h2>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {serviceFrequencies.map(freq => (
                          <motion.button 
                            key={freq.id} 
                            onClick={() => setFormData({...formData, serviceFrequency: freq.id})} 
                            whileHover={{ scale: 1.02 }}
                            className={`p-4 rounded-xl border-2 text-white transition-all duration-300 ${
                              formData.serviceFrequency === freq.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-green-400' 
                                : 'bg-white/5 border-white/20 hover:border-white/40'
                            }`}
                          >
                            {freq.name}
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    {/* Facility Details Grid */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Facility Details</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {/* Square Footage Input */}
                        <div className="space-y-2">
                          <label className="text-sm font-semibold text-white block">Square Footage</label>
                          <div className="relative">
                            <input 
                              type="number" 
                              placeholder="e.g. 10000"
                              value={formData.squareFootage || ''}
                              onChange={(e) => setFormData({...formData, squareFootage: parseInt(e.target.value) || 0})}
                              className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300"
                              style={{
                                background: 'rgba(255, 255, 255, 0.1)',
                                backdropFilter: 'blur(10px)',
                              }}
                            />
                            <span className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 text-sm">sq ft</span>
                          </div>
                        </div>

                        {/* Employees Input */}
                        <div className="space-y-2">
                          <label className="text-sm font-semibold text-white block">Number of Employees</label>
                          <input 
                            type="number" 
                            placeholder="e.g. 50"
                            min="1"
                            value={formData.employees || ''}
                            onChange={(e) => setFormData({...formData, employees: parseInt(e.target.value) || 0})}
                            className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300"
                            style={{
                              background: 'rgba(255, 255, 255, 0.1)',
                              backdropFilter: 'blur(10px)',
                            }}
                          />
                        </div>

                        {/* Floors Input */}
                        <div className="space-y-2">
                          <label className="text-sm font-semibold text-white block">Number of Floors</label>
                          <input 
                            type="number" 
                            placeholder="e.g. 3"
                            min="1"
                            value={formData.floors || ''}
                            onChange={(e) => setFormData({...formData, floors: parseInt(e.target.value) || 1})}
                            className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300"
                            style={{
                              background: 'rgba(255, 255, 255, 0.1)',
                              backdropFilter: 'blur(10px)',
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button variant="outline" onClick={() => navigate('/commercial')}>Cancel</Button>
                    <Button onClick={() => setCurrentStep(1)} disabled={!isStepValid(0)}>
                      Continue <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 1: Schedule */}
              {currentStep === 1 && (
                <motion.div key="step1" initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: 20 }}>
                  <div className="space-y-8">
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-6">Preferred Date</h2>
                      <input 
                        type="date" 
                        onChange={e => setFormData({...formData, preferredDate: e.target.value})} 
                        className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white focus:outline-none focus:border-green-400 [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert"
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>

                    <div>
                      <h2 className="text-2xl font-bold text-white mb-6">Preferred Time</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {timeSlots.map(slot => (
                          <motion.button 
                            key={slot.id} 
                            onClick={() => setFormData({...formData, preferredTime: slot.id})} 
                            whileHover={{ scale: 1.02 }}
                            className={`p-6 rounded-2xl border-2 text-left transition-all duration-300 ${
                              formData.preferredTime === slot.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-green-400' 
                                : 'bg-white/5 border-white/20 hover:border-white/40'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white font-semibold">{slot.name}</span>
                              {slot.popular && (
                                <span className="text-xs bg-green-400 text-black px-2 py-1 rounded-full">Popular</span>
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button variant="outline" onClick={() => setCurrentStep(0)}>Back</Button>
                    <Button onClick={() => setCurrentStep(2)} disabled={!isStepValid(1)}>
                      Continue <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Add-ons */}
              {currentStep === 2 && (
                <motion.div key="step2" initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: 20 }}>
                  <div className="space-y-8">
                    <div>
                      <h2 className="text-2xl font-bold text-white mb-6">Additional Services</h2>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {addOnServices.map(addon => (
                          <motion.button 
                            key={addon.id} 
                            onClick={() => {
                              const newAddOns = formData.addOns.includes(addon.id)
                                ? formData.addOns.filter(a => a !== addon.id)
                                : [...formData.addOns, addon.id];
                              setFormData({ ...formData, addOns: newAddOns });
                            }} 
                            whileHover={{ scale: 1.02 }}
                            className={`p-6 rounded-2xl border-2 text-left transition-all duration-300 ${
                              formData.addOns.includes(addon.id) 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-green-400' 
                                : 'bg-white/5 border-white/20 hover:border-white/40'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white font-semibold">{addon.name}</span>
                              <span className="text-green-300 font-bold">+${addon.price}</span>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Special Instructions</h3>
                      <textarea 
                        placeholder="Any special requirements or notes..."
                        className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 resize-none" 
                        rows={4}
                        value={formData.specialInstructions}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button onClick={() => setCurrentStep(3)}>
                      Continue <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Contact Info */}
              {currentStep === 3 && (
                <motion.div key="step3" initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: 20 }}>
                  <div className="space-y-6">
                    <h2 className="text-2xl font-bold text-white mb-6">Contact Information</h2>
                    
                                         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                       <input 
                         type="text" 
                         placeholder="Company Name" 
                         value={formData.companyName}
                         onChange={e => setFormData({...formData, companyName: e.target.value})} 
                         className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300" 
                         style={{
                           background: 'rgba(255, 255, 255, 0.1)',
                           backdropFilter: 'blur(10px)',
                         }}
                       />
                       <input 
                         type="text" 
                         placeholder="Contact Name" 
                         value={formData.contactName}
                         onChange={e => setFormData({...formData, contactName: e.target.value})} 
                         className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300" 
                         style={{
                           background: 'rgba(255, 255, 255, 0.1)',
                           backdropFilter: 'blur(10px)',
                         }}
                       />
                     </div>

                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                       <input 
                         type="email" 
                         placeholder="Email Address" 
                         value={formData.email}
                         onChange={e => setFormData({...formData, email: e.target.value})} 
                         className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300" 
                         style={{
                           background: 'rgba(255, 255, 255, 0.1)',
                           backdropFilter: 'blur(10px)',
                         }}
                       />
                       <input 
                         type="tel" 
                         placeholder="Phone Number" 
                         value={formData.phone}
                         onChange={e => setFormData({...formData, phone: e.target.value})} 
                         className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300" 
                         style={{
                           background: 'rgba(255, 255, 255, 0.1)',
                           backdropFilter: 'blur(10px)',
                         }}
                       />
                     </div>

                     <input 
                       type="text" 
                       placeholder="Facility Address" 
                       value={formData.address}
                       onChange={e => setFormData({...formData, address: e.target.value})} 
                       className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300" 
                       style={{
                         background: 'rgba(255, 255, 255, 0.1)',
                         backdropFilter: 'blur(10px)',
                       }}
                     />

                    {/* Price Summary */}
                    <div className="bg-white/10 rounded-2xl p-6 border border-white/20">
                      <h3 className="text-lg font-semibold text-white mb-2">Estimated Quote</h3>
                      <p className="text-3xl font-bold text-green-400">${calculatePrice()}</p>
                      <p className="text-gray-300 text-sm">Final price may vary based on facility assessment</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button onClick={handleSubmit} disabled={!isStepValid(3)}>
                      Get Quote & Book Service
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* Payment Modal */}
        {showPaymentModal && (
          <PaymentOptionsModal
            isOpen={showPaymentModal}
            onClose={() => setShowPaymentModal(false)}
            amount={calculatePrice()}
            description="Corporate Cleaning Service"
            formData={formData}
            user={user}
          />
        )}
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedCorporateForm; 
