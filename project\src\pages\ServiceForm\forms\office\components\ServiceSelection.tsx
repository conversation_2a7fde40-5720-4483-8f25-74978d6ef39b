import React from 'react';
import { Sparkles, Clipboard } from 'lucide-react';

interface ServiceSelectionProps {
  services: {
    types: string[];
    customRequests: string;
  };
  onChange: (services: any) => void;
}

export function ServiceSelection({ services, onChange }: ServiceSelectionProps) {
  const serviceOptions = [
    {
      id: 'general',
      label: 'General Office Cleaning',
      description: 'Regular cleaning of workspaces and common areas'
    },
    {
      id: 'deep',
      label: 'Deep Cleaning',
      description: 'Thorough cleaning and sanitization of all surfaces'
    },
    {
      id: 'floor',
      label: 'Floor Care',
      description: 'Carpet cleaning, hard floor maintenance, and polishing'
    },
    {
      id: 'windows',
      label: 'Window Cleaning',
      description: 'Interior window and glass surface cleaning'
    },
    {
      id: 'kitchen',
      label: 'Kitchen & Break Room',
      description: 'Detailed cleaning of kitchen areas and appliances'
    },
    {
      id: 'restroom',
      label: 'Restroom Sanitization',
      description: 'Deep cleaning and sanitization of restroom facilities'
    },
    {
      id: 'disinfection',
      label: 'High-Touch Disinfection',
      description: 'Sanitization of frequently touched surfaces'
    },
    {
      id: 'green',
      label: 'Green Cleaning',
      description: 'Eco-friendly cleaning methods and products'
    }
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Sparkles className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Select Services</h3>
          <p className="text-gray-600">Choose the services you need</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {serviceOptions.map((service) => (
          <label
            key={service.id}
            className={`relative flex flex-col p-6 rounded-xl cursor-pointer transition-all ${
              services.types.includes(service.id)
                ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
            }`}
          >
            <input
              type="checkbox"
              className="sr-only"
              checked={services.types.includes(service.id)}
              onChange={(e) => {
                const newTypes = e.target.checked
                  ? [...services.types, service.id]
                  : services.types.filter(t => t !== service.id);
                onChange({ ...services, types: newTypes });
              }}
            />
            <div className="mb-2 font-medium text-gray-900">{service.label}</div>
            <div className="text-sm text-gray-600">{service.description}</div>
          </label>
        ))}
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <Clipboard className="w-5 h-5 text-brand-600" />
          <label className="text-sm font-medium text-gray-700">
            Additional Requirements or Special Instructions
          </label>
        </div>
        <textarea
          value={services.customRequests}
          onChange={(e) => onChange({ ...services, customRequests: e.target.value })}
          rows={4}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          placeholder="Please describe any specific cleaning requirements or areas that need special attention..."
        />
      </div>
    </div>
  );
}
