import React from 'react';
import { AlertCircle, Upload } from 'lucide-react';
import { motion } from 'framer-motion';

interface StainTreatmentProps {
  treatment: {
    types: string[];
    photos: File[];
    description: string;
    previousTreatments: string[];
  };
  onChange: (treatment: any) => void;
}

export function StainTreatment({ treatment = {
  types: [],
  photos: [],
  description: '',
  previousTreatments: []
}, onChange }: StainTreatmentProps) {
  const handleStainTypeChange = (type: string) => {
    const newTypes = treatment.types.includes(type)
      ? treatment.types.filter(t => t !== type)
      : [...treatment.types, type];
    onChange({ ...treatment, types: newTypes });
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      onChange({ ...treatment, photos: [...treatment.photos, ...Array.from(e.target.files)] });
    }
  };

  const stainTypes = [
    'Coffee/Tea',
    'Wine',
    'Pet Stains',
    'Food',
    'Ink',
    'Oil/Grease',
    'Unknown',
    'Other'
  ];

  const previousTreatmentOptions = [
    'Professional Cleaning',
    'DIY Cleaning',
    'Spot Treatment',
    'Steam Cleaning',
    'Chemical Treatment',
    'None'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <AlertCircle className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Stain Treatment</h3>
          <p className="text-gray-600">Tell us about any stains that need special attention</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        {/* Stain Types */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Select Stain Types <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {stainTypes.map((stain) => (
              <motion.label
                key={stain}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  treatment.types.includes(stain.toLowerCase())
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  className="sr-only"
                  checked={treatment.types.includes(stain.toLowerCase())}
                  onChange={() => handleStainTypeChange(stain.toLowerCase())}
                />
                <span className="text-gray-700">{stain}</span>
              </motion.label>
            ))}
          </div>
        </div>

        {/* Previous Treatments */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Previous Treatments
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {previousTreatmentOptions.map((treatmentType) => (
              <motion.label
                key={treatmentType}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  treatment.previousTreatments.includes(treatmentType)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={treatment.previousTreatments.includes(treatmentType)}
                  onChange={(e) => {
                    const newTreatments = e.target.checked
                      ? [...treatment.previousTreatments, treatmentType]
                      : treatment.previousTreatments.filter(t => t !== treatmentType);
                    onChange({ ...treatment, previousTreatments: newTreatments });
                  }}
                  className="sr-only"
                />
                <span className="text-gray-700">{treatmentType}</span>
              </motion.label>
            ))}
          </div>
        </div>

        {/* Stain Description */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Stain Description
          </label>
          <textarea
            value={treatment.description}
            onChange={(e) => onChange({ ...treatment, description: e.target.value })}
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            placeholder="Please describe the stains in detail (age, size, attempts to clean, etc.)"
          />
        </div>

        {/* Photo Upload */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Upload Photos (Optional)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <input
              type="file"
              id="stain-photos"
              multiple
              accept="image/*"
              className="hidden"
              onChange={handlePhotoUpload}
            />
            <label
              htmlFor="stain-photos"
              className="flex flex-col items-center cursor-pointer"
            >
              <Upload className="w-8 h-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-600">
                Click to upload photos of stained areas
              </span>
              <span className="text-xs text-gray-500 mt-1">
                Max 5 photos, 5MB each
              </span>
            </label>
          </div>
          {treatment.photos.length > 0 && (
            <div className="mt-4 flex flex-wrap gap-2">
              {treatment.photos.map((photo, index) => (
                <div
                  key={index}
                  className="relative bg-gray-100 rounded-lg p-2 text-sm text-gray-600"
                >
                  {photo.name}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
