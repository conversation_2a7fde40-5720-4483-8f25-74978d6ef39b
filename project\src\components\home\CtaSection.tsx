import React from 'react';
import { Button } from '../ui/Button';
import { Phone } from 'lucide-react';

export function CtaSection() {
  return (
    <section className="relative bg-brand-600 py-16 sm:py-20 lg:py-24">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,...')] opacity-10" />
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-4 sm:mb-6">
          Ready to Transform Your Space?
        </h2>
        <p className="text-lg sm:text-xl text-brand-50 mb-6 sm:mb-8 max-w-2xl mx-auto px-2">
          Get a free quote for your business cleaning needs. Available 24/7.
        </p>
        
        <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4">
          <Button
            size="lg"
            className="bg-white text-brand-600 hover:bg-brand-50 w-full sm:w-auto"
          >
            Get Free Quote
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="border-white text-white hover:bg-white/10 w-full sm:w-auto"
          >
            <Phone className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
            Call Us Now
          </Button>
        </div>
      </div>
    </section>
  );
}
