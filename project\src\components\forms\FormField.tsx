import React from 'react';
import { cn } from '../../lib/utils';

interface FormFieldProps {
  children: React.ReactNode;
  label: string;
  description?: string;
  error?: string;
  required?: boolean;
  className?: string;
}

export function FormField({ 
  children, 
  label, 
  description, 
  error, 
  required, 
  className 
}: FormFieldProps) {
  return (
    <div className={cn('space-y-3', className)}>
      <div>
        <div className="flex items-center space-x-2">
          <label className="block text-sm font-medium text-gray-900">
            {label}
          </label>
          {required && <span className="text-red-500">*</span>}
        </div>
        {description && (
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        )}
      </div>
      <div className="mt-2">
        {children}
      </div>
      {error && (
        <p className="text-sm text-red-600 mt-1">{error}</p>
      )}
    </div>
  );
}
