import React from 'react';
import { Form, FormField } from '../components/FormFields';

export function CarpetCleaningForm() {
  return (
    <Form onSubmit={(data) => console.log(data)}>
      <FormField label="Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Carpet Type" required>
        <select
          name="carpetType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="commercial">Commercial Grade</option>
          <option value="berber">Berber</option>
          <option value="plush">Plush</option>
          <option value="other">Other</option>
        </select>
      </FormField>

      <FormField label="Stains or Special Treatment">
        <div className="space-y-2">
          {[
            'Heavy Traffic Areas',
            'Pet Stains',
            'Food/Beverage Stains',
            'Odor Treatment',
            'Deep Sanitization'
          ].map((treatment) => (
            <label key={treatment} className="flex items-center">
              <input
                type="checkbox"
                name="treatments"
                value={treatment.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{treatment}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Special Instructions">
        <textarea
          name="instructions"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific areas of concern or special requirements?"
        />
      </FormField>
    </Form>
  );
}
