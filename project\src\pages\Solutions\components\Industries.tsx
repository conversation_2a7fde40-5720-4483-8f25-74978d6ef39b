import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Building2, GraduationCap, Stethoscope, ShoppingBag, 
  Building, Factory, ArrowRight 
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { ServiceMenu } from '../../../components/services/ServiceMenu';

const industries = [
  {
    icon: Building2,
    name: 'Corporate Offices',
    description: 'Professional cleaning for modern office environments',
    features: ['Daily maintenance', 'Common area cleaning', 'Workspace sanitization'],
    color: 'from-blue-500 to-blue-600',
    recommendedServices: ['office', 'deep', 'carpet']
  },
  {
    icon: Stethoscope,
    name: 'Healthcare Facilities',
    description: 'Specialized cleaning for medical environments',
    features: ['Medical-grade sanitization', 'Infection control', 'HIPAA compliance'],
    color: 'from-green-500 to-green-600',
    recommendedServices: ['sanitization', 'deep', 'floor']
  },
  {
    icon: GraduationCap,
    name: 'Educational Institutions',
    description: 'Safe and thorough cleaning for schools',
    features: ['Classroom cleaning', 'Common area maintenance', 'After-hours service'],
    color: 'from-purple-500 to-purple-600',
    recommendedServices: ['deep', 'sanitization', 'carpet']
  },
  {
    icon: ShoppingBag,
    name: 'Retail & Commercial',
    description: 'Cleaning solutions for retail spaces',
    features: ['Store maintenance', 'High-traffic cleaning', 'Window cleaning'],
    color: 'from-orange-500 to-orange-600',
    recommendedServices: ['window', 'floor', 'pressure']
  },
  {
    icon: Building,
    name: 'Government Facilities',
    description: 'Secure cleaning for government buildings',
    features: ['Security clearance', 'Confidential cleaning', 'Compliance focus'],
    color: 'from-red-500 to-red-600',
    recommendedServices: ['office', 'sanitization', 'deep']
  },
  {
    icon: Factory,
    name: 'Industrial & Manufacturing',
    description: 'Heavy-duty cleaning for industrial spaces',
    features: ['Equipment cleaning', 'Safety compliance', 'Specialized solutions'],
    color: 'from-teal-500 to-teal-600',
    recommendedServices: ['post-construction', 'pressure', 'floor']
  }
];

export function Industries() {
  const [showServiceMenu, setShowServiceMenu] = useState(false);
  const [selectedIndustry, setSelectedIndustry] = useState<typeof industries[0] | null>(null);

  const handleLearnMore = (industry: typeof industries[0]) => {
    setSelectedIndustry(industry);
    setShowServiceMenu(true);
  };

  return (
    <>
      <section className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="text-3xl font-bold text-gray-900 mb-4"
            >
              Industries We Serve
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-600 max-w-2xl mx-auto"
            >
              Specialized cleaning solutions for every industry
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {industries.map((industry, index) => {
              const Icon = industry.icon;
              
              return (
                <motion.div
                  key={industry.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -5 }}
                  className="group relative"
                >
                  <div className="relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                    {/* Gradient Background */}
                    <div className={`absolute inset-0 bg-gradient-to-br ${industry.color} opacity-5 
                                  group-hover:opacity-10 rounded-2xl transition-opacity`} />
                    
                    {/* Content */}
                    <div className="relative">
                      <div className={`inline-flex items-center justify-center p-3 rounded-xl 
                                    bg-gradient-to-br ${industry.color} mb-6 group-hover:scale-110 
                                    transition-transform`}>
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">
                        {industry.name}
                      </h3>
                      <p className="text-gray-600 mb-6">
                        {industry.description}
                      </p>

                      {/* Features */}
                      <div className="space-y-3 mb-6">
                        {industry.features.map((feature, i) => (
                          <div key={i} className="flex items-center text-gray-600">
                            <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                            <span className="text-sm">{feature}</span>
                          </div>
                        ))}
                      </div>

                      {/* Action Button */}
                      <Button
                        onClick={() => handleLearnMore(industry)}
                        variant="outline"
                        className="w-full group-hover:border-brand-500 group-hover:bg-brand-50"
                      >
                        Learn More
                        <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Service Menu */}
      <ServiceMenu
        isOpen={showServiceMenu}
        onClose={() => {
          setShowServiceMenu(false);
          setSelectedIndustry(null);
        }}
        onServiceSelect={(serviceId) => {
          setShowServiceMenu(false);
          setSelectedIndustry(null);
          // Navigation will be handled by the ServiceMenu component
        }}
        highlightedServices={selectedIndustry?.recommendedServices}
        industryContext={selectedIndustry?.name}
      />
    </>
  );
}
