import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Calendar, Clock, CheckCircle, 
  Shield, Star, Building2, Building,
  Users, DollarSign, Heart,
  ChevronRight, ChevronLeft, Sparkles, 
  Waves, Droplets, Wrench
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

interface BookingFormData {
  // Property Info
  propertyType: string;
  propertySize: string;
  
  // Service Details  
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  
  // Add-ons
  addOns: string[];
  
  // Contact Info
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  
  // Marketing
  howDidYouHear: string;
  newsletter: boolean;
}

const BrandAlignedPoolForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState<BookingFormData>({
    propertyType: '',
    propertySize: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    howDidYouHear: '',
    newsletter: false
  });

  // Check for form data restoration after login
  useEffect(() => {
    const savedFormData = localStorage.getItem('poolCleaningFormData');
    if (savedFormData && user) {
      try {
        const parsedData = JSON.parse(savedFormData);
        setFormData(parsedData);
        localStorage.removeItem('poolCleaningFormData');
        // Auto-open payment modal after restore
        setTimeout(() => {
          setShowPaymentModal(true);
        }, 1000);
      } catch (error) {
        console.error('Error parsing saved form data:', error);
        localStorage.removeItem('poolCleaningFormData');
      }
    }
  }, [user]);

  // Dynamic price calculation for pool cleaning services
  const calculatePrice = () => {
    const addOnPrices: Record<string, number> = {
      'chemical-balancing': 35,
      'filter-cleaning': 25,
      'equipment-check': 45
    };

    // Get base price from selected service frequency  
    const selectedSize = getPropertySizes().find(size => size.id === formData.propertySize);
    const price = selectedSize?.basePrice || 109;
    
    // Add-on costs
    const addOnTotal = formData.addOns?.reduce((total, addon) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;

    return Math.round(price + addOnTotal);
  };

  // Property types for pool cleaning services
  const propertyTypes = [
    { 
      id: 'residential', 
      name: 'Residential Pool', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Backyard swimming pool',
      details: 'Standard residential maintenance',
      time: '1-2 hours'
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo Pool', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Shared community pool',
      details: 'Building management coordination',
      time: '2-3 hours'
    },
    { 
      id: 'spa', 
      name: 'Spa/Hot Tub', 
      icon: <Droplets className="w-6 h-6" />, 
      description: 'Hot tub or spa',
      details: 'Specialized small water body care',
      time: '0.5-1 hour'
    }
  ];

  // Get icon for service package
  const getPropertySizeIcon = (sizeId: string) => {
    const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
      'basic': Droplets,
      'weekly': Waves,
      'bi-weekly': Sparkles, 
      'tri-weekly': Shield,
      'monthly': Wrench,
      'default': Waves
    };
    
    const IconComponent = iconMap[sizeId] || iconMap['default'];
    return <IconComponent className="w-8 h-8" />;
  };

  // Pool service packages by property type (maintenance intensity based)
  const propertyTypeConfigs = {
    residential: [
      { 
        id: 'basic', 
        name: 'Basic Cleaning', 
        description: 'One-time deep clean',
        rooms: 'Skimming, vacuuming, basic chemical check',
        time: '2-3 hours',
        price: 'From $129',
        basePrice: 129,
        popular: false 
      },
      { 
        id: 'weekly', 
        name: 'Weekly Service', 
        description: 'Premium maintenance plan',
        rooms: 'Complete weekly pool care',
        time: '1-2 hours/week',
        price: 'From $89/week',
        basePrice: 89,
        popular: true 
      },
      { 
        id: 'bi-weekly', 
        name: 'Bi-Weekly Service', 
        description: 'Every two weeks',
        rooms: 'Regular maintenance schedule',
        time: '1.5-2 hours/visit',
        price: 'From $109/visit',
        basePrice: 109,
        popular: false 
      },
      { 
        id: 'tri-weekly', 
        name: 'Every 3 Weeks', 
        description: 'Extended maintenance cycle',
        rooms: 'Comprehensive tri-weekly care',
        time: '2-2.5 hours/visit',
        price: 'From $119/visit',
        basePrice: 119,
        popular: false 
      },
      { 
        id: 'monthly', 
        name: 'Monthly Service', 
        description: 'Minimal maintenance plan',
        rooms: 'Monthly pool maintenance',
        time: '2-3 hours/visit',
        price: 'From $149/visit',
        basePrice: 149,
        popular: false 
      }
    ],
    apartment: [
      { 
        id: 'weekly', 
        name: 'Weekly Maintenance', 
        description: 'Community pool service',
        rooms: 'High-traffic pool maintenance',
        time: '2-3 hours/week',
        price: 'From $149/week',
        basePrice: 149,
        popular: true 
      },
      { 
        id: 'bi-weekly', 
        name: 'Bi-Weekly Service', 
        description: 'Every two weeks',
        rooms: 'Regular community maintenance',
        time: '2.5-3 hours/visit',
        price: 'From $199/visit',
        basePrice: 199,
        popular: false 
      },
      { 
        id: 'monthly', 
        name: 'Monthly Deep Clean', 
        description: 'Comprehensive monthly service',
        rooms: 'Full community pool treatment',
        time: '3-4 hours/visit',
        price: 'From $279/visit',
        basePrice: 279,
        popular: false 
      }
    ],
    spa: [
      { 
        id: 'weekly', 
        name: 'Weekly Spa Service', 
        description: 'Regular spa maintenance',
        rooms: 'Complete spa care package',
        time: '0.5-1 hour/week',
        price: 'From $79/week',
        basePrice: 79,
        popular: true 
      },
      { 
        id: 'bi-weekly', 
        name: 'Bi-Weekly Care', 
        description: 'Every two weeks',
        rooms: 'Extended spa maintenance',
        time: '1-1.5 hours/visit',
        price: 'From $89/visit',
        basePrice: 89,
        popular: false 
      },
      { 
        id: 'monthly', 
        name: 'Monthly Service', 
        description: 'Basic spa maintenance',
        rooms: 'Essential monthly spa care',
        time: '1-1.5 hours/visit',
        price: 'From $99/visit',
        basePrice: 99,
        popular: false 
      }
    ]
  };

  const getPropertySizes = () => {
    return propertyTypeConfigs[formData.propertyType as keyof typeof propertyTypeConfigs] || [];
  };

  // Add-on services for pool cleaning
  const addOnServices = [
    { 
      id: 'chemical-balancing', 
      name: 'Chemical Balancing', 
      description: 'pH & chlorine adjustment for perfect water chemistry',
      price: 35, 
      icon: <Sparkles className="w-5 h-5" />,
      recommended: true
    },
    { 
      id: 'filter-cleaning', 
      name: 'Filter Cleaning', 
      description: 'Clean & maintain pool filters for optimal circulation',
      price: 25, 
      icon: <Shield className="w-5 h-5" /> 
    },
    { 
      id: 'equipment-check', 
      name: 'Equipment Check', 
      description: 'Pump & equipment inspection for preventive maintenance',
      price: 45, 
      icon: <Wrench className="w-5 h-5" /> 
    }
  ];

  // Validation logic
  const isStepValid = (step: number) => {
    switch(step) {
      case 0: return formData.propertyType && formData.propertySize;
      case 1: return formData.preferredDate && formData.preferredTime;
      case 2: return true; // Add-ons are optional
      case 3: return formData.firstName && formData.lastName && formData.email && formData.phone && formData.address;
      default: return false;
    }
  };

  // Form submission
  const handleSubmit = async () => {
    // Check if user is authenticated
    if (!user) {
      // Save form data and redirect to login
      localStorage.setItem('poolCleaningFormData', JSON.stringify(formData));
      navigate('/auth/login');
      return;
    }
    
    try {
      // Save booking to Supabase database first
      const { supabase } = await import('../../../../lib/supabase/client');
      
      const bookingData = {
        user_id: user.id,
        service_type: 'pool',
        property_details: {
          propertyType: formData.propertyType,
          propertySize: formData.propertySize,
          address: `${formData.address}, ${formData.city}, ${formData.zipCode}`,
        },
        service_details: {
          addOns: formData.addOns,
          howDidYouHear: formData.howDidYouHear,
          newsletter: formData.newsletter,
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime,
        },
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
        },
        status: 'pending',
        total_price: calculatePrice(),
        special_instructions: formData.specialInstructions,
        metadata: {
          submittedAt: new Date().toISOString(),
          requestType: 'booking',
        }
      };

      const { data, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Error saving pool booking:', error);
        throw new Error('Failed to save booking');
      }

      console.log('Pool booking saved successfully:', data);
      
      // Clear form data after successful save
      localStorage.removeItem('poolCleaningFormData');
      
      // Open payment modal for authenticated users
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      // Still allow payment modal to open even if booking save fails
      setShowPaymentModal(true);
    }
  };

  // Calendar date generation
  const getCalendarDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
      const monthDay = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const fullDate = date.toISOString().split('T')[0];
      
      dates.push({
        display: `${dayName}, ${monthDay}`,
        value: fullDate,
        available: i <= 10 // First 10 days are available
      });
    }
    
    return dates;
  };

  // Navigation handlers
  const handleStepNavigation = (direction: 'next' | 'prev') => {
    if (direction === 'next' && currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else if (direction === 'prev' && currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const timeSlots = [
    { 
      id: 'morning', 
      name: '8:00 AM - 11:00 AM', 
      label: 'Morning',
      value: '8:00 AM',
      available: true,
      slots: 4,
      urgency: 'Great availability',
      urgencyColor: 'text-green-600'
    },
    { 
      id: 'midday', 
      name: '11:00 AM - 2:00 PM', 
      label: 'Midday',
      value: '11:00 AM',
      available: true, 
      popular: true,
      slots: 2,
      urgency: 'Only 2 slots left!',
      urgencyColor: 'text-orange-600'
    },
    { 
      id: 'afternoon', 
      name: '2:00 PM - 5:00 PM', 
      label: 'Afternoon',
      value: '2:00 PM',
      available: true,
      slots: 3,
      urgency: 'Limited availability',
      urgencyColor: 'text-yellow-600'
    },
    { 
      id: 'evening', 
      name: '5:00 PM - 7:00 PM', 
      label: 'Evening',
      value: '5:00 PM',
      available: true,
      slots: 2,
      urgency: 'Perfect for after work',
      urgencyColor: 'text-blue-600'
    }
  ];

  const steps = [
    {
      title: 'Pool Details',
      subtitle: 'Tell us about your pool',
      icon: <Waves className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose frequency & timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Maintenance Services',
      subtitle: 'Chemical balancing & equipment',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ];

  const renderStep = () => {
    switch(currentStep) {
      case 0:
        return (
          <motion.div
            key="step0"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">What type of pool do you have?</h2>
              <p className="text-gray-600">Tell us about your pool</p>
            </div>

            {/* Property Type Selection */}
            <div className="mb-8">
              <div className="grid grid-cols-1 gap-4">
                {propertyTypes.map((type) => (
                  <motion.button
                    key={type.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setFormData({ ...formData, propertyType: type.id, propertySize: '' })}
                    className={`w-full p-6 rounded-2xl border-2 text-left transition-all ${
                      formData.propertyType === type.id
                        ? 'border-brand-500 bg-brand-50 shadow-md'
                        : 'border-gray-200 hover:border-gray-300 bg-white'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-xl ${
                        formData.propertyType === type.id ? 'bg-brand-100' : 'bg-gray-100'
                      }`}>
                        {type.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-900">{type.name}</h4>
                        <p className="text-sm text-gray-600">{type.description}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <Clock className="w-3 h-3" />
                            <span>{type.time}</span>
                          </div>
                          {type.badge && (
                            <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">
                              {type.badge}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Service Package Selection */}
            {formData.propertyType && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <label className="block text-sm font-semibold text-gray-700 mb-4">
                  Choose your service package
                </label>
                <div className="grid grid-cols-2 gap-4">
                  {getPropertySizes().map((size) => (
                    <motion.button
                      key={size.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => setFormData({ ...formData, propertySize: size.id })}
                      className={`relative p-6 rounded-2xl border-2 text-left transition-all ${
                        formData.propertySize === size.id
                          ? 'border-brand-500 bg-brand-50 shadow-md'
                          : 'border-gray-200 hover:border-gray-300 bg-white'
                      }`}
                    >
                      {size.popular && (
                        <span className="absolute -top-2 right-4 bg-warm-500 text-white text-xs px-2 py-0.5 rounded-full">
                          Most Popular
                        </span>
                      )}
                      <div className={`mb-3 ${
                        formData.propertySize === size.id ? 'text-brand-600' : 'text-gray-600'
                      }`}>
                        {getPropertySizeIcon(size.id)}
                      </div>
                      <h3 className="font-semibold text-gray-900">{size.name}</h3>
                      <p className="text-sm text-gray-500 mt-1">{size.description}</p>
                      <div className="mt-3 space-y-1">
                        <div className="flex items-center gap-2 text-xs text-gray-600">
                          <Clock className="w-3 h-3" />
                          <span>{size.time}</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs font-medium text-brand-600">
                          <DollarSign className="w-3 h-3" />
                          <span>{size.price}</span>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}
          </motion.div>
        );

      case 1:
        return (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Schedule Your Service</h2>
              <p className="text-gray-600">Choose your preferred date and time</p>
            </div>

            {/* Date Selection */}
            <div className="mb-8">
              <label className="block text-sm font-semibold text-gray-700 mb-4">
                When would you like us to start?
              </label>
              <div className="grid grid-cols-2 gap-3">
                {getCalendarDates().map((date) => (
                  <motion.button
                    key={date.value}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    disabled={!date.available}
                    className={`p-4 rounded-lg border-2 text-left transition-all ${
                      formData.preferredDate === date.value
                        ? 'border-brand-500 bg-brand-50'
                        : date.available
                        ? 'border-gray-200 hover:border-brand-300'
                        : 'border-gray-100 bg-gray-50 text-gray-400'
                    }`}
                    onClick={() => setFormData({ ...formData, preferredDate: date.value })}
                  >
                    <div className="font-semibold">{date.display}</div>
                    <div className="text-sm text-gray-500">
                      {date.available ? 'Available' : 'Fully Booked'}
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Time Selection */}
            {formData.preferredDate && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-8"
              >
                <label className="block text-sm font-semibold text-gray-700 mb-4">
                  What time works best?
                </label>
                <div className="grid grid-cols-1 gap-3">
                  {timeSlots.map((slot) => (
                    <motion.div
                      key={slot.value}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className="relative"
                    >
                      {slot.popular && (
                        <div className="absolute -top-2 -right-2 bg-brand-600 text-white px-2 py-1 rounded-full text-xs font-medium z-10">
                          Popular
                        </div>
                      )}
                      <button
                        disabled={!slot.available}
                        className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
                          formData.preferredTime === slot.value
                            ? 'border-brand-500 bg-brand-50'
                            : slot.available
                            ? 'border-gray-200 hover:border-brand-300'
                            : 'border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed'
                        } ${slot.popular ? 'ring-2 ring-brand-200' : ''}`}
                        onClick={() => slot.available && setFormData({ ...formData, preferredTime: slot.value })}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <div className="font-semibold text-gray-900">{slot.name}</div>
                            <div className="text-sm text-gray-500">{slot.label}</div>
                          </div>
                          <Clock className="w-5 h-5 text-gray-400" />
                        </div>
                        <div className={`text-sm font-medium ${slot.urgencyColor || 'text-gray-500'}`}>
                          {slot.urgency} • {slot.slots} slots available
                        </div>
                      </button>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Special Instructions */}
            {formData.preferredTime && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Any special instructions for your pool?
                </h3>
                <textarea
                  placeholder="Pool condition, access instructions, specific concerns..."
                  className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0 resize-none"
                  rows={4}
                  value={formData.specialInstructions}
                  onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })}
                />
              </motion.div>
            )}
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Additional Pool Services</h2>
              <p className="text-gray-600">Enhance your pool cleaning with professional maintenance services</p>
            </div>

            <div className="mb-8">
              <div className="space-y-4">
                {addOnServices.map((addon) => (
                  <motion.div
                    key={addon.id}
                    whileHover={{ scale: 1.02 }}
                    className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all ${
                      formData.addOns.includes(addon.id)
                        ? 'border-brand-500 bg-brand-50'
                        : 'border-gray-200 hover:border-brand-300'
                    }`}
                    onClick={() => {
                      const newAddOns = formData.addOns.includes(addon.id)
                        ? formData.addOns.filter(id => id !== addon.id)
                        : [...formData.addOns, addon.id];
                      setFormData({ ...formData, addOns: newAddOns });
                    }}
                  >
                    {addon.recommended && (
                      <div className="absolute -top-3 left-6 bg-brand-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        Recommended
                      </div>
                    )}
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-lg ${
                        formData.addOns.includes(addon.id) ? 'bg-brand-100 text-brand-600' : 'bg-gray-100'
                      }`}>
                        {addon.icon}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-semibold text-gray-900">{addon.name}</h4>
                          <span className="text-lg font-bold text-brand-600">+${addon.price}</span>
                        </div>
                        <p className="text-gray-600 mt-1">{addon.description}</p>
                      </div>
                      {formData.addOns.includes(addon.id) && (
                        <CheckCircle className="w-6 h-6 text-brand-600" />
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="bg-white rounded-2xl shadow-soft p-8"
          >
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Contact Information</h2>
              <p className="text-gray-600">Complete your booking</p>
            </div>

            <div className="mb-8">
              <div className="grid grid-cols-2 gap-4">
                <input
                  type="text"
                  placeholder="First Name"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.firstName}
                  onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="Last Name"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.lastName}
                  onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                />
                <input
                  type="email"
                  placeholder="Email Address"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0 col-span-2"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                />
                <input
                  type="tel"
                  placeholder="Phone Number"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0 col-span-2"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="Street Address"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0 col-span-2"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="City"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.city}
                  onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                />
                <input
                  type="text"
                  placeholder="ZIP Code"
                  className="p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                  value={formData.zipCode}
                  onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                />
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                How did you hear about us?
              </h3>
              <select
                className="w-full p-4 border-2 border-gray-200 rounded-lg focus:border-brand-500 focus:ring-0"
                value={formData.howDidYouHear}
                onChange={(e) => setFormData({ ...formData, howDidYouHear: e.target.value })}
              >
                <option value="">Choose one...</option>
                <option value="google">Google Search</option>
                <option value="facebook">Facebook</option>
                <option value="referral">Friend/Family Referral</option>
                <option value="nextdoor">Nextdoor</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="newsletter"
                className="w-5 h-5 text-brand-600 border-2 border-gray-300 rounded focus:ring-brand-500"
                checked={formData.newsletter}
                onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
              />
              <label htmlFor="newsletter" className="text-gray-600">
                Subscribe to pool care tips and special offers
              </label>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ scale: [1, 1.1, 1], rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Waves className="w-8 h-8 text-brand-600" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Crystal Clear Pool Service</h1>
                <p className="text-sm text-gray-600">Professional pool cleaning & maintenance for sparkling water</p>
              </div>
            </div>
            
            {/* Enhanced Trust badges */}
            <div className="hidden md:flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-brand-600" />
                <span className="text-sm font-medium">Licensed & Insured</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium">5-Star Rating</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium">1000+ Happy Pool Owners</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex items-center">
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: currentStep === index ? 1.1 : 1,
                      backgroundColor: currentStep >= index ? '#638907' : '#e5e7eb'
                    }}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white transition-all`}
                  >
                    {currentStep > index ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className={currentStep >= index ? 'text-white' : 'text-gray-600'}>
                        {index + 1}
                      </span>
                    )}
                  </motion.div>
                  <div className="hidden md:block ml-3">
                    <p className={`font-medium ${currentStep >= index ? 'text-brand-600' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500">{step.subtitle}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRight className={`mx-4 w-5 h-5 ${
                    currentStep > index ? 'text-brand-600' : 'text-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              {renderStep()}
            </AnimatePresence>
          </div>

          {/* Right Sidebar - Price Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-soft p-6 sticky top-32">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Summary</h3>
              
              {formData.propertyType && formData.propertySize ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">
                        {getPropertySizes().find(size => size.id === formData.propertySize)?.name}
                      </span>
                      <span className="font-bold text-brand-600">
                        ${getPropertySizes().find(size => size.id === formData.propertySize)?.basePrice}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {propertyTypes.find(type => type.id === formData.propertyType)?.name}
                    </div>
                  </div>

                  {formData.addOns.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-gray-900">Add-on Services:</h4>
                      {formData.addOns.map(addonId => {
                        const addon = addOnServices.find(a => a.id === addonId);
                        return addon ? (
                          <div key={addonId} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{addon.name}</span>
                            <span className="font-medium text-gray-900">+${addon.price}</span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  )}

                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold text-gray-900">Total</span>
                      <span className="text-2xl font-bold text-brand-600">${calculatePrice()}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center py-8 text-gray-500">
                    <Waves className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                    <p>Select pool type to see pricing</p>
                    <p className="text-sm">Starting from $79</p>
                  </div>
                </div>
              )}

              {/* Special Offer */}
              <div className="mt-6 p-4 bg-brand-50 rounded-lg border border-brand-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Heart className="w-5 h-5 text-brand-600" />
                  <span className="font-semibold text-brand-800">Summer Ready Special</span>
                </div>
                <div className="text-2xl font-bold text-brand-600 mb-1">30% OFF</div>
                <div className="text-sm text-brand-700 mb-2">
                  First month of weekly service + free water testing
                </div>
                <div className="text-xs text-brand-600 font-medium">
                  Pool season starting • Book early
                </div>
              </div>

              {/* Features */}
              <div className="mt-6 space-y-3">
                <h4 className="font-medium text-gray-900">What's Included:</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <Waves className="w-4 h-4 text-brand-600" />
                    <span>Professional skimming & vacuuming</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-brand-600" />
                    <span>Water chemistry testing</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Sparkles className="w-4 h-4 text-brand-600" />
                    <span>Equipment inspection</span>
                  </div>
                </div>
              </div>

              {/* Navigation Buttons */}
              <div className="mt-8 space-y-3">
                {currentStep === 3 ? (
                  <Button
                    onClick={handleSubmit}
                    disabled={!isStepValid(currentStep)}
                    className="w-full bg-brand-600 hover:bg-brand-700"
                  >
                    Complete Booking
                    <DollarSign className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={() => handleStepNavigation('next')}
                    disabled={!isStepValid(currentStep)}
                    className="w-full bg-brand-600 hover:bg-brand-700"
                  >
                    Continue
                    <ChevronRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
                
                {currentStep > 0 && (
                  <Button
                    variant="outline"
                    onClick={() => handleStepNavigation('prev')}
                    className="w-full"
                  >
                    <ChevronLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description={`Pool Cleaning - ${getPropertySizes().find(size => size.id === formData.propertySize)?.name || 'Custom'}`}
          customerEmail={formData.email}
          formData={{
            ...formData,
            serviceType: 'pool-cleaning'
          }}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedPoolForm; 
