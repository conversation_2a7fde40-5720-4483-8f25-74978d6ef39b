import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { useDeviceInfo } from '../../utils/deviceCompatibility';

interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  center?: boolean;
  animate?: boolean;
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  full: 'max-w-full'
};

const paddingClasses = {
  none: '',
  sm: 'px-4 py-2',
  md: 'px-6 py-4',
  lg: 'px-8 py-6',
  xl: 'px-12 py-8'
};

export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = '',
  maxWidth = 'full',
  padding = 'md',
  center = true,
  animate = false
}) => {
  const deviceInfo = useDeviceInfo();
  
  const containerClasses = [
    'w-full',
    maxWidthClasses[maxWidth],
    paddingClasses[padding],
    center ? 'mx-auto' : '',
    // Mobile-specific adjustments
    deviceInfo.isMobile ? 'px-4' : '',
    className
  ].filter(Boolean).join(' ');
  
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: deviceInfo.isMobile ? 0.2 : 0.3,
        ease: 'easeOut'
      }
    }
  };
  
  if (animate) {
    return (
      <motion.div
        className={containerClasses}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {children}
      </motion.div>
    );
  }
  
  return (
    <div className={containerClasses}>
      {children}
    </div>
  );
};

// Responsive Grid Component
interface ResponsiveGridProps {
  children: ReactNode;
  className?: string;
  cols?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
}

const gapClasses = {
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8'
};

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className = '',
  cols = { xs: 1, sm: 2, md: 3, lg: 4, xl: 4 },
  gap = 'md'
}) => {
  const deviceInfo = useDeviceInfo();
  
  const gridClasses = [
    'grid',
    gapClasses[gap],
    // Responsive columns
    cols.xs ? `grid-cols-${cols.xs}` : 'grid-cols-1',
    cols.sm ? `sm:grid-cols-${cols.sm}` : '',
    cols.md ? `md:grid-cols-${cols.md}` : '',
    cols.lg ? `lg:grid-cols-${cols.lg}` : '',
    cols.xl ? `xl:grid-cols-${cols.xl}` : '',
    // Mobile-specific adjustments
    deviceInfo.isMobile ? 'gap-3' : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
};

// Responsive Stack Component
interface ResponsiveStackProps {
  children: ReactNode;
  className?: string;
  direction?: 'vertical' | 'horizontal' | 'responsive';
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around';
}

const spacingClasses = {
  sm: 'space-y-2 space-x-2',
  md: 'space-y-4 space-x-4',
  lg: 'space-y-6 space-x-6',
  xl: 'space-y-8 space-x-8'
};

const alignClasses = {
  start: 'items-start',
  center: 'items-center',
  end: 'items-end',
  stretch: 'items-stretch'
};

const justifyClasses = {
  start: 'justify-start',
  center: 'justify-center',
  end: 'justify-end',
  between: 'justify-between',
  around: 'justify-around'
};

export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  className = '',
  direction = 'responsive',
  spacing = 'md',
  align = 'stretch',
  justify = 'start'
}) => {
  const deviceInfo = useDeviceInfo();
  
  const getDirectionClasses = () => {
    switch (direction) {
      case 'vertical':
        return 'flex flex-col';
      case 'horizontal':
        return 'flex flex-row';
      case 'responsive':
      default:
        return deviceInfo.isMobile ? 'flex flex-col' : 'flex flex-row';
    }
  };
  
  const stackClasses = [
    getDirectionClasses(),
    alignClasses[align],
    justifyClasses[justify],
    // Apply spacing based on direction
    direction === 'vertical' || (direction === 'responsive' && deviceInfo.isMobile)
      ? spacing === 'sm' ? 'space-y-2' : spacing === 'md' ? 'space-y-4' : spacing === 'lg' ? 'space-y-6' : 'space-y-8'
      : spacing === 'sm' ? 'space-x-2' : spacing === 'md' ? 'space-x-4' : spacing === 'lg' ? 'space-x-6' : 'space-x-8',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={stackClasses}>
      {children}
    </div>
  );
};

// Responsive Text Component
interface ResponsiveTextProps {
  children: ReactNode;
  className?: string;
  size?: {
    xs?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
    sm?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
    md?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
    lg?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl';
  };
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  align?: 'left' | 'center' | 'right' | 'justify';
  color?: 'primary' | 'secondary' | 'accent' | 'muted' | 'inherit';
}

const weightClasses = {
  light: 'font-light',
  normal: 'font-normal',
  medium: 'font-medium',
  semibold: 'font-semibold',
  bold: 'font-bold'
};

const alignClasses2 = {
  left: 'text-left',
  center: 'text-center',
  right: 'text-right',
  justify: 'text-justify'
};

const colorClasses = {
  primary: 'text-brand-600',
  secondary: 'text-gray-600',
  accent: 'text-accent-600',
  muted: 'text-gray-500',
  inherit: ''
};

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  className = '',
  size = { xs: 'base', sm: 'base', md: 'lg', lg: 'xl' },
  weight = 'normal',
  align = 'left',
  color = 'inherit'
}) => {
  const textClasses = [
    // Responsive text sizes
    size.xs ? `text-${size.xs}` : 'text-base',
    size.sm ? `sm:text-${size.sm}` : '',
    size.md ? `md:text-${size.md}` : '',
    size.lg ? `lg:text-${size.lg}` : '',
    weightClasses[weight],
    alignClasses2[align],
    colorClasses[color],
    className
  ].filter(Boolean).join(' ');
  
  return (
    <div className={textClasses}>
      {children}
    </div>
  );
};

// Responsive Button Component
interface ResponsiveButtonProps {
  children: ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  fullWidth?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

const buttonSizeClasses = {
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
  xl: 'px-8 py-4 text-xl'
};

const buttonVariantClasses = {
  primary: 'bg-brand-600 text-white hover:bg-brand-700 focus:ring-brand-500',
  secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
  outline: 'border-2 border-brand-600 text-brand-600 hover:bg-brand-50 focus:ring-brand-500',
  ghost: 'text-brand-600 hover:bg-brand-50 focus:ring-brand-500'
};

export const ResponsiveButton: React.FC<ResponsiveButtonProps> = ({
  children,
  className = '',
  size = 'md',
  variant = 'primary',
  fullWidth = false,
  onClick,
  disabled = false,
  type = 'button'
}) => {
  const deviceInfo = useDeviceInfo();
  
  const buttonClasses = [
    'inline-flex items-center justify-center',
    'rounded-lg font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    // Touch optimization for mobile
    deviceInfo.isTouchDevice ? 'min-h-[44px] min-w-[44px]' : '',
    buttonSizeClasses[size],
    buttonVariantClasses[variant],
    fullWidth ? 'w-full' : '',
    disabled ? 'pointer-events-none' : '',
    className
  ].filter(Boolean).join(' ');
  
  return (
    <button
      type={type}
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

export default {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveStack,
  ResponsiveText,
  ResponsiveButton
};
