import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, ThumbsUp } from 'lucide-react';

const steps = [
  {
    id: 1,
    title: 'Book Your Service',
    description: 'Enter your zip code and select your preferred cleaning service and time.',
    icon: CalendarCheck
  },
  {
    id: 2,
    title: 'Professional Cleaning',
    description: 'Our certified eco-friendly cleaners arrive and transform your space.',
    icon: Sparkles
  },
  {
    id: 3,
    title: 'Satisfaction Guaranteed',
    description: '100% satisfaction guaranteed or we'll make it right.',
    icon: ThumbsUp
  }
];

export function HowItWorks() {
  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-base text-green-600 font-semibold tracking-wide uppercase">How It Works</h2>
          <p className="mt-2 text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Simple, Efficient, Professional
          </p>
        </div>

        <div className="mt-16">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {steps.map((step) => {
              const Icon = step.icon;
              return (
                <div key={step.id} className="relative">
                  <div className="flex flex-col items-center">
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                      <Icon className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="mt-6 text-xl font-bold text-gray-900">{step.title}</h3>
                    <p className="mt-2 text-center text-gray-500">{step.description}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
