import React from 'react';
import { Form, FormField } from './FormFields';

interface PostConstructionFormProps {
  onSubmit: (data: any) => void;
}

export function PostConstructionForm({ onSubmit }: PostConstructionFormProps) {
  return (
    <Form onSubmit={onSubmit}>
      <FormField label="Approximate Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Project Type" required>
        <select
          name="projectType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="commercial">Commercial Build</option>
          <option value="renovation">Renovation</option>
          <option value="remodel">Remodel</option>
          <option value="addition">Addition</option>
        </select>
      </FormField>

      <FormField label="Service Phase">
        <select
          name="servicePhase"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
        >
          <option value="rough">Rough Clean</option>
          <option value="final">Final Clean</option>
          <option value="both">Both Phases</option>
        </select>
      </FormField>

      <FormField label="Debris Types">
        <div className="space-y-2">
          {['Drywall Dust', 'Paint Residue', 'Construction Debris', 'Adhesive/Caulk'].map((type) => (
            <label key={type} className="flex items-center">
              <input
                type="checkbox"
                name="debrisTypes"
                value={type.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{type}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Additional Notes">
        <textarea
          name="notes"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any special requirements or access restrictions?"
        />
      </FormField>
    </Form>
  );
}
