import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, User, CheckCircle, ArrowRight, Truck, Key, Package, Star, Calendar
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';

interface FormData {
  propertyType: string;
  propertySize: string;
  bedrooms: string;
  bathrooms: string;
  moveType: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
}

const SimplifiedMoveOutForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  
  const [formData, setFormData] = useState<FormData>({
    propertyType: '',
    propertySize: '',
    bedrooms: '',
    bathrooms: '',
    moveType: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: ''
  });

  const calculatePrice = (): number => {
    const basePrice = formData.propertySize === 'small' ? 150 : 
                     formData.propertySize === 'medium' ? 220 :
                     formData.propertySize === 'large' ? 300 : 400;
    const moveTypeMultiplier = formData.moveType === 'both' ? 1.8 : 1;
    return Math.round(basePrice * moveTypeMultiplier);
  };

  const handleFieldChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.propertySize && formData.bedrooms && formData.bathrooms);
      case 2:
        return !!(formData.moveType);
      case 3:
        return !!(formData.firstName && formData.lastName && formData.email && formData.phone && 
                 formData.address && formData.city && formData.zipCode && formData.preferredDate && formData.preferredTime);
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (currentStep < 3 && isStepValid(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    if (!user) {
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setShowPaymentModal(true);
  };

  const handlePaymentComplete = () => {
    setShowPaymentModal(false);
    navigate('/thank-you', { 
      state: { 
        formData: {
          ...formData,
          totalPrice: calculatePrice(),
          bookingId: Date.now().toString(),
          confirmationNumber: `SMOC-${Date.now()}`,
          emailSent: true
        },
        paymentStatus: 'paid',
        serviceType: formData.moveType === 'move-out' ? 'Move-Out Cleaning' : 
                    formData.moveType === 'move-in' ? 'Move-In Cleaning' : 
                    'Move-Out + Move-In Package',
        bookingDetails: {
          id: Date.now().toString(),
          type: formData.moveType === 'move-out' ? 'Move-Out Cleaning' : 
                formData.moveType === 'move-in' ? 'Move-In Cleaning' : 
                'Move-Out + Move-In Package',
          serviceType: 'Move-Out/In Cleaning',
          status: 'confirmed'
        }
      }
    });
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div className="space-y-6">
            <h2 className="text-2xl font-bold text-white text-center mb-6">Property Details</h2>
            
            <div className="space-y-4">
              <label className="block text-white font-semibold">Property Type</label>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { id: 'apartment', name: 'Apartment/Condo' },
                  { id: 'house', name: 'House' },
                  { id: 'townhouse', name: 'Townhouse' },
                  { id: 'large-home', name: 'Large Home' }
                ].map(type => (
                  <button
                    key={type.id}
                    onClick={() => handleFieldChange('propertyType', type.id)}
                    className={`p-3 rounded-lg border-2 text-white ${
                      formData.propertyType === type.id ? 'bg-green-500/20 border-green-400' : 'bg-white/5 border-white/20'
                    }`}
                  >
                    {type.name}
                  </button>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <label className="block text-white font-semibold">Property Size</label>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { id: 'small', name: 'Small (< 1,200 sq ft)' },
                  { id: 'medium', name: 'Medium (1,200-2,000 sq ft)' },
                  { id: 'large', name: 'Large (2,000-3,000 sq ft)' },
                  { id: 'xl', name: 'Extra Large (3,000+ sq ft)' }
                ].map(size => (
                  <button
                    key={size.id}
                    onClick={() => handleFieldChange('propertySize', size.id)}
                    className={`p-3 rounded-lg border-2 text-white ${
                      formData.propertySize === size.id ? 'bg-green-500/20 border-green-400' : 'bg-white/5 border-white/20'
                    }`}
                  >
                    {size.name}
                  </button>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-semibold mb-2">Bedrooms</label>
                <select
                  value={formData.bedrooms}
                  onChange={(e) => handleFieldChange('bedrooms', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                >
                  <option value="">Select</option>
                  <option value="0">Studio</option>
                  <option value="1">1 Bedroom</option>
                  <option value="2">2 Bedrooms</option>
                  <option value="3">3 Bedrooms</option>
                  <option value="4">4 Bedrooms</option>
                  <option value="5+">5+ Bedrooms</option>
                </select>
              </div>
              
              <div>
                <label className="block text-white font-semibold mb-2">Bathrooms</label>
                <select
                  value={formData.bathrooms}
                  onChange={(e) => handleFieldChange('bathrooms', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                >
                  <option value="">Select</option>
                  <option value="1">1 Bathroom</option>
                  <option value="1.5">1.5 Bathrooms</option>
                  <option value="2">2 Bathrooms</option>
                  <option value="2.5">2.5 Bathrooms</option>
                  <option value="3">3 Bathrooms</option>
                  <option value="4+">4+ Bathrooms</option>
                </select>
              </div>
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div className="space-y-6">
            <h2 className="text-2xl font-bold text-white text-center mb-6">Move Type</h2>
            
            <div className="space-y-4">
              {[
                { id: 'move-out', name: 'Move-Out Cleaning', desc: 'Deep clean for deposit return', icon: Truck },
                { id: 'move-in', name: 'Move-In Cleaning', desc: 'Sanitization for new occupancy', icon: Key },
                { id: 'both', name: 'Move-Out + Move-In Package', desc: 'Complete transition service', icon: Package }
              ].map(type => {
                const IconComponent = type.icon;
                return (
                  <button
                    key={type.id}
                    onClick={() => handleFieldChange('moveType', type.id)}
                    className={`w-full p-6 rounded-lg border-2 text-left ${
                      formData.moveType === type.id ? 'bg-green-500/20 border-green-400' : 'bg-white/5 border-white/20'
                    }`}
                  >
                    <div className="flex items-center gap-4">
                      <IconComponent className="w-8 h-8 text-green-400" />
                      <div>
                        <h3 className="text-white font-semibold">{type.name}</h3>
                        <p className="text-white/70">{type.desc}</p>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div className="space-y-6">
            <h2 className="text-2xl font-bold text-white text-center mb-6">Contact & Schedule</h2>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-semibold mb-2">First Name</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleFieldChange('firstName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  placeholder="John"
                />
              </div>
              
              <div>
                <label className="block text-white font-semibold mb-2">Last Name</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleFieldChange('lastName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  placeholder="Doe"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-semibold mb-2">Email</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleFieldChange('email', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="block text-white font-semibold mb-2">Phone</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleFieldChange('phone', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  placeholder="(*************"
                />
              </div>
            </div>

            <div>
              <label className="block text-white font-semibold mb-2">Address</label>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => handleFieldChange('address', e.target.value)}
                className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                placeholder="123 Main Street"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-semibold mb-2">City</label>
                <input
                  type="text"
                  value={formData.city}
                  onChange={(e) => handleFieldChange('city', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  placeholder="Anytown"
                />
              </div>
              
              <div>
                <label className="block text-white font-semibold mb-2">ZIP Code</label>
                <input
                  type="text"
                  value={formData.zipCode}
                  onChange={(e) => handleFieldChange('zipCode', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  placeholder="12345"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-semibold mb-2">Preferred Date</label>
                <input
                  type="date"
                  value={formData.preferredDate}
                  onChange={(e) => handleFieldChange('preferredDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                />
              </div>
              
              <div>
                <label className="block text-white font-semibold mb-2">Preferred Time</label>
                <select
                  value={formData.preferredTime}
                  onChange={(e) => handleFieldChange('preferredTime', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                >
                  <option value="">Select Time</option>
                  <option value="morning">Morning (8AM - 12PM)</option>
                  <option value="afternoon">Afternoon (1PM - 5PM)</option>
                  <option value="evening">Evening (5PM - 9PM)</option>
                  <option value="flexible">Flexible (Any Time)</option>
                </select>
              </div>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4">
        <div className="w-full max-w-3xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-2 flex items-center justify-center gap-3">
              <Truck className="w-8 h-8 text-green-400" />
              Move-Out/In Cleaning
            </h1>
            <p className="text-gray-200">Professional deep cleaning for your move</p>
          </motion.div>

          {/* Progress */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    currentStep >= step ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'
                  }`}>
                    {currentStep > step ? <CheckCircle size={16} /> : step}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step === 1 ? 'Property' : step === 2 ? 'Move Type' : 'Contact'}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div 
                className="bg-green-500 h-full rounded-full" 
                animate={{ width: `${(currentStep / 3) * 100}%` }} 
              />
            </div>
          </div>

          {/* Price Display */}
          {formData.propertySize && formData.moveType && (
            <motion.div 
              initial={{ opacity: 0 }} 
              animate={{ opacity: 1 }} 
              className="mb-8 p-6 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl border border-green-400/30"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-white font-semibold">Estimated Total</h3>
                  <p className="text-white/70 text-sm">
                    {formData.moveType === 'move-out' ? 'Move-Out Cleaning' :
                     formData.moveType === 'move-in' ? 'Move-In Cleaning' :
                     'Move-Out + Move-In Package'}
                  </p>
                </div>
                <div className="text-3xl font-bold text-green-400">${calculatePrice()}</div>
              </div>
            </motion.div>
          )}

          {/* Form Content */}
          <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 mb-8">
            {renderStep()}
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <Button
              variant="secondary"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center gap-2"
            >
              <ArrowRight className="w-4 h-4 rotate-180" />
              Previous
            </Button>

            <div className="text-center">
              <p className="text-white/60 text-sm">Step {currentStep} of 3</p>
            </div>

            {currentStep < 3 ? (
              <Button
                onClick={nextStep}
                disabled={!isStepValid(currentStep)}
                className="flex items-center gap-2"
              >
                Next
                <ArrowRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={!isStepValid(currentStep) || isSubmitting}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                    Processing...
                  </>
                ) : (
                  <>
                    Book Now - ${calculatePrice()}
                    <Star className="w-4 h-4" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculatePrice()}
        description={
          formData.moveType === 'move-out' ? 'Move-Out Cleaning Service' :
          formData.moveType === 'move-in' ? 'Move-In Cleaning Service' :
          'Move-Out + Move-In Cleaning Package'
        }
        customerEmail={formData.email}
        formData={{
          serviceType: 'residential_move',
          cleaningType: 'move',
          frequency: 'one-time',
          propertyType: formData.propertyType,
          propertySize: formData.propertySize,
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode,
          moveType: formData.moveType,
          bedrooms: formData.bedrooms,
          bathrooms: formData.bathrooms,
          totalPrice: calculatePrice(),
          submittedAt: new Date().toISOString()
        }}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default SimplifiedMoveOutForm; 
