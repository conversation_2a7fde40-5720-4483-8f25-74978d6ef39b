import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, User, CheckCircle, ArrowRight,
  Building2, Castle, Warehouse, Heart, Gift, Zap, Users,
  MoveRight, Package, Shield, Star,
  Key, Truck, Calendar
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';

interface FormData {
  propertyType: string;
  propertySize: string;
  bedrooms: string;
  bathrooms: string;
  moveType: string;
  depositGuarantee: boolean;
  specialRequests: string[];
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  landlordRequirements: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const ModernMoveOutCleaningForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    propertySize: '',
    bedrooms: '',
    bathrooms: '',
    moveType: '',
    depositGuarantee: false,
    specialRequests: [],
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    landlordRequirements: ''
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('moveOutCleaningFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('moveOutCleaningFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property Details' },
    { id: 2, name: 'Move Type & Services' },
    { id: 3, name: 'Add-ons & Guarantees' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Studio to 3BR units',
      multiplier: 1.0
    },
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: '1-2 story homes',
      multiplier: 1.2
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Castle, 
      description: 'Multi-level attached',
      multiplier: 1.15
    },
    { 
      id: 'large-home', 
      name: 'Large Home/Estate', 
      icon: Warehouse, 
      description: '3+ stories, 4+ bedrooms',
      multiplier: 1.5
    }
  ];

  const propertySizes = [
    { id: 'small', name: 'Small (< 1,200 sq ft)', basePrice: 150, multiplier: 0.8 },
    { id: 'medium', name: 'Medium (1,200 - 2,000 sq ft)', basePrice: 220, multiplier: 1.0 },
    { id: 'large', name: 'Large (2,000 - 3,000 sq ft)', basePrice: 300, multiplier: 1.3 },
    { id: 'xl', name: 'Extra Large (3,000+ sq ft)', basePrice: 400, multiplier: 1.7 }
  ];

  const moveTypes = [
    { 
      id: 'move-out', 
      name: 'Move-Out Cleaning', 
      price: 0, 
      description: 'Deep clean for security deposit return',
      features: ['Complete deep clean', 'Inside appliances', 'Cabinet interiors', 'Baseboards & trim', 'Light fixtures', 'Switch plates'],
      icon: Truck
    },
    { 
      id: 'move-in', 
      name: 'Move-In Cleaning', 
      price: 0, 
      description: 'Thorough sanitization for new occupancy',
      features: ['Sanitization focus', 'Cabinet cleaning', 'Appliance interiors', 'Bathroom deep clean', 'Floor restoration', 'Fresh start guarantee'],
      icon: Key
    },
    { 
      id: 'both', 
      name: 'Move-Out + Move-In Package', 
      price: 50, 
      description: 'Complete transition service',
      features: ['Both properties cleaned', 'Scheduling coordination', 'Priority booking', 'Package discount', 'Satisfaction guarantee', 'Free re-clean if needed'],
      icon: Package
    }
  ];

  const addOnServices = [
    { id: 'inside-oven', name: 'Deep Oven Cleaning', price: 35, icon: Zap, description: 'Professional oven degreasing' },
    { id: 'inside-fridge', name: 'Refrigerator Deep Clean', price: 30, icon: Gift, description: 'Complete fridge sanitization' },
    { id: 'inside-cabinets', name: 'Cabinet Interior Cleaning', price: 40, icon: CheckCircle, description: 'All cabinet shelves and drawers' },
    { id: 'garage-cleaning', name: 'Garage Cleaning', price: 60, icon: Home, description: 'Sweep, organize, and clean garage' },
    { id: 'basement-cleaning', name: 'Basement/Storage Areas', price: 50, icon: Building2, description: 'Storage room deep clean' },
    { id: 'window-cleaning', name: 'Interior Window Cleaning', price: 45, icon: Sparkles, description: 'All accessible windows' },
    { id: 'carpet-cleaning', name: 'Carpet Deep Clean', price: 80, icon: Heart, description: 'Professional carpet cleaning' },
    { id: 'pressure-washing', name: 'Patio/Deck Pressure Wash', price: 70, icon: Users, description: 'Outdoor surface cleaning' }
  ];

  const specialRequests = [
    { id: 'eco-friendly', name: 'Eco-Friendly Products Only', description: 'Green cleaning solutions' },
    { id: 'pet-safe', name: 'Pet-Safe Products', description: 'Safe for pets and animals' },
    { id: 'heavy-duty', name: 'Heavy-Duty Deep Clean', description: 'Extra intensive cleaning' },
    { id: 'same-day', name: 'Same-Day Service', description: 'Urgent cleaning needs' },
    { id: 'weekend-only', name: 'Weekend Only', description: 'Saturday/Sunday availability' },
    { id: 'early-morning', name: 'Early Morning (6-8 AM)', description: 'Before business hours' }
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)', popular: true },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)', popular: false },
    { id: 'evening', name: 'Evening (5PM - 9PM)', popular: false },
    { id: 'flexible', name: 'Flexible (Any Time)', popular: true }
  ];

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName':
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      case 'email':
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      case 'phone':
        return !validatePhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      case 'address':
        return value.length < 5 ? 'Please enter a complete address' : '';
      case 'city':
        return value.length < 2 ? 'Please enter a valid city' : '';
      case 'zipCode':
        return !validateZipCode(value) ? 'Please enter a valid ZIP code (12345 or 12345-6789)' : '';
      default:
        return '';
    }
  };

  // Step validation
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.propertySize && formData.bedrooms && formData.bathrooms);
      case 2:
        return !!(formData.moveType);
      case 3:
        return true; // Add-ons and guarantees are optional
      case 4: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode', 'preferredDate', 'preferredTime'];
        return requiredFields.every(field => {
          const value = formData[field as keyof FormData] as string;
          return value && !validateField(field, value);
        });
      }
      default:
        return false;
    }
  };

  const calculateTotalPrice = (): number => {
    try {
      // Determine service type
      const serviceType = 'residential_move';

      // Convert property size to square footage estimate
      const sizeToSqft: Record<string, number> = {
        'small': 800,
        'medium': 1200,
        'large': 1800,
        'xl': 2500
      };

      // Prepare pricing input
      const pricingInput: PricingInput = {
        serviceType: serviceType,
        propertySize: sizeToSqft[formData.propertySize || 'medium'] || 1200,
        frequency: 'onetime', // Move-out is always one-time
        addOns: formData.addOns || [],
        customOptions: {
          bedrooms: parseInt(formData.bedrooms || '0') || 0,
          bathrooms: parseInt(formData.bathrooms || '0') || 0,
          propertyType: formData.propertyType || 'house',
          moveType: formData.moveType || 'move-out'
        }
      };

      // Calculate price using centralized service
      const pricingResult = calculatePrice(pricingInput);
      return Math.round(pricingResult.total);
    } catch (error) {
      console.error('Error calculating price:', error);
      return 299; // Fallback price
    }
  };

  const handleFieldChange = (field: string, value: string | string[] | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle form submission - now shows payment modal
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      // Prompt user to login
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Determine correct service type
      const serviceType = 'residential_move';

      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString(),
        cleaningType: 'move', // Explicitly set for move services
        frequency: 'one-time' // Move services are always one-time
      });

      // Save to localStorage for persistence
      localStorage.setItem('residentialBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      // Clear localStorage since booking is now complete
      localStorage.removeItem('residentialBookingData');
      localStorage.removeItem('moveOutCleaningFormData');
      
      // Navigate to Thank You page with booking data
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: Date.now().toString(),
            confirmationNumber: `MOC-${Date.now()}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: formData.moveType === 'move-out' ? 'Move-Out Cleaning' : 
                      formData.moveType === 'move-in' ? 'Move-In Cleaning' : 
                      'Move-Out + Move-In Package',
          bookingDetails: {
            id: Date.now().toString(),
            type: formData.moveType === 'move-out' ? 'Move-Out Cleaning' : 
                  formData.moveType === 'move-in' ? 'Move-In Cleaning' : 
                  'Move-Out + Move-In Package',
            serviceType: 'Move-Out/In Cleaning',
            status: 'confirmed',
            message: `Your ${formData.moveType === 'move-out' ? 'move-out' : 
                                formData.moveType === 'move-in' ? 'move-in' : 
                                'move-out and move-in'} cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div key="step1" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Property Details</h2>
              <p className="text-white/70">Tell us about the property you're moving from/to</p>
            </div>
            
            {/* Property Type */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-white mb-4">Property Type</label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {propertyTypes.map((type) => {
                  const IconComponent = type.icon;
                  return (
                    <motion.button
                      key={type.id}
                      onClick={() => handleFieldChange('propertyType', type.id)}
                      className={`p-4 rounded-xl border-2 text-left ${
                        formData.propertyType === type.id 
                          ? 'bg-green-500/20 border-green-400' 
                          : 'bg-white/5 border-white/20'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-center gap-4">
                        <div className="text-green-300">
                          <IconComponent className="w-8 h-8" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">{type.name}</h3>
                          <p className="text-sm text-gray-300">{type.description}</p>
                        </div>
                      </div>
                    </motion.button>
                  );
                })}
              </div>
            </div>

            {/* Property Size */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-white mb-4">Property Size</label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {propertySizes.map((size) => (
                  <motion.button
                    key={size.id}
                    onClick={() => handleFieldChange('propertySize', size.id)}
                    className={`p-4 rounded-xl border-2 text-left ${
                      formData.propertySize === size.id 
                        ? 'bg-green-500/20 border-green-400' 
                        : 'bg-white/5 border-white/20'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <h3 className="font-semibold text-white">{size.name}</h3>
                    <p className="text-green-400 font-bold">Starting at ${size.basePrice}</p>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Bedrooms and Bathrooms */}
            <div className="grid grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-semibold text-white mb-4">Bedrooms</label>
                                 <GlassmorphismSelect
                   value={formData.bedrooms || ''}
                   onChange={(value) => handleFieldChange('bedrooms', value)}
                   options={[
                     { id: '', name: 'Select' },
                     { id: '0', name: 'Studio' },
                     { id: '1', name: '1 Bedroom' },
                     { id: '2', name: '2 Bedrooms' },
                     { id: '3', name: '3 Bedrooms' },
                     { id: '4', name: '4 Bedrooms' },
                     { id: '5+', name: '5+ Bedrooms' }
                   ]}
                 />
              </div>

              <div>
                <label className="block text-sm font-semibold text-white mb-4">Bathrooms</label>
                                 <GlassmorphismSelect
                   value={formData.bathrooms || ''}
                   onChange={(value) => handleFieldChange('bathrooms', value)}
                   options={[
                     { id: '', name: 'Select' },
                     { id: '1', name: '1 Bathroom' },
                     { id: '1.5', name: '1.5 Bathrooms' },
                     { id: '2', name: '2 Bathrooms' },
                     { id: '2.5', name: '2.5 Bathrooms' },
                     { id: '3', name: '3 Bathrooms' },
                     { id: '3.5', name: '3.5 Bathrooms' },
                     { id: '4+', name: '4+ Bathrooms' }
                   ]}
                 />
              </div>
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Move Type & Services</h2>
              <p className="text-white/70">What type of move cleaning do you need?</p>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-semibold text-white mb-4">Choose Your Service</label>
              <div className="space-y-4">
                {moveTypes.map((type) => {
                  const IconComponent = type.icon;
                  return (
                    <motion.button
                      key={type.id}
                      onClick={() => handleFieldChange('moveType', type.id)}
                      className={`w-full p-6 rounded-xl border-2 text-left ${
                        formData.moveType === type.id 
                          ? 'bg-green-500/20 border-green-400' 
                          : 'bg-white/5 border-white/20'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center gap-4">
                          <div className="text-green-300">
                            <IconComponent className="w-8 h-8" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-white text-lg">{type.name}</h3>
                            <p className="text-sm text-gray-300">{type.description}</p>
                          </div>
                        </div>
                        {type.price > 0 && (
                          <span className="text-green-400 font-bold">+${type.price}</span>
                        )}
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        {type.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2 text-xs text-gray-300">
                            <CheckCircle className="w-3 h-3 text-green-400" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </motion.button>
                  );
                })}
              </div>
            </div>

            {/* Special Requests */}
            <div className="mb-6">
              <label className="block text-sm font-semibold text-white mb-4">Special Requests (Optional)</label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {specialRequests.map((request) => (
                  <motion.label
                    key={request.id}
                    className={`flex items-start gap-3 p-3 rounded-lg border cursor-pointer ${
                      formData.specialRequests?.includes(request.id)
                        ? 'bg-green-500/20 border-green-400' 
                        : 'bg-white/5 border-white/20'
                    }`}
                    whileHover={{ scale: 1.02 }}
                  >
                    <input
                      type="checkbox"
                      checked={formData.specialRequests?.includes(request.id) || false}
                      onChange={(e) => {
                        const current = formData.specialRequests || [];
                        if (e.target.checked) {
                          handleFieldChange('specialRequests', [...current, request.id]);
                        } else {
                          handleFieldChange('specialRequests', current.filter(id => id !== request.id));
                        }
                      }}
                      className="mt-1 text-green-500 bg-transparent border-white/20 rounded focus:ring-green-500"
                    />
                    <div>
                      <div className="text-white font-medium text-sm">{request.name}</div>
                      <div className="text-white/60 text-xs">{request.description}</div>
                    </div>
                  </motion.label>
                ))}
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div key="step3" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Add-ons & Guarantees</h2>
              <p className="text-white/70">Customize your cleaning service</p>
            </div>

            {/* Add-on Services */}
            <div className="mb-8">
              <label className="block text-sm font-semibold text-white mb-4">Additional Services</label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {addOnServices.map((addon) => {
                  const IconComponent = addon.icon;
                  return (
                    <motion.label
                      key={addon.id}
                      className={`flex items-center gap-4 p-4 rounded-xl border cursor-pointer ${
                        formData.addOns?.includes(addon.id)
                          ? 'bg-green-500/20 border-green-400' 
                          : 'bg-white/5 border-white/20'
                      }`}
                      whileHover={{ scale: 1.02 }}
                    >
                      <input
                        type="checkbox"
                        checked={formData.addOns?.includes(addon.id) || false}
                        onChange={(e) => {
                          const current = formData.addOns || [];
                          if (e.target.checked) {
                            handleFieldChange('addOns', [...current, addon.id]);
                          } else {
                            handleFieldChange('addOns', current.filter(id => id !== addon.id));
                          }
                        }}
                        className="text-green-500 bg-transparent border-white/20 rounded focus:ring-green-500"
                      />
                      <div className="text-green-300">
                        <IconComponent className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-center">
                          <h3 className="font-semibold text-white">{addon.name}</h3>
                          <span className="text-green-400 font-bold">+${addon.price}</span>
                        </div>
                        <p className="text-sm text-gray-300">{addon.description}</p>
                      </div>
                    </motion.label>
                  );
                })}
              </div>
            </div>

            {/* Security Deposit Guarantee */}
            <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 p-6 rounded-xl border border-green-400/30">
              <div className="flex items-start gap-4">
                <Shield className="w-8 h-8 text-green-400 mt-1" />
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white mb-2">Security Deposit Guarantee</h3>
                  <p className="text-white/80 mb-4">
                    We guarantee your cleaning will meet or exceed landlord requirements for deposit return. 
                    If your landlord isn't satisfied, we'll return to re-clean at no charge.
                  </p>
                  <motion.label
                    className="flex items-center gap-3 cursor-pointer"
                    whileHover={{ scale: 1.02 }}
                  >
                    <input
                      type="checkbox"
                      checked={formData.depositGuarantee || false}
                      onChange={(e) => handleFieldChange('depositGuarantee', e.target.checked)}
                      className="text-green-500 bg-transparent border-white/20 rounded focus:ring-green-500 w-5 h-5"
                    />
                    <span className="text-white font-medium">
                      Include Security Deposit Guarantee <span className="text-green-400">(FREE)</span>
                    </span>
                  </motion.label>
                </div>
              </div>
            </div>

            {/* Landlord Requirements */}
            <div>
              <label className="block text-sm font-semibold text-white mb-4">
                Landlord/Property Manager Requirements (Optional)
              </label>
              <textarea
                value={formData.landlordRequirements || ''}
                onChange={(e) => handleFieldChange('landlordRequirements', e.target.value)}
                placeholder="Share any specific requirements from your landlord or property manager for cleaning..."
                className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20 resize-none"
                rows={4}
              />
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div key="step4" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Contact & Schedule</h2>
              <p className="text-white/70">Almost done! Just need your details and preferred timing</p>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <User className="w-5 h-5" />
                Contact Information
              </h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-white mb-2">First Name</label>
                  <input
                    type="text"
                    value={formData.firstName || ''}
                    onChange={(e) => handleFieldChange('firstName', e.target.value)}
                    className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                    placeholder="John"
                  />
                  {validationErrors.firstName && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.firstName}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-white mb-2">Last Name</label>
                  <input
                    type="text"
                    value={formData.lastName || ''}
                    onChange={(e) => handleFieldChange('lastName', e.target.value)}
                    className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                    placeholder="Doe"
                  />
                  {validationErrors.lastName && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.lastName}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-white mb-2">Email</label>
                  <input
                    type="email"
                    value={formData.email || ''}
                    onChange={(e) => handleFieldChange('email', e.target.value)}
                    className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                    placeholder="<EMAIL>"
                  />
                  {validationErrors.email && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-white mb-2">Phone</label>
                  <input
                    type="tel"
                    value={formData.phone || ''}
                    onChange={(e) => handleFieldChange('phone', e.target.value)}
                    className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                    placeholder="(*************"
                  />
                  {validationErrors.phone && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-white mb-2">Property Address</label>
                <input
                  type="text"
                  value={formData.address || ''}
                  onChange={(e) => handleFieldChange('address', e.target.value)}
                  className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                  placeholder="123 Main Street"
                />
                {validationErrors.address && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-white mb-2">City</label>
                  <input
                    type="text"
                    value={formData.city || ''}
                    onChange={(e) => handleFieldChange('city', e.target.value)}
                    className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                    placeholder="Anytown"
                  />
                  {validationErrors.city && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-white mb-2">ZIP Code</label>
                  <input
                    type="text"
                    value={formData.zipCode || ''}
                    onChange={(e) => handleFieldChange('zipCode', e.target.value)}
                    className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                    placeholder="12345"
                  />
                  {validationErrors.zipCode && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Schedule Information */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Schedule Your Service
              </h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-white mb-2">Preferred Date</label>
                  <input
                    type="date"
                    value={formData.preferredDate || ''}
                    onChange={(e) => handleFieldChange('preferredDate', e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full p-3 rounded-xl bg-white/10 border border-white/20 text-white focus:border-green-400 focus:ring-2 focus:ring-green-400/20"
                  />
                  {validationErrors.preferredDate && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.preferredDate}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-semibold text-white mb-2">Preferred Time</label>
                  <GlassmorphismSelect
                    value={formData.preferredTime || ''}
                    onChange={(value) => handleFieldChange('preferredTime', value)}
                    options={[
                      { id: '', name: 'Select Time' },
                      ...timeSlots.map(slot => ({
                        id: slot.id,
                        name: slot.name + (slot.popular ? ' ⭐' : '')
                      }))
                    ]}
                  />
                  {validationErrors.preferredTime && (
                    <p className="text-red-400 text-sm mt-1">{validationErrors.preferredTime}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Special Instructions */}
            <div>
              <label className="block text-sm font-semibold text-white mb-4">
                Special Instructions (Optional)
              </label>
              <textarea
                value={formData.specialInstructions || ''}
                onChange={(e) => handleFieldChange('specialInstructions', e.target.value)}
                placeholder="Any special instructions, access codes, parking information, or other details our team should know..."
                className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:ring-2 focus:ring-green-400/20 resize-none"
                rows={4}
              />
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2 flex items-center justify-center gap-3">
              <MoveRight className="w-8 h-8 text-green-400" />
              Move-Out/In Cleaning
            </h1>
            <p className="text-gray-200">Professional deep cleaning for your move - get your deposit back guaranteed!</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= step.id ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-green-500 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          {/* Price Display */}
          {currentStep > 1 && (
            <motion.div 
              initial={{ opacity: 0, y: -10 }} 
              animate={{ opacity: 1, y: 0 }} 
              className="mb-8 p-6 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl border border-green-400/30"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-white font-semibold">Estimated Total</h3>
                  <p className="text-white/70 text-sm">
                    {formData.moveType === 'move-out' ? 'Move-Out Cleaning' :
                     formData.moveType === 'move-in' ? 'Move-In Cleaning' :
                     formData.moveType === 'both' ? 'Move-Out + Move-In Package' : 'Move Cleaning Service'}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-green-400">${calculateTotalPrice()}</div>
                  {formData.depositGuarantee && (
                    <div className="flex items-center gap-1 text-sm text-white/80">
                      <Shield className="w-4 h-4 text-green-400" />
                      Deposit Guarantee Included
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )}

          <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 mb-8">
            <AnimatePresence mode="wait">
              {renderStep()}
            </AnimatePresence>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <Button
              variant="secondary"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center gap-2"
            >
              <ArrowRight className="w-4 h-4 rotate-180" />
              Previous
            </Button>

            <div className="text-center">
              <p className="text-white/60 text-sm">
                Step {currentStep} of {steps.length}
              </p>
            </div>

            {currentStep < steps.length ? (
              <Button
                onClick={nextStep}
                disabled={!isStepValid(currentStep)}
                className="flex items-center gap-2"
              >
                Next
                <ArrowRight className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={!isStepValid(currentStep) || isSubmitting}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                    Processing...
                  </>
                ) : (
                  <>
                    Book Now - ${calculateTotalPrice()}
                    <Star className="w-4 h-4" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description={
          formData.moveType === 'move-out' ? 'Move-Out Cleaning Service' :
          formData.moveType === 'move-in' ? 'Move-In Cleaning Service' :
          formData.moveType === 'both' ? 'Move-Out + Move-In Cleaning Package' :
          'Move Cleaning Service'
        }
        customerEmail={formData.email || ''}
        formData={{
          // 👉 everything at the top-level so BookingService can find it
          serviceType: 'residential_move',
          cleaningType: 'move',
          frequency: 'one-time',
          propertyType: formData.propertyType,      // ✅ required
          propertySize: formData.propertySize,      // ✅ required
          preferredDate: formData.preferredDate,    // ✅ required
          preferredTime: formData.preferredTime,    // ✅ required
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode,
          moveType: formData.moveType || 'move-out',
          depositGuarantee: !!formData.depositGuarantee,
          addOns: formData.addOns,
          specialRequests: formData.specialRequests,
          totalPrice: calculateTotalPrice(),
          submittedAt: new Date().toISOString()
        }}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default ModernMoveOutCleaningForm; 
