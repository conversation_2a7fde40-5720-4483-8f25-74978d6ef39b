import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { BookingForm } from '../components/BookingForm';
import { ModernPaymentModal } from '../components/ModernPaymentModal';
import { BookingService } from '../lib/api/bookingService';
import { StandardizedBookingData } from '../lib/api/bookingService';

export function BookingPage() {
  const { user } = useAuth();
  const [bookingData, setBookingData] = useState<StandardizedBookingData | null>(null);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [totalPrice, setTotalPrice] = useState(0);

  const handleBookingSubmit = async (formData: any) => {
    try {
      // Calculate price based on form data
      const price = calculatePrice(formData);
      setTotalPrice(price);
      
      // Save booking data
      const booking = await BookingService.saveBooking(formData, formData.serviceType, user);
      setBookingData(booking);
      
      // Open payment modal
      setIsPaymentModalOpen(true);
    } catch (error) {
      console.error('Error creating booking:', error);
      // Handle error
    }
  };

  const calculatePrice = (formData: any) => {
    // Simple price calculation logic
    let basePrice = 100; // Base price $100
    
    // Adjust price based on service type
    if (formData.serviceType === 'residential_deep') {
      basePrice += 50;
    } else if (formData.serviceType === 'residential_move') {
      basePrice += 75;
    }
    
    // Adjust price based on property size
    if (formData.propertyDetails?.size === 'large') {
      basePrice += 50;
    } else if (formData.propertyDetails?.size === 'medium') {
      basePrice += 25;
    }
    
    return basePrice;
  };

  const handlePaymentComplete = () => {
    // Redirect to confirmation page
    window.location.href = '/booking-confirmation';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Book Your Cleaning Service</h1>
      
      <BookingForm onSubmit={handleBookingSubmit} />
      
      {bookingData && (
        <ModernPaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          amount={totalPrice}
          formData={bookingData}
          user={user}
          booking={bookingData}
          onPaymentComplete={handlePaymentComplete}
        />
      )}
    </div>
  );
}
