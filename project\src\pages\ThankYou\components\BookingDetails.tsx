import React from 'react';
import { motion } from 'framer-motion';
import { 
  MapPin, Users, Calendar, Clock, Building2, 
  Brush, Sparkles, GlassWater, Construction,
  Shield, Ruler, AlertCircle
} from 'lucide-react';

interface BookingDetailsProps {
  formData: any;
}

export function BookingDetails({ formData }: BookingDetailsProps) {
  // Helper function to safely get nested properties
  const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((acc, part) => acc && acc[part], obj);
  };

  // Get service-specific details
  const getServiceDetails = () => {
    const serviceType = getNestedValue(formData, 'serviceType');
    const details: any = {};

    switch (serviceType) {
      case 'carpet':
        details.icon = Brush;
        details.extraInfo = [
          { label: 'Carpet Material', value: getNestedValue(formData, 'carpetDetails.material') },
          { label: 'Carpet Age', value: getNestedValue(formData, 'carpetDetails.age') },
          { label: 'Stain Treatment', value: getNestedValue(formData, 'stainTreatment.types')?.join(', ') }
        ];
        break;
      case 'window':
        details.icon = GlassWater;
        details.extraInfo = [
          { label: 'Window Count', value: getNestedValue(formData, 'windowDetails.windowCount') },
          { label: 'Service Scope', value: getNestedValue(formData, 'windowDetails.serviceScope') },
          { label: 'Height Access', value: getNestedValue(formData, 'accessDetails.heightAccess') }
        ];
        break;
      case 'sanitization':
        details.icon = Shield;
        details.extraInfo = [
          { label: 'Sanitization Level', value: getNestedValue(formData, 'protocolDetails.sanitizationLevel') },
          { label: 'Occupancy', value: getNestedValue(formData, 'sanitizationScope.occupancy') },
          { label: 'Operating Hours', value: getNestedValue(formData, 'sanitizationScope.operatingHours') }
        ];
        break;
      case 'construction':
        details.icon = Construction;
        details.extraInfo = [
          { label: 'Project Type', value: getNestedValue(formData, 'projectDetails.type') },
          { label: 'Phase', value: getNestedValue(formData, 'cleaningScope.phase') },
          { label: 'Special Requirements', value: getNestedValue(formData, 'cleaningScope.specialRequirements')?.join(', ') }
        ];
        break;
      default:
        details.icon = Sparkles;
        details.extraInfo = [];
    }

    return details;
  };

  const serviceDetails = getServiceDetails();
  const ServiceIcon = serviceDetails.icon;

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.2 }}
      className="bg-white rounded-2xl shadow-xl p-8 mb-8"
    >
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-3 rounded-full bg-brand-100">
          <ServiceIcon className="w-6 h-6 text-brand-600" />
        </div>
        <h2 className="text-2xl font-semibold text-gray-900">
          Booking Details
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Location & Service Info */}
        <div className="space-y-6">
          <div className="flex items-start space-x-3">
            <MapPin className="w-5 h-5 text-brand-600 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">Location</h3>
              <p className="text-gray-600">{getNestedValue(formData, 'propertyDetails.propertyAddress')}</p>
              {getNestedValue(formData, 'propertyDetails.squareFootage') && (
                <p className="text-sm text-gray-500">
                  {getNestedValue(formData, 'propertyDetails.squareFootage')} sq ft
                </p>
              )}
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <Building2 className="w-5 h-5 text-brand-600 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">Property Details</h3>
              <p className="text-gray-600">{getNestedValue(formData, 'propertyDetails.propertyType')}</p>
              <p className="text-sm text-gray-500">{getNestedValue(formData, 'propertyDetails.industryType')}</p>
            </div>
          </div>

          {/* Service-specific details */}
          {serviceDetails.extraInfo.map((info: any, index: number) => (
            info.value && (
              <div key={index} className="flex items-start space-x-3">
                <Ruler className="w-5 h-5 text-brand-600 mt-1" />
                <div>
                  <h3 className="font-medium text-gray-900">{info.label}</h3>
                  <p className="text-gray-600">{info.value}</p>
                </div>
              </div>
            )
          ))}
        </div>

        {/* Schedule & Contact */}
        <div className="space-y-6">
          {getNestedValue(formData, 'schedule.date') && (
            <div className="flex items-start space-x-3">
              <Calendar className="w-5 h-5 text-brand-600 mt-1" />
              <div>
                <h3 className="font-medium text-gray-900">Schedule</h3>
                <p className="text-gray-600">
                  {new Date(formData.schedule.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
                {formData.schedule.timeSlot && (
                  <p className="text-sm text-gray-500">{formData.schedule.timeSlot}</p>
                )}
              </div>
            </div>
          )}

          {formData.schedule.frequency && formData.schedule.frequency !== 'one-time' && (
            <div className="flex items-start space-x-3">
              <Clock className="w-5 h-5 text-brand-600 mt-1" />
              <div>
                <h3 className="font-medium text-gray-900">Service Frequency</h3>
                <p className="text-gray-600">{formData.schedule.frequency}</p>
              </div>
            </div>
          )}

          <div className="flex items-start space-x-3">
            <Users className="w-5 h-5 text-brand-600 mt-1" />
            <div>
              <h3 className="font-medium text-gray-900">Contact Details</h3>
              <p className="text-gray-600">{formData.contact.fullName}</p>
              <p className="text-sm text-gray-500">{formData.contact.email}</p>
              <p className="text-sm text-gray-500">{formData.contact.phone}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Special Instructions or Notes */}
      {getNestedValue(formData, 'schedule.access') && (
        <div className="mt-6 p-4 bg-brand-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-5 h-5 text-brand-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-brand-900">Access Instructions</h4>
              <p className="text-brand-600 mt-1">{formData.schedule.access}</p>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}
