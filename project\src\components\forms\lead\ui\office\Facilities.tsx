import React from 'react';
import { Coffee, Bath, Leaf } from 'lucide-react';
import { motion } from 'framer-motion';

interface FacilitiesProps {
  employeeCount: number;
  squareFootage: number;
}

export function Facilities({ employeeCount, squareFootage }: FacilitiesProps) {
  return (
    <div className="space-y-4">
      <FacilityCard
        icon={Coffee}
        title="Break Room"
        color="yellow"
        usage={Math.min(employeeCount * 5, 100)}
      />
      
      <FacilityCard
        icon={Bath}
        title="Restrooms"
        color="purple"
        units={Math.ceil(employeeCount / 20)}
      />
      
      <FacilityCard
        icon={Leaf}
        title="Green Space"
        color="emerald"
        plants={Math.min(5, Math.ceil(squareFootage / 1000))}
      />
    </div>
  );
}

interface FacilityCardProps {
  icon: React.ComponentType<any>;
  title: string;
  color: string;
  usage?: number;
  units?: number;
  plants?: number;
}

function FacilityCard({ icon: Icon, title, color, usage, units, plants }: FacilityCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className={`bg-${color}-50/80 backdrop-blur rounded-lg p-4 shadow-sm 
                  hover:shadow-md transition-all duration-300`}
    >
      <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
        <Icon className={`w-4 h-4 mr-2 text-${color}-600`} />
        {title}
      </h4>

      {usage !== undefined && (
        <motion.div 
          className={`h-2 bg-${color}-200 rounded-full overflow-hidden`}
          initial={{ width: 0 }}
          animate={{ width: '100%' }}
        >
          <motion.div
            className={`h-full bg-${color}-400`}
            initial={{ width: 0 }}
            animate={{ width: `${usage}%` }}
            transition={{ delay: 0.5 }}
          />
        </motion.div>
      )}

      {units !== undefined && (
        <div className="grid grid-cols-2 gap-2">
          {Array.from({ length: units }).map((_, i) => (
            <motion.div
              key={i}
              className={`h-2 bg-${color}-200 rounded-full`}
              initial={{ scaleX: 0 }}
              animate={{ scaleX: 1 }}
              transition={{ delay: i * 0.1 }}
            />
          ))}
        </div>
      )}

      {plants !== undefined && (
        <div className="flex items-center space-x-1">
          {Array.from({ length: plants }).map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, rotate: -30 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: i * 0.1 }}
            >
              <Leaf className={`w-4 h-4 text-${color}-500 animate-pulse-scale`} />
            </motion.div>
          ))}
        </div>
      )}
    </motion.div>
  );
}
