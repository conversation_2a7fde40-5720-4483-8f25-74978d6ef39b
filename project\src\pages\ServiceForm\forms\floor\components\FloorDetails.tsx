import React from 'react';
import { Grid, Ruler, AlertTriangle, Shield, Droplets, Brush, Wrench, Building, Layers, Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';

interface FloorDetailsProps {
  details: {
    floorType: string;
    squareFootage: number;
    floors: number;
    currentCondition: string;
    age: string;
    previousTreatments: string[];
    trafficLevel: string;
    specialCoatings: boolean;
    maintenanceFrequency: string;
  };
  onChange: (details: any) => void;
}

export function FloorDetails({ details, onChange }: FloorDetailsProps) {
  const floorTypes = [
    { value: 'hardwood', label: 'Hardwood', description: 'Natural wood flooring' },
    { value: 'marble', label: 'Marble', description: 'Natural stone marble' },
    { value: 'granite', label: 'Granite', description: 'Natural stone granite' },
    { value: 'terrazzo', label: 'Terrazzo', description: 'Composite material' },
    { value: 'vinyl', label: 'Vinyl/VCT', description: 'Vinyl composition tile' },
    { value: 'concrete', label: 'Concrete', description: 'Polished concrete' },
    { value: 'limestone', label: 'Limestone', description: 'Natural limestone' },
    { value: 'other', label: 'Other', description: 'Other floor types' }
  ];

  const conditions = [
    { value: 'excellent', label: 'Excellent', description: 'Minor wear only' },
    { value: 'good', label: 'Good', description: 'Some visible wear' },
    { value: 'fair', label: 'Fair', description: 'Noticeable wear/damage' },
    { value: 'poor', label: 'Poor', description: 'Significant damage' },
    { value: 'severe', label: 'Severely Damaged', description: 'Major repairs needed' }
  ];

  const trafficLevels = [
    { value: 'light', label: 'Light Traffic', description: '< 100 people daily' },
    { value: 'moderate', label: 'Moderate Traffic', description: '100-500 people daily' },
    { value: 'heavy', label: 'Heavy Traffic', description: '500-1000 people daily' },
    { value: 'extreme', label: 'Extreme Traffic', description: '1000+ people daily' }
  ];

  const maintenanceFrequencies = [
    { value: 'daily', label: 'Daily', description: 'Regular daily maintenance' },
    { value: 'weekly', label: 'Weekly', description: 'Weekly maintenance schedule' },
    { value: 'monthly', label: 'Monthly', description: 'Monthly maintenance only' },
    { value: 'none', label: 'No Regular Maintenance', description: 'No current maintenance' }
  ];

  const ages = [
    '0-2 years',
    '3-5 years',
    '6-10 years',
    '11-15 years',
    '15+ years',
    'Unknown'
  ];

  const treatments = [
    { id: 'regular', label: 'Regular Maintenance', icon: Brush },
    { id: 'polish', label: 'Polishing', icon: Sparkles },
    { id: 'refinish', label: 'Refinishing', icon: Droplets },
    { id: 'wax', label: 'Waxing', icon: Shield },
    { id: 'repair', label: 'Repairs', icon: Wrench },
    { id: 'none', label: 'No Previous Treatment', icon: AlertTriangle }
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Grid className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Floor Details</h3>
          <p className="text-gray-600">Tell us about your floor's current condition</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        {/* Floor Type Selection */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Floor Type <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {floorTypes.map((type, index) => (
              <motion.label
                key={type.value}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  details.floorType === type.value
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="floorType"
                  value={type.value}
                  checked={details.floorType === type.value}
                  onChange={(e) => onChange({ ...details, floorType: e.target.value })}
                  className="sr-only"
                />
                <div className="font-medium text-gray-900">{type.label}</div>
                <p className="text-sm text-gray-600 mt-1">{type.description}</p>
              </motion.label>
            ))}
          </div>
        </div>

        {/* Property Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Square Footage */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Square Footage <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.squareFootage || ''}
                onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="Total floor area"
                min="1"
                required
              />
            </div>
            <p className="text-sm text-gray-500">Enter the total area to be restored</p>
          </div>

          {/* Number of Floors */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Number of Floors <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Layers className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="number"
                value={details.floors || ''}
                onChange={(e) => onChange({ ...details, floors: Number(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="Number of floors"
                min="1"
                required
              />
            </div>
            <p className="text-sm text-gray-500">Total number of floors needing service</p>
          </div>
        </div>

        {/* Traffic Level */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Traffic Level <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {trafficLevels.map((level, index) => (
              <motion.label
                key={level.value}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + (index * 0.1) }}
                className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  details.trafficLevel === level.value
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="trafficLevel"
                  value={level.value}
                  checked={details.trafficLevel === level.value}
                  onChange={(e) => onChange({ ...details, trafficLevel: e.target.value })}
                  className="sr-only"
                />
                <div className="font-medium text-gray-900">{level.label}</div>
                <p className="text-sm text-gray-600 mt-1">{level.description}</p>
              </motion.label>
            ))}
          </div>
        </div>

        {/* Current Condition */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Current Condition <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {conditions.map((condition, index) => (
              <motion.label
                key={condition.value}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + (index * 0.1) }}
                className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  details.currentCondition === condition.value
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="currentCondition"
                  value={condition.value}
                  checked={details.currentCondition === condition.value}
                  onChange={(e) => onChange({ ...details, currentCondition: e.target.value })}
                  className="sr-only"
                />
                <div className="font-medium text-gray-900">{condition.label}</div>
                <p className="text-sm text-gray-600 mt-1">{condition.description}</p>
              </motion.label>
            ))}
          </div>
        </div>

        {/* Maintenance Frequency */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Current Maintenance Frequency <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {maintenanceFrequencies.map((freq, index) => (
              <motion.label
                key={freq.value}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + (index * 0.1) }}
                className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  details.maintenanceFrequency === freq.value
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="maintenanceFrequency"
                  value={freq.value}
                  checked={details.maintenanceFrequency === freq.value}
                  onChange={(e) => onChange({ ...details, maintenanceFrequency: e.target.value })}
                  className="sr-only"
                />
                <div className="font-medium text-gray-900">{freq.label}</div>
                <p className="text-sm text-gray-600 mt-1">{freq.description}</p>
              </motion.label>
            ))}
          </div>
        </div>

        {/* Floor Age */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Floor Age <span className="text-red-500">*</span>
          </label>
          <select
            value={details.age}
            onChange={(e) => onChange({ ...details, age: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select floor age</option>
            {ages.map((age) => (
              <option key={age} value={age}>{age}</option>
            ))}
          </select>
        </div>

        {/* Special Coatings */}
        <div className="space-y-2">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={details.specialCoatings}
              onChange={(e) => onChange({ ...details, specialCoatings: e.target.checked })}
              className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
            />
            <div className="flex items-center">
              <Shield className="w-5 h-5 text-brand-600 mr-2" />
              <span className="text-gray-700">Special Coatings or Sealants Present</span>
            </div>
          </label>
          <p className="text-sm text-gray-500 ml-8">Check if your floor has any special protective coatings or sealants</p>
        </div>

        {/* Previous Treatments */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Previous Treatments
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {treatments.map((treatment, index) => {
              const Icon = treatment.icon;
              return (
                <motion.label
                  key={treatment.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 + (index * 0.1) }}
                  className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
                    details.previousTreatments.includes(treatment.id)
                      ? 'border-brand-500 bg-brand-50 shadow-md'
                      : 'border-gray-200 hover:border-brand-300'
                  }`}
                >
                  <input
                    type="checkbox"
                    checked={details.previousTreatments.includes(treatment.id)}
                    onChange={(e) => {
                      const newTreatments = e.target.checked
                        ? [...details.previousTreatments, treatment.id]
                        : details.previousTreatments.filter(t => t !== treatment.id);
                      onChange({ ...details, previousTreatments: newTreatments });
                    }}
                    className="sr-only"
                  />
                  <Icon className={`w-5 h-5 mr-3 ${
                    details.previousTreatments.includes(treatment.id) ? 'text-brand-600' : 'text-gray-400'
                  }`} />
                  <span className="text-gray-700">{treatment.label}</span>
                </motion.label>
              );
            })}
          </div>
        </div>

        {/* Warning for severe condition */}
        {details.currentCondition === 'severe' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl"
          >
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Severe Damage Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Due to the severe condition of your floor, an on-site inspection may be required before proceeding with restoration. Our team will contact you to schedule an assessment.
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
