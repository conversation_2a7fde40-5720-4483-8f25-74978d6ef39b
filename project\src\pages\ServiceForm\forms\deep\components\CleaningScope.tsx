import React from 'react';
import { Sparkles, Target, Shield, Leaf } from 'lucide-react';
import { motion } from 'framer-motion';

interface CleaningScopeProps {
  scope: {
    phase: string;
    areas: string[];
    additionalServices: string[];
    specialRequirements: string[];
  };
  onChange: (scope: any) => void;
}

export function CleaningScope({ scope, onChange }: CleaningScopeProps) {
  const phases = [
    {
      id: 'standard',
      label: 'Standard Deep Clean',
      description: 'Thorough cleaning of all areas'
    },
    {
      id: 'intensive',
      label: 'Intensive Deep Clean',
      description: 'Extra attention to heavily soiled areas'
    },
    {
      id: 'premium',
      label: 'Premium Deep Clean',
      description: 'Our most comprehensive cleaning package'
    }
  ];

  const areas = [
    'Reception/Lobby',
    'Private Offices',
    'Conference Rooms',
    'Break Room/Kitchen',
    'Restrooms',
    'Common Areas',
    'Stairwells',
    'Storage Areas',
    'Server Rooms',
    'Elevators',
    'Windows (Interior)',
    'HVAC Vents'
  ];

  const additionalServices = [
    'Carpet Deep Cleaning',
    'Floor Waxing/Buffing',
    'High Dusting',
    'Window Cleaning',
    'Upholstery Cleaning',
    'Sanitization Service',
    'Pressure Washing',
    'Grout Cleaning'
  ];

  const specialRequirements = [
    'Eco-Friendly Products Only',
    'Hypoallergenic Products',
    'After-Hours Service',
    'Weekend Service',
    'HEPA Filtration',
    'Certification Required'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Sparkles className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Cleaning Scope</h3>
          <p className="text-gray-600">Define your cleaning requirements</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        {/* Cleaning Phase Selection */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Cleaning Phase <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {phases.map((phase) => (
              <label
                key={phase.id}
                className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.phase === phase.id
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="phase"
                  value={phase.id}
                  checked={scope.phase === phase.id}
                  onChange={(e) => onChange({ ...scope, phase: e.target.value })}
                  className="sr-only"
                  required
                />
                <div className="font-medium text-gray-900">{phase.label}</div>
                <p className="text-sm text-gray-600 mt-1">{phase.description}</p>
              </label>
            ))}
          </div>
        </div>

        {/* Areas to Clean */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Areas to Clean <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {areas.map((area) => (
              <label
                key={area}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.areas.includes(area)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.areas.includes(area)}
                  onChange={(e) => {
                    const newAreas = e.target.checked
                      ? [...scope.areas, area]
                      : scope.areas.filter(a => a !== area);
                    onChange({ ...scope, areas: newAreas });
                  }}
                  className="sr-only"
                />
                <Target className={`w-5 h-5 mr-3 ${
                  scope.areas.includes(area) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{area}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Additional Services */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Additional Services
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {additionalServices.map((service) => (
              <label
                key={service}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.additionalServices.includes(service)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.additionalServices.includes(service)}
                  onChange={(e) => {
                    const newServices = e.target.checked
                      ? [...scope.additionalServices, service]
                      : scope.additionalServices.filter(s => s !== service);
                    onChange({ ...scope, additionalServices: newServices });
                  }}
                  className="sr-only"
                />
                <Shield className={`w-5 h-5 mr-3 ${
                  scope.additionalServices.includes(service) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{service}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Special Requirements */}
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Special Requirements
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {specialRequirements.map((req) => (
              <label
                key={req}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  scope.specialRequirements.includes(req)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={scope.specialRequirements.includes(req)}
                  onChange={(e) => {
                    const newReqs = e.target.checked
                      ? [...scope.specialRequirements, req]
                      : scope.specialRequirements.filter(r => r !== req);
                    onChange({ ...scope, specialRequirements: newReqs });
                  }}
                  className="sr-only"
                />
                <Leaf className={`w-5 h-5 mr-3 ${
                  scope.specialRequirements.includes(req) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{req}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
