import React, { useState } from 'react';
import SimpleCheckout from '../components/SimpleCheckout';

const CheckoutTest: React.FC = () => {
  const [amount, setAmount] = useState(25.00);
  const [description, setDescription] = useState('Test Payment');
  const [customerEmail, setCustomerEmail] = useState('');

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Checkout Test</h1>
          <p className="mt-2 text-gray-600">
            Test the Square checkout integration
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Configuration Panel */}
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Payment Configuration</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount ($)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0.50"
                  value={amount}
                  onChange={(e) => setAmount(parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Email (optional)
                </label>
                <input
                  type="email"
                  value={customerEmail}
                  onChange={(e) => setCustomerEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <h3 className="text-sm font-medium text-yellow-800 mb-2">
                Testing Instructions:
              </h3>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Currently in sandbox mode</li>
                <li>• Use Square test cards for payments</li>
                <li>• Test card: 4111 1111 1111 1111</li>
                <li>• Any future expiry date</li>
                <li>• Any 3-digit CVV</li>
              </ul>
            </div>
          </div>

          {/* Checkout Component */}
          <div>
            <SimpleCheckout
              amount={amount}
              description={description}
              customerEmail={customerEmail || undefined}
            />
          </div>
        </div>

        <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">How it works:</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-700">
            <li>Configure your payment details above</li>
            <li>Click "Pay Now" to create a checkout session</li>
            <li>You'll be redirected to Square's secure payment page</li>
            <li>Complete the payment using test card details</li>
            <li>You'll be redirected back to the thank you page</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default CheckoutTest;
