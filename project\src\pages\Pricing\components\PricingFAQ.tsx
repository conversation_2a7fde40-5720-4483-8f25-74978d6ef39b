import React from 'react';
import { Plus, Minus } from 'lucide-react';

const faqs = [
  {
    question: 'How is the pricing calculated?',
    answer: 'Our pricing is based on several factors including square footage, service frequency, and type of facility. We offer customized quotes to ensure you only pay for what you need.'
  },
  {
    question: 'What is included in the basic plan?',
    answer: 'The basic plan includes essential cleaning services such as vacuuming, dusting, trash removal, and basic sanitization of common areas. Additional services can be added as needed.'
  },
  {
    question: 'Do you require long-term contracts?',
    answer: 'While we offer discounted rates for long-term commitments, we also provide flexible month-to-month services to meet your specific needs.'
  },
  {
    question: 'Can I customize my cleaning schedule?',
    answer: 'Yes! We work with you to create a cleaning schedule that fits your business hours and requirements, whether it is daily, weekly, or monthly service.'
  }
];

export function PricingFAQ() {
  const [openIndex, setOpenIndex] = React.useState<number | null>(null);

  return (
    <section className="py-24 bg-white">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900">
            Frequently Asked Questions
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            Everything you need to know about our pricing
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="border border-gray-200 rounded-lg overflow-hidden"
            >
              <button
                className="w-full flex items-center justify-between p-6 text-left"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <span className="text-lg font-medium text-gray-900">
                  {faq.question}
                </span>
                {openIndex === index ? (
                  <Minus className="w-5 h-5 text-gray-500" />
                ) : (
                  <Plus className="w-5 h-5 text-gray-500" />
                )}
              </button>
              {openIndex === index && (
                <div className="px-6 pb-6">
                  <p className="text-gray-600">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
