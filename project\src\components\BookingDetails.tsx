import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MapPin, Users, Calendar, Clock, Building2, 
  Brush, Sparkles, GlassWater, Construction,
  Shield, Ruler, AlertCircle, BadgeCheck, Award, ThumbsUp,
  CreditCard, ArrowRight, DollarSign
} from 'lucide-react';
import { Button } from './ui/Button';
import { PaymentOptionsModal } from './PaymentOptionsModal';
import { useAuth } from '/lib/auth/useAuth';

// Helper function to safely get nested properties
function getNestedValue(obj: any, path: string) {
  return path.split('.').reduce((acc, part) => acc && acc[part], obj);
}

// Service details configuration moved outside component
const getServiceDetails = (formData: any) => {
  const serviceType = getNestedValue(formData, 'serviceType');
  const details: any = {
    icon: Sparkles,
    title: 'Service Details',
    sections: []
  };

  switch (serviceType) {
    case 'carpet':
      details.icon = Brush;
      details.title = 'Carpet Cleaning Details';
      details.sections = [
        {
          title: 'Carpet Information',
          items: [
            { label: 'Material', value: getNestedValue(formData, 'carpetDetails.material') },
            { label: 'Age', value: getNestedValue(formData, 'carpetDetails.age') },
            { label: 'Condition', value: getNestedValue(formData, 'carpetDetails.condition') }
          ]
        },
        {
          title: 'Stain Treatment',
          items: [
            { label: 'Types', value: getNestedValue(formData, 'stainTreatment.types')?.join(', ') }
          ]
        }
      ];
      break;
      
    case 'window':
      details.icon = GlassWater;
      details.title = 'Window Cleaning Details';
      details.sections = [
        {
          title: 'Service Scope',
          items: [
            { label: 'Window Count', value: getNestedValue(formData, 'windowDetails.windowCount') },
            { label: 'Service Type', value: getNestedValue(formData, 'windowDetails.serviceScope') },
            { label: 'Window Types', value: getNestedValue(formData, 'windowDetails.windowTypes')?.join(', ') }
          ]
        },
        {
          title: 'Access Details',
          items: [
            { label: 'Height Access', value: getNestedValue(formData, 'accessDetails.heightAccess') },
            { label: 'Parking Available', value: getNestedValue(formData, 'accessDetails.parkingAvailable') ? 'Yes' : 'No' }
          ]
        }
      ];
      break;

    case 'sanitization':
      details.icon = Shield;
      details.title = 'Sanitization Details';
      details.sections = [
        {
          title: 'Protocol Details',
          items: [
            { label: 'Level', value: getNestedValue(formData, 'protocolDetails.sanitizationLevel') },
            { label: 'Requirements', value: getNestedValue(formData, 'protocolDetails.specialRequirements')?.join(', ') }
          ]
        },
        {
          title: 'Scope',
          items: [
            { label: 'Areas', value: getNestedValue(formData, 'sanitizationScope.areas')?.join(', ') },
            { label: 'High-Touch Surfaces', value: getNestedValue(formData, 'sanitizationScope.highTouchSurfaces')?.join(', ') },
            { label: 'Occupancy', value: getNestedValue(formData, 'sanitizationScope.occupancy') }
          ]
        }
      ];
      break;

    case 'construction':
      details.icon = Construction;
              details.title = 'Post-Construction Cleaning Details';
      details.sections = [
        {
          title: 'Project Details',
          items: [
            { label: 'Type', value: getNestedValue(formData, 'projectDetails.type') },
            { label: 'Phase', value: getNestedValue(formData, 'cleaningScope.phase') }
          ]
        },
        {
          title: 'Cleaning Scope',
          items: [
            { label: 'Areas', value: getNestedValue(formData, 'cleaningScope.areas')?.join(', ') },
            { label: 'Special Requirements', value: getNestedValue(formData, 'cleaningScope.specialRequirements')?.join(', ') }
          ]
        }
      ];
      break;

    case 'deep-cleaning':
      details.icon = Sparkles;
      details.title = 'Deep Cleaning Details';
      details.sections = [
        {
          title: 'Property Information',
          items: [
            { label: 'Property Type', value: getNestedValue(formData, 'propertyType') },
            { label: 'Property Size', value: getNestedValue(formData, 'propertySize') },
            { label: 'Address', value: getNestedValue(formData, 'address') }
          ]
        },
        {
          title: 'Service Details',
          items: [
            { label: 'Preferred Date', value: getNestedValue(formData, 'preferredDate') },
            { label: 'Preferred Time', value: getNestedValue(formData, 'preferredTime') },
            { label: 'Add-ons', value: getNestedValue(formData, 'addOns')?.join(', ') || 'None' }
          ]
        }
      ];
      break;

    default:
      details.sections = [
        {
          title: 'Service Information',
          items: [
            { label: 'Type', value: serviceType },
            { label: 'Frequency', value: getNestedValue(formData, 'cleaningDetails.frequency') }
          ]
        }
      ];
  }

  return details;
};

interface BookingDetailsProps {
  formData: any;
  showPaymentButton?: boolean;
  estimatedPrice?: number;
  onPaymentComplete?: () => void;
}

export function BookingDetails({ 
  formData, 
  showPaymentButton = false, 
  estimatedPrice = 0,
  onPaymentComplete 
}: BookingDetailsProps) {
  const { user } = useAuth();
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Add safety check for formData
  if (!formData) {
    return (
      <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
        <div className="text-center text-gray-500">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <p>No booking details available</p>
        </div>
      </div>
    );
  }

  const serviceDetails = getServiceDetails(formData);
  const ServiceIcon = serviceDetails.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="bg-white rounded-2xl shadow-xl p-8 mb-8"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-12">
        <div className="flex items-center space-x-4">
          <div className="p-4 rounded-xl bg-brand-100">
            <ServiceIcon className="w-8 h-8 text-brand-600" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold text-gray-900">
              {serviceDetails.title}
            </h2>
            <div className="flex items-center mt-2 text-sm text-brand-600">
              <BadgeCheck className="w-4 h-4 mr-2" />
              <span>Booking #{Math.random().toString(36).substr(2, 9).toUpperCase()}</span>
            </div>
          </div>
        </div>

        {/* Trust Badges */}
        <div className="hidden md:flex items-center space-x-6">
          <div className="flex items-center text-sm bg-brand-50 px-4 py-2 rounded-lg">
            <Award className="w-4 h-4 mr-2 text-brand-600" />
            <span className="text-gray-700">Licensed & Insured</span>
          </div>
          <div className="flex items-center text-sm bg-brand-50 px-4 py-2 rounded-lg">
            <ThumbsUp className="w-4 h-4 mr-2 text-brand-600" />
            <span className="text-gray-700">Satisfaction Guaranteed</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {/* Property & Service Details */}
        <div className="space-y-8">
          {/* Contact Information */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="flex items-start space-x-4"
          >
            <div className="p-3 rounded-lg bg-gray-50">
              <MapPin className="w-5 h-5 text-brand-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Contact & Location</h3>
              <p className="text-gray-600 mt-1">
                {getNestedValue(formData, 'firstName')} {getNestedValue(formData, 'lastName')}
              </p>
              <p className="text-gray-600">
                {getNestedValue(formData, 'address')}, {getNestedValue(formData, 'city')} {getNestedValue(formData, 'zipCode')}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                {getNestedValue(formData, 'email')} • {getNestedValue(formData, 'phone')}
              </p>
            </div>
          </motion.div>

          {/* Service-specific sections */}
          {serviceDetails.sections.map((section: any, index: number) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 + (index * 0.1) }}
              className="border-t pt-6"
            >
              <h3 className="font-medium text-gray-900 mb-4">{section.title}</h3>
              <div className="space-y-2">
                {section.items.map((item: any, itemIndex: number) => (
                  item.value && (
                    <div key={itemIndex} className="flex justify-between text-sm">
                      <span className="text-gray-600">{item.label}:</span>
                      <span className="font-medium text-gray-900">{item.value}</span>
                    </div>
                  )
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Pricing & Payment */}
        <div className="space-y-6">
          {estimatedPrice > 0 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-gradient-to-br from-brand-50 to-brand-100 rounded-xl p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">Estimated Total</h3>
                <div className="text-right">
                  <div className="text-2xl font-bold text-brand-600">${estimatedPrice}</div>
                  <div className="text-xs text-gray-500">Final price may vary</div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Payment Button */}
          {showPaymentButton && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-xl border border-gray-200 p-6"
            >
              <div className="text-center mb-6">
                <h3 className="font-semibold text-gray-900 mb-2">Ready to book?</h3>
                <p className="text-sm text-gray-600">
                  Complete your booking with secure payment
                </p>
              </div>

              <Button
                onClick={() => setShowPaymentModal(true)}
                size="lg"
                className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800 shadow-lg hover:shadow-xl transition-all duration-200"
              >
                Complete Booking & Pay
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>

              <p className="text-xs text-gray-500 mt-4 text-center">
                Multiple payment options available • No hidden fees • Cancel anytime
              </p>
            </motion.div>
          )}
        </div>
      </div>

      {/* Payment Options Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={estimatedPrice}
        description={`${getNestedValue(formData, 'serviceType') || 'Cleaning'} Service`}
        customerEmail={getNestedValue(formData, 'email')}
        formData={formData}
        user={user}
        onPaymentComplete={() => {
          setShowPaymentModal(false);
          if (onPaymentComplete) {
            onPaymentComplete();
          }
        }}
      />
    </motion.div>
  );
}
