/**
 * Enhanced Error Boundary Component
 * 
 * Provides comprehensive error catching and recovery for React components
 * with user-friendly error messages and recovery actions.
 */

import React, { Component, ReactNode } from 'react';
import { EnhancedError<PERSON><PERSON><PERSON>, EnhancedError, ErrorRecoveryAction } from '../../lib/utils/enhancedErrorHandler';

interface Props {
  children: ReactNode;
  fallback?: (error: EnhancedError, retry: () => void) => ReactNode;
  onError?: (error: EnhancedError) => void;
  boundaryId?: string;
  enableRecovery?: boolean;
}

interface State {
  hasError: boolean;
  error: EnhancedError | null;
  errorId: string | null;
  isRetrying: boolean;
}

export class ErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorId: null,
      isRetrying: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const enhancedError = EnhancedErrorHandler.createEnhancedError(
      'REACT_ERROR_BOUNDARY',
      'Something went wrong. Please try refreshing the page.',
      error.message,
      {
        severity: 'high',
        category: 'system',
        retryable: true,
        originalError: error
      }
    );

    return {
      hasError: true,
      error: enhancedError,
      errorId: `error-${Date.now()}`
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const enhancedError = EnhancedErrorHandler.createEnhancedError(
      'REACT_ERROR_BOUNDARY',
      'Something went wrong. Please try refreshing the page.',
      error.message,
      {
        severity: 'high',
        category: 'system',
        context: {
          componentStack: errorInfo.componentStack,
          errorBoundary: this.props.boundaryId || 'unknown'
        },
        retryable: true,
        originalError: error
      }
    );

    // Log the error
    EnhancedErrorHandler.logError(enhancedError);

    // Update state with enhanced error
    this.setState({ error: enhancedError });

    // Call onError callback if provided
    if (this.props.onError) {
      this.props.onError(enhancedError);
    }

    // Update error boundary state in handler
    if (this.props.boundaryId) {
      EnhancedErrorHandler.handleErrorWithRecovery(
        enhancedError,
        { componentStack: errorInfo.componentStack },
        this.props.boundaryId
      );
    }
  }

  handleRetry = async () => {
    if (!this.state.error || this.state.isRetrying) return;

    this.setState({ isRetrying: true });

    try {
      // Clear the error state to retry rendering
      this.setState({
        hasError: false,
        error: null,
        errorId: null,
        isRetrying: false
      });

      // Clear error boundary state
      if (this.props.boundaryId) {
        EnhancedErrorHandler.clearErrorBoundary(this.props.boundaryId);
      }
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      this.setState({ isRetrying: false });
    }
  };

  handleRecoveryAction = async (action: ErrorRecoveryAction) => {
    this.setState({ isRetrying: true });

    try {
      await action.action();
      
      // If action was successful, try to recover
      if (action.type === 'retry') {
        await this.handleRetry();
      }
    } catch (actionError) {
      console.error('Recovery action failed:', actionError);
      // Update error with recovery failure info
      if (this.state.error) {
        const updatedError = {
          ...this.state.error,
          context: {
            ...this.state.error.context,
            recoveryAttempts: (this.state.error.context.recoveryAttempts || 0) + 1,
            lastRecoveryError: actionError
          }
        };
        this.setState({ error: updatedError });
      }
    } finally {
      this.setState({ isRetrying: false });
    }
  };

  renderDefaultErrorUI = (error: EnhancedError) => {
    const { enableRecovery = true } = this.props;

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            {/* Error Icon */}
            <div className="mx-auto h-12 w-12 text-red-500">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>

            {/* Error Message */}
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Oops! Something went wrong
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              {error.userMessage}
            </p>

            {/* Error ID for support */}
            {this.state.errorId && (
              <p className="mt-2 text-xs text-gray-400">
                Error ID: {this.state.errorId}
              </p>
            )}
          </div>

          {/* Recovery Actions */}
          {enableRecovery && error.recoveryActions.length > 0 && (
            <div className="space-y-3">
              {error.recoveryActions
                .sort((a, b) => {
                  const priorityOrder = { high: 0, medium: 1, low: 2 };
                  return priorityOrder[a.priority] - priorityOrder[b.priority];
                })
                .map((action, index) => (
                  <button
                    key={index}
                    onClick={() => this.handleRecoveryAction(action)}
                    disabled={this.state.isRetrying}
                    className={`
                      w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white
                      ${action.priority === 'high' 
                        ? 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500' 
                        : action.priority === 'medium'
                        ? 'bg-gray-600 hover:bg-gray-700 focus:ring-gray-500'
                        : 'bg-gray-400 hover:bg-gray-500 focus:ring-gray-400'
                      }
                      focus:outline-none focus:ring-2 focus:ring-offset-2
                      disabled:opacity-50 disabled:cursor-not-allowed
                    `}
                  >
                    {this.state.isRetrying ? (
                      <div className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Working...
                      </div>
                    ) : (
                      action.label
                    )}
                  </button>
                ))}
            </div>
          )}

          {/* Technical Details (Development Only) */}
          {!import.meta.env.PROD && (
            <details className="mt-6">
              <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                Technical Details (Development)
              </summary>
              <div className="mt-2 p-3 bg-gray-100 rounded text-xs text-gray-700 font-mono">
                <div><strong>Code:</strong> {error.code}</div>
                <div><strong>Category:</strong> {error.category}</div>
                <div><strong>Severity:</strong> {error.severity}</div>
                <div><strong>Technical Message:</strong> {error.technicalMessage}</div>
                {error.context && Object.keys(error.context).length > 0 && (
                  <div><strong>Context:</strong> {JSON.stringify(error.context, null, 2)}</div>
                )}
              </div>
            </details>
          )}
        </div>
      </div>
    );
  };

  render() {
    if (this.state.hasError && this.state.error) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(this.state.error, this.handleRetry);
      }

      // Use default error UI
      return this.renderDefaultErrorUI(this.state.error);
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// Hook for error boundary state
export function useErrorBoundary(boundaryId?: string) {
  const throwError = (error: Error | string) => {
    const errorToThrow = typeof error === 'string' ? new Error(error) : error;
    throw errorToThrow;
  };

  const clearError = () => {
    if (boundaryId) {
      EnhancedErrorHandler.clearErrorBoundary(boundaryId);
    }
  };

  const getErrorState = () => {
    if (boundaryId) {
      return EnhancedErrorHandler.getErrorBoundaryState(boundaryId);
    }
    return null;
  };

  return {
    throwError,
    clearError,
    getErrorState
  };
}

