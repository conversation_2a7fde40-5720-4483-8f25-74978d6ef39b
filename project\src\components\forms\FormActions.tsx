import React from 'react';
import { Button } from '../ui/Button';

interface FormActionsProps {
  onBack: () => void;
  onNext: () => void;
  isFirstStep?: boolean;
  isLastStep?: boolean;
  isSubmitting?: boolean;
}

export function FormActions({ 
  onBack, 
  onNext, 
  isFirstStep, 
  isLastStep,
  isSubmitting 
}: FormActionsProps) {
  return (
    <div className="flex items-center justify-between mt-12 pt-8 border-t border-gray-200">
      <Button
        variant="outline"
        onClick={onBack}
        disabled={isSubmitting}
        size="lg"
        className="min-w-[120px]"
      >
        {isFirstStep ? 'Cancel' : 'Back'}
      </Button>
      
      <Button
        onClick={onNext}
        disabled={isSubmitting}
        size="lg"
        className="min-w-[120px]"
      >
        {isLastStep ? 'Submit Request' : 'Continue'}
      </Button>
    </div>
  );
}
