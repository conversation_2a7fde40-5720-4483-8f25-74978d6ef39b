import React from 'react';
import { Calendar, Clock, AlertCircle } from 'lucide-react';

interface ServiceSchedulingProps {
  schedule: {
    date: string;
    timeSlot: string;
    urgency: string;
  };
  onChange: (schedule: any) => void;
}

export function ServiceScheduling({ schedule, onChange }: ServiceSchedulingProps) {
  const today = new Date().toISOString().split('T')[0];
  
  const urgencyLevels = [
    {
      value: 'standard',
      label: 'Standard',
      description: 'Schedule within normal business hours'
    },
    {
      value: 'priority',
      label: 'Priority',
      description: 'Schedule within 24-48 hours'
    },
    {
      value: 'emergency',
      label: 'Emergency',
      description: 'Immediate response required'
    }
  ];

  const timeSlots = [
    { value: 'early-morning', label: 'Early Morning (5AM - 8AM)' },
    { value: 'morning', label: 'Morning (8AM - 12PM)' },
    { value: 'afternoon', label: 'Afternoon (12PM - 4PM)' },
    { value: 'evening', label: 'Evening (4PM - 8PM)' },
    { value: 'night', label: 'Night (8PM - 12AM)' },
    { value: 'after-hours', label: 'After Hours (12AM - 5AM)' }
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Calendar className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Schedule Service</h3>
          <p className="text-gray-600">Choose your preferred date and time</p>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Service Urgency <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {urgencyLevels.map((level) => (
              <label
                key={level.value}
                className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  schedule.urgency === level.value
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="urgency"
                  value={level.value}
                  checked={schedule.urgency === level.value}
                  onChange={(e) => onChange({ ...schedule, urgency: e.target.value })}
                  className="sr-only"
                />
                <div className="flex items-center mb-1">
                  <AlertCircle className={`w-5 h-5 mr-2 ${
                    schedule.urgency === level.value ? 'text-brand-600' : 'text-gray-400'
                  }`} />
                  <span className="font-medium text-gray-900">{level.label}</span>
                </div>
                <p className="text-sm text-gray-600">{level.description}</p>
              </label>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Preferred Date <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="date"
                value={schedule.date}
                onChange={(e) => onChange({ ...schedule, date: e.target.value })}
                min={today}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Preferred Time <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={schedule.timeSlot}
                onChange={(e) => onChange({ ...schedule, timeSlot: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              >
                <option value="">Select time slot</option>
                {timeSlots.map((slot) => (
                  <option key={slot.value} value={slot.value}>
                    {slot.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {schedule.urgency === 'emergency' && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Emergency Service Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Emergency services are available 24/7 with rapid response times. Additional fees may apply for emergency scheduling.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
