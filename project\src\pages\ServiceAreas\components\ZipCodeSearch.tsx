import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, MapPin } from 'lucide-react';
import { Button } from '../../../components/ui/Button';

interface ZipCodeSearchProps {
  onSubmit: (zipCode: string) => void;
}

export function ZipCodeSearch({ onSubmit }: ZipCodeSearchProps) {
  const [zipCode, setZipCode] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!/^\d{5}$/.test(zipCode)) {
      setError('Please enter a valid 5-digit ZIP code');
      return;
    }

    setError('');
    onSubmit(zipCode);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="max-w-xl mx-auto"
    >
      <form onSubmit={handleSubmit} className="relative">
        <div className="flex rounded-2xl bg-white shadow-xl p-2">
          <div className="relative flex-grow">
            <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={zipCode}
              onChange={(e) => {
                setZipCode(e.target.value.replace(/\D/g, '').slice(0, 5));
                setError('');
              }}
              placeholder="Enter ZIP Code"
              className="w-full pl-12 pr-4 py-3 text-lg rounded-xl focus:outline-none focus:ring-2 focus:ring-brand-500"
              maxLength={5}
            />
          </div>
          <Button
            type="submit"
            size="lg"
            className="ml-2 px-8 rounded-xl"
          >
            <Search className="w-5 h-5 mr-2" />
            Check Availability
          </Button>
        </div>
        {error && (
          <motion.p
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute text-red-100 text-sm mt-2"
          >
            {error}
          </motion.p>
        )}
      </form>
    </motion.div>
  );
}
