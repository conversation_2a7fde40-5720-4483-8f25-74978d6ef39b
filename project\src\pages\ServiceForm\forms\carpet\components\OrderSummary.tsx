import React from 'react';
import { Check, DollarSign } from 'lucide-react';
import type { FormData } from '../types';

interface OrderSummaryProps {
  formData: FormData;
}

export function OrderSummary({ formData }: OrderSummaryProps) {
  const calculatePrice = () => {
    let basePrice = formData.propertyDetails.squareFootage * 0.50; // $0.50 per sq ft
    
    // Add service type multiplier
    if (formData.serviceType === 'commercial') basePrice *= 1.2;
    if (formData.serviceType === 'medical') basePrice *= 1.5;
    
    // Add stain treatment fee
    if (formData.stainTreatment.types.length > 0) {
      basePrice += formData.stainTreatment.types.length * 50; // $50 per stain type
    }
    
    return basePrice;
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
        <DollarSign className="w-5 h-5 mr-2 text-brand-600" />
        Order Summary
      </h3>

      {formData.serviceType ? (
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Service Type:</span>
            <span className="font-medium text-gray-900">
              {formData.serviceType.charAt(0).toUpperCase() + formData.serviceType.slice(1)}
            </span>
          </div>

          {formData.propertyDetails.squareFootage > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Area:</span>
              <span className="font-medium text-gray-900">
                {formData.propertyDetails.squareFootage.toLocaleString()} sq ft
              </span>
            </div>
          )}

          {formData.stainTreatment.types.length > 0 && (
            <div className="border-t pt-4">
              <p className="text-sm text-gray-600 mb-2">Stain Treatment:</p>
              {formData.stainTreatment.types.map((type) => (
                <div key={type} className="flex items-center text-sm">
                  <Check className="w-4 h-4 text-brand-600 mr-2" />
                  <span className="text-gray-700">
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </span>
                </div>
              ))}
            </div>
          )}

          <div className="border-t pt-4">
            <div className="flex justify-between text-lg font-semibold">
              <span>Estimated Total:</span>
              <span className="text-brand-600">${calculatePrice().toFixed(2)}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Final price may vary based on inspection
            </p>
          </div>
        </div>
      ) : (
        <p className="text-gray-500 text-center">
          Select services to see pricing
        </p>
      )}
    </div>
  );
}
