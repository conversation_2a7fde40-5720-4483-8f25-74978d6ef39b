import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Building } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export function CallToAction() {
  const navigate = useNavigate();

  const handleGetQuote = () => {
    navigate('/contact');
  };

  return (
    <section className="py-28 sm:py-36">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="relative text-center"
        >
          <div className="relative z-10 flex flex-col items-center">
            <motion.div 
              className="w-16 h-16 bg-white/10 rounded-2xl mb-8 border border-white/20 flex items-center justify-center"
              style={{
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)',
              }}
              initial={{ scale: 0 }}
              whileInView={{ scale: 1, rotate: 360 }}
          viewport={{ once: true }}
              transition={{ type: 'spring', stiffness: 260, damping: 15, delay: 0.2 }}
            >
              <Building className="w-8 h-8 text-emerald-300" />
            </motion.div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-5">Elevate Your Business Environment</h2>
            <p className="text-lg text-white/70 max-w-2xl mx-auto mb-10">
              Partner with us to create a clean, safe, and professional space that reflects the quality of your brand.
            </p>

            <motion.button
              onClick={handleGetQuote}
              whileHover={{ scale: 1.05, boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)' }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              className="group px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 flex items-center gap-3 bg-white text-gray-900 shadow-lg"
          >
              Request a Free Quote
              <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
} 
