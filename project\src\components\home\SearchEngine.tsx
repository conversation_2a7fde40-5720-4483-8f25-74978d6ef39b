import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Search, MapPin, Star, Calendar, Clock, DollarSign, 
  Home, Building2, AlertCircle, Loader2, CheckCircle, History,
  TrendingUp, Sparkles, Navigation, GlassWater, Brush,
  Construction, Sprout, Grid, Waves, Hammer, ChevronRight,
  Mic, MicOff, Filter, X, ArrowUp, Zap, Target, Users
} from 'lucide-react';
import { navigateToServiceWithPropertyType, getValidServiceId, selectBestService } from '../../lib/utils/navigation';

// Enhanced interfaces
interface SearchSuggestion {
  id: string;
  text: string;
  type: 'service' | 'location' | 'popular' | 'recent' | 'autocomplete';
  serviceType?: string;
  propertyType?: string;
  category?: string;
  popularity?: number;
  price?: string;
  description?: string;
}

interface SearchResult {
  id: string;
  title: string;
  description: string;
  serviceType: string;
  propertyType: string;
  price: string;
  rating: number;
  availability: string;
  route: string;
  icon: React.ComponentType<any>;
  popular?: boolean;
  emergency?: boolean;
}

interface VoiceSearchState {
  isListening: boolean;
  isSupported: boolean;
  transcript: string;
  confidence: number;
}

interface SearchFilters {
  propertyType: 'all' | 'residential' | 'commercial';
  priceRange: 'all' | 'budget' | 'standard' | 'premium';
  availability: 'all' | 'today' | 'this-week' | 'flexible';
  serviceCategory: 'all' | 'cleaning' | 'maintenance' | 'specialized';
}

export function SearchEngine() {
  const navigate = useNavigate();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  
  // Core search state
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  // Location and filters
  const [zipCode, setZipCode] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({
    propertyType: 'all',
    priceRange: 'all',
    availability: 'all',
    serviceCategory: 'all'
  });
  const [showFilters, setShowFilters] = useState(false);
  
  // Voice search
  const [voiceState, setVoiceState] = useState<VoiceSearchState>({
    isListening: false,
    isSupported: false,
    transcript: '',
    confidence: 0
  });
  
  // Search history and analytics
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<string[]>([]);

  // Comprehensive service database
  const serviceDatabase: SearchResult[] = [
    // Residential Services
    {
      id: 'regular-cleaning',
      title: 'Regular House Cleaning',
      description: 'Weekly, bi-weekly, or monthly house cleaning service',
      serviceType: 'regular',
      propertyType: 'residential',
      price: 'From $89',
      rating: 4.9,
      availability: 'Available today',
      route: '/residential/regular',
      icon: Home,
      popular: true
    },
    {
      id: 'deep-cleaning',
      title: 'Deep Cleaning Service',
      description: 'Thorough one-time deep cleaning for your entire home',
      serviceType: 'deep',
      propertyType: 'residential',
      price: 'From $149',
      rating: 4.8,
      availability: 'Available tomorrow',
      route: '/residential/deep',
      icon: Sparkles,
      popular: true
    },
    {
      id: 'carpet-cleaning',
      title: 'Professional Carpet Cleaning',
      description: 'Deep carpet cleaning with stain removal and odor elimination',
      serviceType: 'carpet',
      propertyType: 'residential',
      price: 'From $99',
      rating: 4.7,
      availability: 'Available this week',
      route: '/residential/carpet',
      icon: Brush
    },
    {
      id: 'window-cleaning',
      title: 'Window Cleaning Service',
      description: 'Interior and exterior window cleaning for crystal clear views',
      serviceType: 'window',
      propertyType: 'residential',
      price: 'From $79',
      rating: 4.6,
      availability: 'Available today',
      route: '/residential/window',
      icon: GlassWater
    },
    {
      id: 'pressure-washing',
      title: 'Pressure Washing',
      description: 'Driveway, patio, deck, and exterior surface cleaning',
      serviceType: 'pressure',
      propertyType: 'residential',
      price: 'From $129',
      rating: 4.8,
      availability: 'Weather dependent',
      route: '/residential/pressure',
      icon: Waves
    },
    {
      id: 'sanitization',
      title: 'Sanitization & Disinfection',
      description: 'Medical-grade sanitization for health and safety',
      serviceType: 'sanitization',
      propertyType: 'residential',
      price: 'From $99',
      rating: 4.9,
      availability: 'Emergency available',
      route: '/residential/sanitization',
      icon: Sprout,
      emergency: true
    },
    {
      id: 'pool-cleaning',
      title: 'Pool Cleaning Service',
      description: 'Complete pool maintenance and chemical balancing',
      serviceType: 'pool',
      propertyType: 'residential',
      price: 'From $89',
      rating: 4.7,
      availability: 'Seasonal service',
      route: '/residential/pool',
      icon: Waves
    },
    {
      id: 'chimney-cleaning',
      title: 'Chimney Cleaning & Inspection',
      description: 'Professional chimney cleaning and safety inspection',
      serviceType: 'chimney',
      propertyType: 'residential',
      price: 'From $179',
      rating: 4.8,
      availability: 'Fall season special',
      route: '/residential/chimney',
      icon: Construction
    },
    // Commercial Services
    {
      id: 'office-cleaning',
      title: 'Office Cleaning Service',
      description: 'Daily, weekly, or monthly commercial office cleaning',
      serviceType: 'office',
      propertyType: 'commercial',
      price: 'From $199',
      rating: 4.8,
      availability: 'Flexible scheduling',
      route: '/service-form/office',
      icon: Building2,
      popular: true
    },
    {
      id: 'post-construction',
      title: 'Post-Construction Cleanup',
      description: 'Professional construction debris removal and cleaning',
      serviceType: 'construction',
      propertyType: 'commercial',
      price: 'From $299',
      rating: 4.7,
      availability: 'Available today',
      route: '/service-form/construction',
      icon: Construction
    },
    {
      id: 'floor-restoration',
      title: 'Commercial Floor Care',
      description: 'Floor cleaning, waxing, and restoration services',
      serviceType: 'floor',
      propertyType: 'commercial',
      price: 'From $249',
      rating: 4.6,
      availability: 'Weekend preferred',
      route: '/service-form/floor',
      icon: Hammer
    },
    {
      id: 'tile-grout',
      title: 'Tile & Grout Cleaning',
      description: 'Professional tile and grout deep cleaning service',
      serviceType: 'tile',
      propertyType: 'commercial',
      price: 'From $149',
      rating: 4.5,
      availability: 'Available this week',
      route: '/service-form/tile',
      icon: Grid
    }
  ];

  // Search suggestions database
  const suggestionDatabase: SearchSuggestion[] = [
    // Service suggestions
    { id: '1', text: 'house cleaning', type: 'service', serviceType: 'regular', propertyType: 'residential', popularity: 95 },
    { id: '2', text: 'deep cleaning', type: 'service', serviceType: 'deep', propertyType: 'residential', popularity: 90 },
    { id: '3', text: 'office cleaning', type: 'service', serviceType: 'office', propertyType: 'commercial', popularity: 85 },
    { id: '4', text: 'carpet cleaning', type: 'service', serviceType: 'carpet', propertyType: 'residential', popularity: 80 },
    { id: '5', text: 'window cleaning', type: 'service', serviceType: 'window', propertyType: 'residential', popularity: 75 },
    { id: '6', text: 'pressure washing', type: 'service', serviceType: 'pressure', propertyType: 'residential', popularity: 70 },
    { id: '7', text: 'sanitization service', type: 'service', serviceType: 'sanitization', propertyType: 'residential', popularity: 85 },
    { id: '8', text: 'pool cleaning', type: 'service', serviceType: 'pool', propertyType: 'residential', popularity: 60 },
    { id: '9', text: 'chimney cleaning', type: 'service', serviceType: 'chimney', propertyType: 'residential', popularity: 55 },
    { id: '10', text: 'post construction cleanup', type: 'service', serviceType: 'construction', propertyType: 'commercial', popularity: 65 },
    
    // Popular searches
    { id: 'p1', text: 'weekly house cleaning service near me', type: 'popular', popularity: 95 },
    { id: 'p2', text: 'emergency cleaning service', type: 'popular', popularity: 70 },
    { id: 'p3', text: 'move in cleaning', type: 'popular', popularity: 80 },
    { id: 'p4', text: 'apartment cleaning', type: 'popular', popularity: 75 },
    { id: 'p5', text: 'commercial office cleaning', type: 'popular', popularity: 65 },
    
    // Location-based suggestions
    { id: 'l1', text: 'cleaning services in New York', type: 'location', popularity: 90 },
    { id: 'l2', text: 'cleaning services in Los Angeles', type: 'location', popularity: 85 },
    { id: 'l3', text: 'cleaning services in Chicago', type: 'location', popularity: 80 },
  ];

  // Voice search setup
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      setVoiceState(prev => ({ ...prev, isSupported: true }));
    }
  }, []);

  // Load search history
  useEffect(() => {
    const saved = localStorage.getItem('searchHistory');
    if (saved) {
      setSearchHistory(JSON.parse(saved));
    }
    
    const savedZip = localStorage.getItem('userZipCode');
    if (savedZip) {
      setZipCode(savedZip);
    }
  }, []);

  // Intelligent search with fuzzy matching
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setSuggestions([]);
      return;
    }

    setIsSearching(true);
    
    // Simulate search delay for realism
    await new Promise(resolve => setTimeout(resolve, 150));
    
    const lowerQuery = searchQuery.toLowerCase();
    
    // Fuzzy matching algorithm
    const fuzzyMatch = (text: string, query: string): number => {
      const textLower = text.toLowerCase();
      const queryLower = query.toLowerCase();
      
      // Exact match
      if (textLower.includes(queryLower)) return 100;
      
      // Word matching
      const queryWords = queryLower.split(' ');
      const textWords = textLower.split(' ');
      let wordMatches = 0;
      
      queryWords.forEach(qWord => {
        textWords.forEach(tWord => {
          if (tWord.includes(qWord) || qWord.includes(tWord)) {
            wordMatches++;
          }
        });
      });
      
      return (wordMatches / queryWords.length) * 75;
    };

    // Search results with scoring
    const searchResults = serviceDatabase
      .map(service => ({
        ...service,
        score: Math.max(
          fuzzyMatch(service.title, lowerQuery),
          fuzzyMatch(service.description, lowerQuery),
          fuzzyMatch(service.serviceType, lowerQuery)
        )
      }))
      .filter(service => {
        // Apply filters
        if (filters.propertyType !== 'all' && service.propertyType !== filters.propertyType) return false;
        if (filters.serviceCategory !== 'all') {
          const categories = {
            'cleaning': ['regular', 'deep', 'carpet', 'window'],
            'maintenance': ['pressure', 'pool', 'chimney', 'floor'],
            'specialized': ['sanitization', 'construction', 'tile']
          };
          if (!categories[filters.serviceCategory]?.includes(service.serviceType)) return false;
        }
        return service.score > 20; // Minimum relevance threshold
      })
      .sort((a, b) => {
        // Sort by score, then by popularity
        if (b.score !== a.score) return b.score - a.score;
        if (a.popular && !b.popular) return -1;
        if (b.popular && !a.popular) return 1;
        return b.rating - a.rating;
      })
      .slice(0, 8);

    setResults(searchResults);
    
    // Generate suggestions
    const searchSuggestions = suggestionDatabase
      .map(suggestion => ({
        ...suggestion,
        score: fuzzyMatch(suggestion.text, lowerQuery)
      }))
      .filter(suggestion => suggestion.score > 30)
      .sort((a, b) => b.score - a.score)
      .slice(0, 6);

    setSuggestions(searchSuggestions);
    setIsSearching(false);
    setShowResults(searchResults.length > 0);
  }, [filters]);

  // Real-time search as user types
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (query.length > 0) {
        performSearch(query);
        setShowSuggestions(true);
      } else {
        setResults([]);
        setSuggestions([]);
        setShowSuggestions(false);
        setShowResults(false);
      }
    }, 200);

    return () => clearTimeout(debounceTimer);
  }, [query, performSearch]);

  // Voice search functionality
  const startVoiceSearch = () => {
    if (!voiceState.isSupported) return;

    const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.continuous = false;
    recognition.interimResults = true;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setVoiceState(prev => ({ ...prev, isListening: true }));
    };

    recognition.onresult = (event: any) => {
      const result = event.results[event.results.length - 1];
      const transcript = result[0].transcript;
      const confidence = result[0].confidence;
      
      setVoiceState(prev => ({ 
        ...prev, 
        transcript, 
        confidence: confidence * 100 
      }));
      
      if (result.isFinal) {
        setQuery(transcript);
        setVoiceState(prev => ({ ...prev, isListening: false }));
      }
    };

    recognition.onerror = () => {
      setVoiceState(prev => ({ ...prev, isListening: false }));
    };

    recognition.onend = () => {
      setVoiceState(prev => ({ ...prev, isListening: false }));
    };

    recognition.start();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions && !showResults) return;

    const totalItems = suggestions.length + results.length;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => (prev + 1) % totalItems);
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev <= 0 ? totalItems - 1 : prev - 1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0) {
          if (selectedIndex < suggestions.length) {
            const suggestion = suggestions[selectedIndex];
            setQuery(suggestion.text);
            performSearch(suggestion.text);
          } else {
            const result = results[selectedIndex - suggestions.length];
            navigate(result.route);
          }
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setShowResults(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // Save search to history
  const saveToHistory = (searchQuery: string) => {
    const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 10);
    setSearchHistory(newHistory);
    localStorage.setItem('searchHistory', JSON.stringify(newHistory));
  };

  // Handle search submission
  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query;
    if (!finalQuery.trim()) return;
    
    saveToHistory(finalQuery);
    
    if (results.length > 0) {
      navigate(results[0].route);
    } else {
      // Fallback to general search
      const bestService = selectBestService([finalQuery], { propertyType: 'residential' });
      navigateToServiceWithPropertyType(navigate, bestService, 'residential');
    }
  };

  return (
    <div ref={searchRef} className="w-full max-w-4xl mx-auto">
      {/* Main Search Container */}
      <div className="relative">
        {/* Search Input */}
        <div className="flex items-center bg-white rounded-full shadow-lg border border-gray-200 
                      hover:shadow-xl transition-shadow duration-300 overflow-hidden">
          <div className="flex-1 flex items-center">
            <Search className="w-5 h-5 text-gray-400 ml-4" />
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setShowSuggestions(query.length > 0)}
              placeholder="Search for cleaning services..."
              className="flex-1 px-4 py-4 text-base focus:outline-none placeholder-gray-500"
            />
            
            {/* Voice Search Button */}
            {voiceState.isSupported && (
              <button
                type="button"
                onClick={startVoiceSearch}
                disabled={voiceState.isListening}
                className={`p-2 mr-2 rounded-full transition-colors ${
                  voiceState.isListening 
                    ? 'bg-red-100 text-red-600 animate-pulse' 
                    : 'hover:bg-gray-100 text-gray-400 hover:text-gray-600'
                }`}
                title="Voice search"
              >
                {voiceState.isListening ? <MicOff className="w-5 h-5" /> : <Mic className="w-5 h-5" />}
              </button>
            )}
            
            {/* Clear Button */}
            {query && (
              <button
                type="button"
                onClick={() => {
                  setQuery('');
                  setShowSuggestions(false);
                  setShowResults(false);
                }}
                className="p-2 mr-2 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
          
          {/* Filter Button */}
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className={`p-3 border-l border-gray-200 hover:bg-gray-50 transition-colors ${
              showFilters ? 'bg-brand-50 text-brand-600' : 'text-gray-400'
            }`}
            title="Search filters"
          >
            <Filter className="w-5 h-5" />
          </button>
          
          {/* Location Input */}
          <div className="flex items-center border-l border-gray-200 bg-gray-50 px-3">
            <MapPin className="w-4 h-4 text-gray-400 mr-2" />
            <input
              type="text"
              value={zipCode}
              onChange={(e) => setZipCode(e.target.value.replace(/\D/g, '').slice(0, 5))}
              placeholder="ZIP"
              className="w-20 py-4 text-sm bg-transparent focus:outline-none text-center"
              maxLength={5}
            />
          </div>
          
          {/* Search Button */}
          <button
            type="button"
            onClick={() => handleSearch()}
            disabled={!query.trim()}
            className="px-6 py-4 bg-brand-600 text-white hover:bg-brand-700 
                     disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Search className="w-5 h-5" />
          </button>
        </div>

        {/* Voice Recognition Feedback */}
        {voiceState.isListening && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute top-full left-0 right-0 mt-2 bg-red-50 border border-red-200 
                     rounded-lg p-3 flex items-center gap-2"
          >
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
            <span className="text-sm text-red-700">Listening... Speak now</span>
            {voiceState.transcript && (
              <span className="text-sm text-gray-600 ml-2">"{voiceState.transcript}"</span>
            )}
          </motion.div>
        )}

        {/* Search Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg 
                       border border-gray-200 p-4 z-50"
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {/* Property Type Filter */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Property Type</label>
                  <select
                    value={filters.propertyType}
                    onChange={(e) => setFilters(prev => ({ ...prev, propertyType: e.target.value as any }))}
                    className="w-full text-sm border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-brand-500"
                  >
                    <option value="all">All Properties</option>
                    <option value="residential">Residential</option>
                    <option value="commercial">Commercial</option>
                  </select>
                </div>
                
                {/* Price Range Filter */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Price Range</label>
                  <select
                    value={filters.priceRange}
                    onChange={(e) => setFilters(prev => ({ ...prev, priceRange: e.target.value as any }))}
                    className="w-full text-sm border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-brand-500"
                  >
                    <option value="all">Any Price</option>
                    <option value="budget">Budget ($50-$100)</option>
                    <option value="standard">Standard ($100-$200)</option>
                    <option value="premium">Premium ($200+)</option>
                  </select>
                </div>
                
                {/* Availability Filter */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Availability</label>
                  <select
                    value={filters.availability}
                    onChange={(e) => setFilters(prev => ({ ...prev, availability: e.target.value as any }))}
                    className="w-full text-sm border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-brand-500"
                  >
                    <option value="all">Any Time</option>
                    <option value="today">Today</option>
                    <option value="this-week">This Week</option>
                    <option value="flexible">Flexible</option>
                  </select>
                </div>
                
                {/* Service Category Filter */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={filters.serviceCategory}
                    onChange={(e) => setFilters(prev => ({ ...prev, serviceCategory: e.target.value as any }))}
                    className="w-full text-sm border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-brand-500"
                  >
                    <option value="all">All Services</option>
                    <option value="cleaning">Regular Cleaning</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="specialized">Specialized</option>
                  </select>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Search Suggestions & Results */}
        <AnimatePresence>
          {(showSuggestions || showResults) && (suggestions.length > 0 || results.length > 0) && (
            <motion.div
              ref={resultsRef}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl 
                       border border-gray-200 overflow-hidden z-50 max-h-96 overflow-y-auto"
            >
              {/* Suggestions Section */}
              {suggestions.length > 0 && (
                <div className="border-b border-gray-100">
                  <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 flex items-center gap-2">
                    <Zap className="w-3 h-3" />
                    Suggestions
                  </div>
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={suggestion.id}
                      type="button"
                      onClick={() => {
                        setQuery(suggestion.text);
                        performSearch(suggestion.text);
                      }}
                      className={`w-full px-4 py-2 text-left hover:bg-gray-50 transition-colors
                               flex items-center gap-3 ${
                                 index === selectedIndex ? 'bg-brand-50 border-r-2 border-brand-500' : ''
                               }`}
                    >
                      <Search className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-700">{suggestion.text}</span>
                      {suggestion.type === 'popular' && (
                        <TrendingUp className="w-3 h-3 text-orange-500 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              )}

              {/* Results Section */}
              {results.length > 0 && (
                <div>
                  <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 flex items-center gap-2">
                    <Target className="w-3 h-3" />
                    Services ({results.length} found)
                  </div>
                  {results.map((result, index) => {
                    const Icon = result.icon;
                    const actualIndex = suggestions.length + index;
                    return (
                      <button
                        key={result.id}
                        type="button"
                        onClick={() => navigate(result.route)}
                        className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors
                                 border-b border-gray-50 last:border-b-0 ${
                                   actualIndex === selectedIndex ? 'bg-brand-50 border-r-2 border-brand-500' : ''
                                 }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            result.propertyType === 'residential' ? 'bg-brand-50' : 'bg-blue-50'
                          }`}>
                            <Icon className={`w-5 h-5 ${
                              result.propertyType === 'residential' ? 'text-brand-600' : 'text-blue-600'
                            }`} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-gray-900 truncate">{result.title}</h4>
                              {result.popular && (
                                <span className="text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded">
                                  Popular
                                </span>
                              )}
                              {result.emergency && (
                                <span className="text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded">
                                  Emergency
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 truncate">{result.description}</p>
                            <div className="flex items-center gap-4 mt-1">
                              <span className="text-sm font-medium text-gray-900">{result.price}</span>
                              <div className="flex items-center gap-1">
                                <Star className="w-3 h-3 text-yellow-400 fill-current" />
                                <span className="text-xs text-gray-600">{result.rating}</span>
                              </div>
                              <span className="text-xs text-gray-500">{result.availability}</span>
                            </div>
                          </div>
                          <ChevronRight className="w-4 h-4 text-gray-400" />
                        </div>
                      </button>
                    );
                  })}
                </div>
              )}

              {/* No Results */}
              {isSearching && (
                <div className="px-4 py-8 text-center">
                  <Loader2 className="w-6 h-6 text-brand-600 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Searching...</p>
                </div>
              )}
              
              {!isSearching && query && suggestions.length === 0 && results.length === 0 && (
                <div className="px-4 py-8 text-center">
                  <AlertCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">No services found for "{query}"</p>
                  <button
                    type="button"
                    onClick={() => handleSearch()}
                    className="text-xs text-brand-600 hover:text-brand-700"
                  >
                    Search anyway →
                  </button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Quick Access & Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="mt-6 flex flex-wrap items-center justify-center gap-6 text-sm text-gray-600"
      >
        <span className="flex items-center gap-1">
          <Users className="w-4 h-4" />
          <span className="font-semibold text-gray-900">4.5M+</span> customers served
        </span>
        <span className="flex items-center gap-1">
          <Star className="w-4 h-4 text-yellow-400 fill-current" />
          <span className="font-semibold text-gray-900">4.9/5</span> avg rating
        </span>
        <span className="flex items-center gap-1">
          <Zap className="w-4 h-4 text-brand-600" />
          <span>Instant quotes</span>
        </span>
        <span className="flex items-center gap-1">
          <CheckCircle className="w-4 h-4 text-green-600" />
          <span>Verified professionals</span>
        </span>
      </motion.div>
    </div>
  );
} 
