import React from 'react';
import { motion } from 'framer-motion';
import { Sparkles, CheckCircle } from 'lucide-react';

export function SuccessAnimation() {
  return (
    <div className="relative h-48 overflow-hidden">
      {/* Background gradient animation */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-brand-500/20 via-brand-400/10 to-brand-500/20"
        animate={{
          backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "linear"
        }}
      />

      {/* Success checkmark animation */}
      <motion.div
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20
        }}
      >
        <div className="relative">
          {/* Outer ring */}
          <motion.div
            className="absolute -inset-4 rounded-full bg-green-500/20"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
          
          {/* Inner circle */}
          <div className="relative bg-green-100 rounded-full p-6">
            <CheckCircle className="w-12 h-12 text-green-600" />
          </div>
        </div>
      </motion.div>

      {/* Floating sparkles */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute"
          initial={{ 
            x: Math.random() * 100 - 50,
            y: Math.random() * 100 - 50,
            scale: 0,
            opacity: 0 
          }}
          animate={{ 
            scale: [0, 1, 0],
            opacity: [0, 1, 0],
            y: [0, -50, -100]
          }}
          transition={{
            duration: 2,
            delay: i * 0.2,
            repeat: Infinity,
            repeatDelay: 1
          }}
        >
          <Sparkles className="w-6 h-6 text-brand-400" />
        </motion.div>
      ))}
    </div>
  );
}
