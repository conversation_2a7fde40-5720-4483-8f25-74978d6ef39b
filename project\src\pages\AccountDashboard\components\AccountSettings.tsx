import React from 'react';
import { motion } from 'framer-motion';
import { Phone, CreditCard, Shield, Bell } from 'lucide-react';

const cardVariants = { 
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } }
};

const cardHover = { scale: 1.02, transition: { duration: 0.2 }};

export function AccountSettings() {
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={{ visible: { transition: { staggerChildren: 0.07 } } }}
      className="space-y-4 md:space-y-6"
    >
      {/* Profile Section */}
      <motion.div variants={cardVariants} whileHover={cardHover} className="p-4 md:p-8 bg-white/5 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl shadow-2xl shadow-black/30">
        <h2 className="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">Profile Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <InputField label="Full Name" placeholder="John Doe" />
          <InputField label="Email Address" placeholder="<EMAIL>" type="email" />
          <InputField label="Phone Number" placeholder="+****************" type="tel" />
          <InputField label="Home Address" placeholder="123 Main St, Anytown, USA" />
        </div>
        <motion.button
          className="mt-4 md:mt-6 px-4 md:px-6 py-2 md:py-2.5 bg-white/10 text-white font-semibold text-sm md:text-base rounded-lg md:rounded-xl border border-white/20 hover:bg-white/20 transition-colors touch-manipulation"
          whileTap={{ scale: 0.95 }}
        >
          Save Changes
        </motion.button>
      </motion.div>

      {/* Preferences Section */}
      <motion.div variants={cardVariants} whileHover={cardHover} className="p-4 md:p-8 bg-white/5 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl shadow-2xl shadow-black/30">
        <h2 className="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">Notification Preferences</h2>
        <div className="space-y-3 md:space-y-4">
          <PreferenceItem icon={<Bell size={18} />} title="Email Notifications" description="Receive updates about your bookings." />
          <PreferenceItem icon={<Phone size={18} />} title="SMS Reminders" description="Get text messages before your appointment." />
        </div>
      </motion.div>

      {/* Security Section */}
      <motion.div variants={cardVariants} whileHover={cardHover} className="p-4 md:p-8 bg-white/5 backdrop-blur-2xl border border-white/10 rounded-2xl md:rounded-3xl shadow-2xl shadow-black/30">
        <h2 className="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">Account Security</h2>
        <div className="space-y-3 md:space-y-4">
          <SecurityOption icon={<Shield size={18} />} title="Change Password" description="Update the password for your account." />
          <SecurityOption icon={<CreditCard size={18} />} title="Payment Methods" description="Manage your saved payment options." />
        </div>
      </motion.div>
    </motion.div>
  );
}

const InputField = ({ label, ...props }: { label: string } & React.InputHTMLAttributes<HTMLInputElement>) => (
  <div>
    <label className="block text-xs md:text-sm font-medium text-white/60 mb-1 md:mb-2">{label}</label>
    <input
      {...props}
      className="w-full px-3 md:px-4 py-2 md:py-3 bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg md:rounded-xl text-sm md:text-base text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:bg-white/10 transition-all touch-manipulation"
    />
  </div>
);

const PreferenceItem = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => (
  <div className="flex items-center justify-between p-3 md:p-4 bg-white/5 rounded-xl md:rounded-2xl border border-white/10">
    <div className="flex items-center space-x-3 md:space-x-4 min-w-0 flex-1">
      <div className="text-emerald-400 flex-shrink-0">{icon}</div>
      <div className="min-w-0 flex-1">
        <p className="font-semibold text-sm md:text-base text-white truncate">{title}</p>
        <p className="text-xs md:text-sm text-white/60 truncate">{description}</p>
      </div>
    </div>
    <label className="relative inline-flex items-center cursor-pointer flex-shrink-0 ml-2 touch-manipulation">
      <input type="checkbox" className="sr-only peer" defaultChecked />
      <div className="w-10 h-5 md:w-11 md:h-6 bg-white/10 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border after:rounded-full after:h-4 after:w-4 md:after:h-5 md:after:w-5 after:transition-all peer-checked:bg-gradient-to-r from-emerald-500 to-green-500"></div>
    </label>
  </div>
);

const SecurityOption = ({ icon, title, description }: { icon: React.ReactNode; title: string; description: string }) => (
  <button className="w-full p-3 md:p-4 bg-white/5 rounded-xl md:rounded-2xl border border-white/10 hover:bg-white/10 hover:border-emerald-400/30 transition-colors text-left group touch-manipulation">
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3 md:space-x-4 min-w-0 flex-1">
        <div className="text-emerald-400 group-hover:text-emerald-300 transition-colors flex-shrink-0">{icon}</div>
        <div className="min-w-0 flex-1">
          <p className="font-semibold text-sm md:text-base text-white truncate">{title}</p>
          <p className="text-xs md:text-sm text-white/60 truncate">{description}</p>
        </div>
      </div>
      <div className="text-white/40 group-hover:text-white transition-colors flex-shrink-0 ml-2 text-lg">→</div>
    </div>
  </button>
);
