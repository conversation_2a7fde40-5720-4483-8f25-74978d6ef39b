#!/usr/bin/env node

/**
 * Payment System Test Setup Validator
 * 
 * This script validates that the test environment is properly configured
 * and runs a quick smoke test to ensure all components are working.
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  issues: [],
  recommendations: []
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
  results.passed++;
}

function error(message) {
  log(`❌ ${message}`, 'red');
  results.failed++;
  results.issues.push(message);
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
  results.warnings++;
}

function info(message) {
  log(`ℹ️  ${message}`, 'cyan');
}

function header(message) {
  log(`\n${'='.repeat(60)}`, 'magenta');
  log(message, 'magenta');
  log('='.repeat(60), 'magenta');
}

function subHeader(message) {
  log(`\n${'-'.repeat(40)}`, 'blue');
  log(message, 'blue');
  log('-'.repeat(40), 'blue');
}

function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    success(`${description} exists: ${filePath}`);
    return true;
  } else {
    error(`${description} missing: ${filePath}`);
    return false;
  }
}

function checkEnvironmentVariables() {
  subHeader('Environment Variables');
  
  const requiredVars = [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_SQUARE_APPLICATION_ID',
    'VITE_SQUARE_ACCESS_TOKEN',
    'VITE_SQUARE_LOCATION_ID',
    'VITE_SQUARE_ENVIRONMENT'
  ];
  
  // Check .env file
  const envPath = '.env';
  if (fs.existsSync(envPath)) {
    success('.env file found');
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        envVars[key.trim()] = value.trim();
      }
    });
    
    requiredVars.forEach(varName => {
      if (envVars[varName]) {
        success(`${varName} is configured`);
      } else {
        error(`${varName} is missing from .env`);
        results.recommendations.push(`Add ${varName} to your .env file`);
      }
    });
  } else {
    error('.env file not found');
    results.recommendations.push('Create a .env file with required environment variables');
  }
}

function checkPackageJson() {
  subHeader('Package Configuration');
  
  const packagePath = 'package.json';
  if (!checkFileExists(packagePath, 'package.json')) {
    return;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    // Check test scripts
    const requiredScripts = [
      'test:payment',
      'test:payment:unit',
      'test:payment:integration',
      'test:payment:e2e',
      'test:payment:performance',
      'test:payment:security'
    ];
    
    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        success(`Test script '${script}' is configured`);
      } else {
        warning(`Test script '${script}' is missing`);
        results.recommendations.push(`Add '${script}' script to package.json`);
      }
    });
    
    // Check dependencies
    const requiredDeps = {
      'vitest': 'devDependencies',
      '@vitest/ui': 'devDependencies',
      '@vitest/coverage-v8': 'devDependencies',
      '@testing-library/react': 'devDependencies',
      '@testing-library/jest-dom': 'devDependencies',
      'react': 'dependencies',
      '@supabase/supabase-js': 'dependencies',
      'square': 'dependencies'
    };
    
    Object.entries(requiredDeps).forEach(([dep, type]) => {
      const deps = packageJson[type] || {};
      if (deps[dep]) {
        success(`${dep} is installed (${type})`);
      } else {
        error(`${dep} is missing from ${type}`);
        results.recommendations.push(`Install ${dep}: npm install ${type === 'devDependencies' ? '--save-dev' : ''} ${dep}`);
      }
    });
    
  } catch (err) {
    error(`Failed to parse package.json: ${err.message}`);
  }
}

function checkTestFiles() {
  subHeader('Test Files');
  
  const testFiles = [
    'src/tests/e2e-payment-booking-flow.test.ts',
    'src/tests/input-validation.test.ts',
    'src/tests/error-handling.test.ts',
    'src/tests/payment-performance.test.ts',
    'src/tests/payment-system-test-runner.ts'
  ];
  
  testFiles.forEach(file => {
    checkFileExists(file, 'Test file');
  });
  
  // Check test runner script
  checkFileExists('run-payment-tests.ps1', 'PowerShell test runner');
}

function checkProjectStructure() {
  subHeader('Project Structure');
  
  const requiredDirs = [
    'src',
    'src/pages',
    'src/lib/services',
    'src/lib',
    'src/tests'
  ];
  
  requiredDirs.forEach(dir => {
    if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
      success(`Directory exists: ${dir}`);
    } else {
      error(`Directory missing: ${dir}`);
    }
  });
  
  // Check key source files
  const keyFiles = [
    'src/lib/services/paymentSynchronizer.ts',
    'src/lib/supabase/client.ts',
    'src/pages/Residential/forms/RegularCleaning/RegularCleaningForm.tsx',
    'src/pages/Residential/forms/DeepCleaning/BrandAlignedDeepCleaningForm.tsx',
    'src/pages/Commercial/forms/BrandAlignedCorporateForm.tsx',
    'src/pages/Residential/forms/PostConstruction/BrandAlignedPostConstructionForm.tsx',
    'src/pages/Residential/forms/MoveOutCleaning/ModernMoveOutCleaningForm.tsx'
  ];
  
  keyFiles.forEach(file => {
    checkFileExists(file, 'Source file');
  });
}

function checkNodeEnvironment() {
  subHeader('Node.js Environment');
  
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    
    success(`Node.js version: ${nodeVersion}`);
    success(`npm version: ${npmVersion}`);
    
    // Check if Node version is compatible
    const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0]);
    if (majorVersion >= 18) {
      success('Node.js version is compatible (>=18)');
    } else {
      warning(`Node.js version ${nodeVersion} may not be compatible. Recommended: v18+`);
      results.recommendations.push('Upgrade to Node.js v18 or higher');
    }
    
  } catch (err) {
    error('Node.js or npm not found');
    results.recommendations.push('Install Node.js from https://nodejs.org/');
  }
}

function runSmokeTest() {
  subHeader('Smoke Test');
  
  try {
    // Check if vitest is available
    execSync('npx vitest --version', { encoding: 'utf8', stdio: 'pipe' });
    success('Vitest is available');
    
    // Try to run a simple test validation
    info('Running basic test validation...');
    
    // Create a simple test file for validation
    const testContent = `
import { describe, it, expect } from 'vitest';

describe('Smoke Test', () => {
  it('should validate test environment', () => {
    expect(true).toBe(true);
    expect(process.env.NODE_ENV).toBeDefined();
  });
  
  it('should have access to required globals', () => {
    expect(global).toBeDefined();
    expect(console).toBeDefined();
  });
});
`;
    
    const smokeTestPath = 'smoke-test.temp.js';
    fs.writeFileSync(smokeTestPath, testContent);
    
    try {
      execSync(`npx vitest run ${smokeTestPath}`, { encoding: 'utf8', stdio: 'pipe' });
      success('Smoke test passed');
    } catch (testErr) {
      warning('Smoke test failed - check vitest configuration');
    } finally {
      // Clean up
      if (fs.existsSync(smokeTestPath)) {
        fs.unlinkSync(smokeTestPath);
      }
    }
    
  } catch (err) {
    error('Vitest not available');
    results.recommendations.push('Install vitest: npm install --save-dev vitest');
  }
}

function generateReport() {
  header('Validation Summary');
  
  const total = results.passed + results.failed + results.warnings;
  const successRate = total > 0 ? Math.round((results.passed / total) * 100) : 0;
  
  log(`\n📊 Results:`);
  log(`   Total Checks: ${total}`);
  log(`   Passed: ${results.passed}`, 'green');
  log(`   Failed: ${results.failed}`, 'red');
  log(`   Warnings: ${results.warnings}`, 'yellow');
  log(`   Success Rate: ${successRate}%`, successRate >= 80 ? 'green' : successRate >= 60 ? 'yellow' : 'red');
  
  if (results.issues.length > 0) {
    log(`\n🚨 Critical Issues:`, 'red');
    results.issues.forEach(issue => {
      log(`   - ${issue}`, 'red');
    });
  }
  
  if (results.recommendations.length > 0) {
    log(`\n💡 Recommendations:`, 'cyan');
    results.recommendations.forEach(rec => {
      log(`   - ${rec}`, 'cyan');
    });
  }
  
  log(`\n🎯 Next Steps:`, 'blue');
  if (results.failed > 0) {
    log('   1. Fix critical issues listed above', 'blue');
    log('   2. Re-run this validator', 'blue');
    log('   3. Run payment tests when all issues are resolved', 'blue');
  } else {
    log('   1. Run payment tests: npm run test:payment', 'blue');
    log('   2. Deploy edge functions to Supabase cloud', 'blue');
    log('   3. Test payment flow manually in browser', 'blue');
  }
  
  // Determine overall status
  let status;
  if (results.failed === 0 && results.warnings <= 2) {
    status = '🟢 READY';
  } else if (results.failed <= 2) {
    status = '🟡 NEEDS ATTENTION';
  } else {
    status = '🔴 NOT READY';
  }
  
  log(`\n🏁 Status: ${status}`, 'bright');
  
  return results.failed === 0;
}

// Main execution
function main() {
  header('Payment System Test Setup Validator');
  
  info('Validating test environment setup...');
  
  checkNodeEnvironment();
  checkProjectStructure();
  checkPackageJson();
  checkEnvironmentVariables();
  checkTestFiles();
  runSmokeTest();
  
  const isReady = generateReport();
  
  // Exit with appropriate code
  process.exit(isReady ? 0 : 1);
}

// Run if called directly
if (process.argv[1] && import.meta.url.includes(path.basename(process.argv[1]))) {
  main();
}

export {
  main,
  checkEnvironmentVariables,
  checkPackageJson,
  checkTestFiles,
  checkProjectStructure,
  checkNodeEnvironment,
  runSmokeTest
};