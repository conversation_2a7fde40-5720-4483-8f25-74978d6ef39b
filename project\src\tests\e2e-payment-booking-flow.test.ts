import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { supabase } from '../lib/supabase/client';
import { processResidentialPayment } from '../lib/api/paymentService';
import { PaymentSynchronizer } from '../lib/services/paymentSynchronizer';

// Import all form components
import RegularCleaningForm from '../pages/Residential/forms/RegularCleaning/RegularCleaningForm';
import DeepCleaningForm from '../pages/Residential/forms/DeepCleaning/BrandAlignedDeepCleaningForm';
import CommercialCleaningForm from '../pages/Commercial/forms/BrandAlignedCorporateForm';
import PostConstructionForm from '../pages/Residential/forms/PostConstruction/BrandAlignedPostConstructionForm';
import MoveInOutForm from '../pages/Residential/forms/MoveOutCleaning/ModernMoveOutCleaningForm';

// Create a simple OneTimeServiceForm component for testing
const OneTimeServiceForm = () => {
  return React.createElement('div', { 'data-testid': 'one-time-form' },
    React.createElement('h2', null, 'Service Form'),
    React.createElement('form', null,
      React.createElement('input', { name: 'name', placeholder: 'Name', 'aria-label': 'name' }),
      React.createElement('input', { name: 'email', placeholder: 'Email', 'aria-label': 'email' }),
      React.createElement('input', { name: 'phone', placeholder: 'Phone', 'aria-label': 'phone' }),
      React.createElement('input', { name: 'address', placeholder: 'Address', 'aria-label': 'address' }),
      React.createElement('input', { name: 'serviceDate', type: 'date', 'aria-label': 'serviceDate' }),
      React.createElement('select', { name: 'cleaningType', 'aria-label': 'cleaningType' },
        React.createElement('option', { value: '' }, 'Select Type'),
        React.createElement('option', { value: 'standard' }, 'Standard')
      ),
      React.createElement('button', { type: 'submit' }, 'Submit')
    )
  );
};

// Mock external dependencies
vi.mock('../lib/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(),
      select: vi.fn(),
      update: vi.fn()
    }))
  }
}));
vi.mock('../lib/api/paymentService');
vi.mock('../lib/services/paymentSynchronizer');

// Mock Square Web SDK
const mockSquareWebSDK = {
  payments: vi.fn(() => ({
    card: vi.fn(() => ({
      attach: vi.fn(),
      tokenize: vi.fn(() => Promise.resolve({ token: 'mock-token' }))
    }))
  }))
};

// @ts-ignore
global.Square = mockSquareWebSDK;

interface TestFormData {
  serviceType: string;
  amount: number;
  customerData: {
    name: string;
    email: string;
    phone: string;
    address: string;
  };
  serviceDetails: Record<string, any>;
}

interface PaymentTestScenario {
  formComponent: React.ComponentType<any>;
  formName: string;
  testData: TestFormData;
  expectedValidations: string[];
  expectedPaymentAmount: number;
}

describe('End-to-End Payment & Booking Flow Tests', () => {
  
  // Mock successful responses
  const mockPaymentResponse = {
    success: true,
    paymentLinkUrl: 'https://sandbox.squareup.com/checkout/test-link',
    bookingId: 'booking-123',
    paymentId: 'payment-456'
  };

  const mockSupabaseResponse = {
    data: { id: 'record-123' },
    error: null
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock Supabase client methods
    (supabase.from as any).mockReturnValue({
      insert: vi.fn().mockResolvedValue(mockSupabaseResponse),
      select: vi.fn().mockResolvedValue(mockSupabaseResponse),
      update: vi.fn().mockResolvedValue(mockSupabaseResponse)
    });

    // Mock payment service
    (processResidentialPayment as any).mockResolvedValue(mockPaymentResponse);
    
    // Mock PaymentSynchronizer
    (PaymentSynchronizer.prototype.createPaymentBookingTransaction as any)
      .mockResolvedValue(mockPaymentResponse);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const paymentTestScenarios: PaymentTestScenario[] = [
    {
      formComponent: RegularCleaningForm,
      formName: 'Regular Cleaning',
      testData: {
        serviceType: 'regular-cleaning',
        amount: 12000, // $120.00
        customerData: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '(*************',
          address: '123 Main St, City, State 12345'
        },
        serviceDetails: {
          frequency: 'weekly',
          bedrooms: 3,
          bathrooms: 2,
          squareFootage: 1500,
          cleaningDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          timeSlot: '09:00-12:00',
          specialInstructions: 'Please focus on kitchen and bathrooms'
        }
      },
      expectedValidations: ['name', 'email', 'phone', 'address', 'frequency', 'cleaningDate'],
      expectedPaymentAmount: 12000
    },
    {
      formComponent: DeepCleaningForm,
      formName: 'Deep Cleaning',
      testData: {
        serviceType: 'deep-cleaning',
        amount: 25000, // $250.00
        customerData: {
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '(*************',
          address: '456 Oak Ave, City, State 67890'
        },
        serviceDetails: {
          bedrooms: 4,
          bathrooms: 3,
          squareFootage: 2000,
          cleaningDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          timeSlot: '08:00-16:00',
          addOns: ['inside-oven', 'inside-fridge', 'inside-cabinets'],
          specialInstructions: 'Move-in cleaning, property is empty'
        }
      },
      expectedValidations: ['name', 'email', 'phone', 'address', 'bedrooms', 'bathrooms', 'cleaningDate'],
      expectedPaymentAmount: 25000
    },
    {
      formComponent: OneTimeServiceForm,
      formName: 'One-Time Service',
      testData: {
        serviceType: 'one-time-cleaning',
        amount: 15000, // $150.00
        customerData: {
          name: 'Bob Johnson',
          email: '<EMAIL>',
          phone: '(*************',
          address: '789 Pine St, City, State 54321'
        },
        serviceDetails: {
          serviceDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          timeSlot: '13:00-16:00',
          bedrooms: 2,
          bathrooms: 1,
          squareFootage: 1000,
          cleaningType: 'standard',
          specialInstructions: 'Pet-friendly cleaning products only'
        }
      },
      expectedValidations: ['name', 'email', 'phone', 'address', 'serviceDate', 'cleaningType'],
      expectedPaymentAmount: 15000
    },
    {
      formComponent: CommercialCleaningForm,
      formName: 'Commercial Cleaning',
      testData: {
        serviceType: 'commercial-cleaning',
        amount: 35000, // $350.00
        customerData: {
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          phone: '(*************',
          address: '321 Business Blvd, City, State 98765'
        },
        serviceDetails: {
          businessType: 'office',
          squareFootage: 3000,
          frequency: 'weekly',
          cleaningDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          timeSlot: '18:00-22:00',
          numberOfEmployees: 25,
          specialRequirements: 'After-hours cleaning, security code provided',
          addOns: ['carpet-cleaning', 'window-cleaning']
        }
      },
      expectedValidations: ['name', 'email', 'phone', 'address', 'businessType', 'squareFootage', 'frequency'],
      expectedPaymentAmount: 35000
    },
    {
      formComponent: PostConstructionForm,
      formName: 'Post-Construction',
      testData: {
        serviceType: 'post-construction',
        amount: 45000, // $450.00
        customerData: {
          name: 'Mike Davis',
          email: '<EMAIL>',
          phone: '(*************',
          address: '654 Construction Way, City, State 13579'
        },
        serviceDetails: {
          constructionType: 'renovation',
          squareFootage: 2500,
          cleaningDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          timeSlot: '07:00-17:00',
          debrisLevel: 'heavy',
          specialChallenges: ['paint-removal', 'dust-heavy-areas', 'floor-protection'],
          accessRequirements: 'Key pickup from site manager',
          urgency: 'standard'
        }
      },
      expectedValidations: ['name', 'email', 'phone', 'address', 'constructionType', 'squareFootage', 'cleaningDate'],
      expectedPaymentAmount: 45000
    },
    {
      formComponent: MoveInOutForm,
      formName: 'Move In/Out',
      testData: {
        serviceType: 'move-in-out',
        amount: 20000, // $200.00
        customerData: {
          name: 'Lisa Brown',
          email: '<EMAIL>',
          phone: '(*************',
          address: '987 Moving Lane, City, State 24680'
        },
        serviceDetails: {
          moveType: 'move-out',
          bedrooms: 3,
          bathrooms: 2,
          squareFootage: 1800,
          cleaningDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          timeSlot: '10:00-15:00',
          propertyCondition: 'good',
          addOns: ['inside-appliances', 'garage-cleaning'],
          depositReturn: true,
          landlordRequirements: 'Professional receipt required'
        }
      },
      expectedValidations: ['name', 'email', 'phone', 'address', 'moveType', 'bedrooms', 'cleaningDate'],
      expectedPaymentAmount: 20000
    }
  ];

  describe('Form Validation Tests', () => {
    paymentTestScenarios.forEach(({ formComponent: FormComponent, formName, testData, expectedValidations }) => {
      it(`should validate all required fields for ${formName}`, async () => {
        const user = userEvent.setup();
        render(
          React.createElement(BrowserRouter, null,
            React.createElement(FormComponent, null)
          )
        );

        // Try to submit empty form
        const submitButton = screen.getByRole('button', { name: /submit|book|schedule/i });
        await user.click(submitButton);

        // Check that validation errors appear for required fields
        for (const field of expectedValidations) {
          const errorMessage = await screen.findByText(new RegExp(field, 'i'));
          expect(errorMessage).toBeInTheDocument();
        }
      });

      it(`should accept valid data for ${formName}`, async () => {
        const user = userEvent.setup();
        render(
          React.createElement(BrowserRouter, null,
            React.createElement(FormComponent, null)
          )
        );

        // Fill in customer data
        await user.type(screen.getByLabelText(/name/i), testData.customerData.name);
        await user.type(screen.getByLabelText(/email/i), testData.customerData.email);
        await user.type(screen.getByLabelText(/phone/i), testData.customerData.phone);
        await user.type(screen.getByLabelText(/address/i), testData.customerData.address);

        // Fill in service-specific fields
        for (const [key, value] of Object.entries(testData.serviceDetails)) {
          const field = screen.queryByLabelText(new RegExp(key, 'i'));
          if (field) {
            if (field.tagName === 'SELECT') {
              await user.selectOptions(field, value.toString());
            } else if (field.getAttribute('type') === 'checkbox') {
              if (value) await user.click(field);
            } else {
              await user.clear(field);
              await user.type(field, value.toString());
            }
          }
        }

        // Submit form
        const submitButton = screen.getByRole('button', { name: /submit|book|schedule/i });
        await user.click(submitButton);

        // Should not show validation errors
        await waitFor(() => {
          expectedValidations.forEach(field => {
            const errorMessage = screen.queryByText(new RegExp(`${field}.*required`, 'i'));
            expect(errorMessage).not.toBeInTheDocument();
          });
        });
      });
    });
  });

  describe('Payment Processing Tests', () => {
    paymentTestScenarios.forEach(({ formName, testData, expectedPaymentAmount }) => {
      it(`should process payment correctly for ${formName}`, async () => {
        const result = await processResidentialPayment(
          expectedPaymentAmount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        );

        expect(processResidentialPayment).toHaveBeenCalledWith(
          expectedPaymentAmount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        );

        expect(result).toEqual(mockPaymentResponse);
        expect(result.success).toBe(true);
        expect(result.paymentLinkUrl).toContain('squareup.com');
        expect(result.bookingId).toBeDefined();
        expect(result.paymentId).toBeDefined();
      });

      it(`should handle payment errors gracefully for ${formName}`, async () => {
        const errorResponse = {
          success: false,
          error: 'Payment processing failed',
          code: 'PAYMENT_ERROR'
        };

        (processResidentialPayment as any).mockResolvedValueOnce(errorResponse);

        const result = await processResidentialPayment(
          expectedPaymentAmount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        );

        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('Booking Synchronization Tests', () => {
    it('should create synchronized payment and booking records', async () => {
      const synchronizer = new PaymentSynchronizer();
      const testData = paymentTestScenarios[0].testData;

      const result = await synchronizer.createPaymentBookingTransaction(
        testData.amount,
        testData.serviceType,
        testData.customerData,
        testData.serviceDetails
      );

      expect(PaymentSynchronizer.prototype.createPaymentBookingTransaction)
        .toHaveBeenCalledWith(
          testData.amount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        );

      expect(result).toEqual(mockPaymentResponse);
    });

    it('should handle database transaction failures', async () => {
      const errorResponse = {
        success: false,
        error: 'Database transaction failed',
        code: 'DB_ERROR'
      };

      (PaymentSynchronizer.prototype.createPaymentBookingTransaction as any)
        .mockRejectedValueOnce(new Error('Database transaction failed'));

      const synchronizer = new PaymentSynchronizer();
      const testData = paymentTestScenarios[0].testData;

      await expect(
        synchronizer.createPaymentBookingTransaction(
          testData.amount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      ).rejects.toThrow('Database transaction failed');
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle invalid payment amounts', async () => {
      const testData = paymentTestScenarios[0].testData;
      
      // Test negative amount
      await expect(
        processResidentialPayment(
          -1000,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      ).rejects.toThrow();

      // Test zero amount
      await expect(
        processResidentialPayment(
          0,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      ).rejects.toThrow();

      // Test extremely large amount
      await expect(
        processResidentialPayment(
          999999999,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      ).rejects.toThrow();
    });

    it('should handle invalid email formats', async () => {
      const testData = {
        ...paymentTestScenarios[0].testData,
        customerData: {
          ...paymentTestScenarios[0].testData.customerData,
          email: 'invalid-email'
        }
      };

      await expect(
        processResidentialPayment(
          testData.amount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      ).rejects.toThrow();
    });

    it('should handle missing required customer data', async () => {
      const testData = {
        ...paymentTestScenarios[0].testData,
        customerData: {
          name: '',
          email: '',
          phone: '',
          address: ''
        }
      };

      await expect(
        processResidentialPayment(
          testData.amount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      ).rejects.toThrow();
    });

    it('should handle network timeouts', async () => {
      (processResidentialPayment as any).mockRejectedValueOnce(
        new Error('Network timeout')
      );

      const testData = paymentTestScenarios[0].testData;

      await expect(
        processResidentialPayment(
          testData.amount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      ).rejects.toThrow('Network timeout');
    });

    it('should handle Square API errors', async () => {
      const squareError = {
        success: false,
        error: 'Square API error: Invalid location ID',
        code: 'SQUARE_API_ERROR'
      };

      (processResidentialPayment as any).mockResolvedValueOnce(squareError);

      const testData = paymentTestScenarios[0].testData;
      const result = await processResidentialPayment(
        testData.amount,
        testData.serviceType,
        testData.customerData,
        testData.serviceDetails
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Square API error');
    });
  });

  describe('Integration Tests', () => {
    it('should complete full user flow from form to payment', async () => {
      const user = userEvent.setup();
      const FormComponent = paymentTestScenarios[0].formComponent;
      const testData = paymentTestScenarios[0].testData;

      render(
        React.createElement(BrowserRouter, null,
          React.createElement(FormComponent, null)
        )
      );

      // Fill form
      await user.type(screen.getByLabelText(/name/i), testData.customerData.name);
      await user.type(screen.getByLabelText(/email/i), testData.customerData.email);
      await user.type(screen.getByLabelText(/phone/i), testData.customerData.phone);
      await user.type(screen.getByLabelText(/address/i), testData.customerData.address);

      // Submit form
      const submitButton = screen.getByRole('button', { name: /submit|book|schedule/i });
      await user.click(submitButton);

      // Verify payment processing was called
      await waitFor(() => {
        expect(processResidentialPayment).toHaveBeenCalled();
      });

      // Verify success state
      await waitFor(() => {
        const successMessage = screen.queryByText(/success|confirmed|booked/i);
        expect(successMessage).toBeInTheDocument();
      });
    });

    it('should handle complete payment flow with webhook simulation', async () => {
      const testData = paymentTestScenarios[0].testData;
      
      // Step 1: Create payment link
      const paymentResult = await processResidentialPayment(
        testData.amount,
        testData.serviceType,
        testData.customerData,
        testData.serviceDetails
      );

      expect(paymentResult.success).toBe(true);
      expect(paymentResult.paymentLinkUrl).toBeDefined();
      expect(paymentResult.bookingId).toBeDefined();

      // Step 2: Simulate webhook payment completion
      const webhookPayload = {
        type: 'payment.updated',
        data: {
          object: {
            payment: {
              id: paymentResult.paymentId,
              status: 'COMPLETED',
              amount_money: {
                amount: testData.amount,
                currency: 'USD'
              }
            }
          }
        }
      };

      // Simulate webhook processing
      const webhookResponse = await fetch('/api/webhook-payment-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(webhookPayload)
      });

      expect(webhookResponse.ok).toBe(true);

      // Step 3: Verify booking status updated
      expect(supabase.from).toHaveBeenCalledWith('bookings');
    });
  });

  describe('Performance Tests', () => {
    it('should process payments within acceptable time limits', async () => {
      const testData = paymentTestScenarios[0].testData;
      const startTime = Date.now();

      await processResidentialPayment(
        testData.amount,
        testData.serviceType,
        testData.customerData,
        testData.serviceDetails
      );

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Payment processing should complete within 5 seconds
      expect(processingTime).toBeLessThan(5000);
    });

    it('should handle concurrent payment requests', async () => {
      const testData = paymentTestScenarios[0].testData;
      const concurrentRequests = 5;

      const promises = Array.from({ length: concurrentRequests }, () =>
        processResidentialPayment(
          testData.amount,
          testData.serviceType,
          testData.customerData,
          testData.serviceDetails
        )
      );

      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.paymentLinkUrl).toBeDefined();
      });
    });
  });
});