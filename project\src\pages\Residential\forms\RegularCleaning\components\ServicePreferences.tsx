import React from 'react';
import { Clock, Target, Leaf, Shield, Home, Maximize, Grid, Plus } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServicePreferencesProps {
  preferences: {
    frequency: string;
    preferredDay: string[];
    preferredTime: string;
    cleaningFocus: string[];
    greenCleaning: boolean;
    petFriendly: boolean;
  };
  onChange: (preferences: any) => void;
}

export function ServicePreferences({ preferences, onChange }: ServicePreferencesProps) {
  const frequencies = [
    { value: 'one-time', label: 'One-Time Service' },
    { value: 'weekly', label: 'Weekly' },
    { value: 'bi-weekly', label: 'Bi-Weekly' },
    { value: 'monthly', label: 'Monthly' },
    { value: 'custom', label: 'Custom Schedule' }
  ];

  const daysOfWeek = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday',
    'Friday', 'Saturday', 'Sunday'
  ];

  const timeSlots = [
    'Morning (8AM - 12PM)',
    'Afternoon (12PM - 4PM)',
    'Evening (4PM - 8PM)'
  ];

  const focusAreas = [
    {
      id: 'kitchen',
      label: 'Kitchen Deep Clean',
      description: 'Appliances, counters, cabinets',
      icon: Target
    },
    {
      id: 'bathroom',
      label: 'Bathroom Sanitization',
      description: 'Deep cleaning and disinfection',
      icon: Shield
    },
    {
      id: 'bedroom',
      label: 'Bedrooms & Living Areas',
      description: 'Dusting, vacuuming, surfaces',
      icon: Home
    },
    {
      id: 'windows',
      label: 'Windows & Mirrors',
      description: 'Glass and mirror cleaning',
      icon: Maximize
    },
    {
      id: 'floors',
      label: 'Floors & Carpets',
      description: 'Mopping, vacuuming, spot cleaning',
      icon: Grid
    },
    {
      id: 'extras',
      label: 'Extra Services',
      description: 'Baseboards, cabinet fronts, etc',
      icon: Plus
    }
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Clock className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Service Preferences</h3>
          <p className="text-gray-600">Customize your cleaning service</p>
        </div>
      </div>

      {/* Service Frequency */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Service Frequency <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          {frequencies.map((freq) => (
            <label
              key={freq.value}
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                preferences.frequency === freq.value
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="frequency"
                value={freq.value}
                checked={preferences.frequency === freq.value}
                onChange={(e) => onChange({ ...preferences, frequency: e.target.value })}
                className="sr-only"
              />
              <Clock className={`w-5 h-5 mr-3 ${
                preferences.frequency === freq.value ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-gray-700">{freq.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Preferred Days */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Preferred Days
        </label>
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
          {daysOfWeek.map((day) => (
            <label
              key={day}
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                preferences.preferredDay.includes(day)
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="checkbox"
                checked={preferences.preferredDay.includes(day)}
                onChange={(e) => {
                  const newDays = e.target.checked
                    ? [...preferences.preferredDay, day]
                    : preferences.preferredDay.filter(d => d !== day);
                  onChange({ ...preferences, preferredDay: newDays });
                }}
                className="sr-only"
              />
              <span className="text-gray-700">{day}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Preferred Time */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Preferred Time <span className="text-red-500">*</span>
        </label>
        <select
          value={preferences.preferredTime}
          onChange={(e) => onChange({ ...preferences, preferredTime: e.target.value })}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          required
        >
          <option value="">Select preferred time</option>
          {timeSlots.map((slot) => (
            <option key={slot} value={slot}>{slot}</option>
          ))}
        </select>
      </div>

      {/* Enhanced Cleaning Focus Areas */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Cleaning Focus Areas <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {focusAreas.map((area) => {
            const Icon = area.icon;
            return (
              <label
                key={area.id}
                className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  preferences.cleaningFocus.includes(area.id)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={preferences.cleaningFocus.includes(area.id)}
                  onChange={(e) => {
                    const newAreas = e.target.checked
                      ? [...preferences.cleaningFocus, area.id]
                      : preferences.cleaningFocus.filter(a => a !== area.id);
                    onChange({ ...preferences, cleaningFocus: newAreas });
                  }}
                  className="sr-only"
                  required={preferences.cleaningFocus.length === 0}
                />
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    preferences.cleaningFocus.includes(area.id)
                      ? 'bg-brand-100'
                      : 'bg-gray-100'
                  }`}>
                    <Icon className={`w-5 h-5 ${
                      preferences.cleaningFocus.includes(area.id)
                        ? 'text-brand-600'
                        : 'text-gray-500'
                    }`} />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{area.label}</div>
                    <p className="text-sm text-gray-500 mt-1">{area.description}</p>
                  </div>
                </div>
              </label>
            );
          })}
        </div>
        {preferences.cleaningFocus.length === 0 && (
          <p className="text-sm text-red-500">Please select at least one focus area</p>
        )}
      </div>

      {/* Additional Preferences */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Additional Preferences
        </label>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <label
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              preferences.greenCleaning
                ? 'border-brand-500 bg-brand-50'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={preferences.greenCleaning}
              onChange={(e) => onChange({ ...preferences, greenCleaning: e.target.checked })}
              className="sr-only"
            />
            <Leaf className={`w-5 h-5 mr-3 ${
              preferences.greenCleaning ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Eco-Friendly Products</span>
          </label>

          <label
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              preferences.petFriendly
                ? 'border-brand-500 bg-brand-50'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={preferences.petFriendly}
              onChange={(e) => onChange({ ...preferences, petFriendly: e.target.checked })}
              className="sr-only"
            />
            <Shield className={`w-5 h-5 mr-3 ${
              preferences.petFriendly ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Pet-Friendly Service</span>
          </label>
        </div>
      </div>
    </div>
  );
}
