import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Sparkles, CheckCircle, 
  ArrowRight, Briefcase, Users, Mail, Phone, Home, User
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';

interface OfficeBookingFormData {
  officeType: string;
  officeSize: string;
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedOfficeForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<OfficeBookingFormData>>({
    officeType: 'standard',
    officeSize: 'medium',
    serviceFrequency: 'weekly',
    addOns: [],
  });
  
  useEffect(() => {
    const savedData = localStorage.getItem('officeFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('officeFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Service Type' },
    { id: 2, name: 'Office & Schedule' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact' },
  ];

  const officeTypes = [
    { id: 'standard', name: 'Standard Office', icon: <Building className="w-8 h-8" />, description: 'General-purpose office spaces' },
    { id: 'coworking', name: 'Co-working Space', icon: <Users className="w-8 h-8" />, description: 'Shared and flexible workspaces' },
    { id: 'medical', name: 'Medical Office', icon: <Briefcase className="w-8 h-8" />, description: 'Clinics and healthcare facilities' },
    { id: 'tech-startup', name: 'Tech Startup', icon: <Sparkles className="w-8 h-8" />, description: 'Fast-paced startup environments' },
  ];

  const officeSizeOptions = [
    { id: 'small', name: 'Under 1,000 sq ft' },
    { id: 'medium', name: '1,000 - 5,000 sq ft' },
    { id: 'large', name: '5,000 - 10,000 sq ft' },
    { id: 'xl', name: '10,000+ sq ft' },
  ];
  
  const serviceFrequencies = [
    { id: 'one-time', name: 'One-Time' },
    { id: 'daily', name: 'Daily' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly' },
  ];

  const addOnServices = [
    { id: 'window-cleaning', name: 'Interior Window Cleaning' },
    { id: 'carpet-shampoo', name: 'Carpet Shampooing' },
    { id: 'high-touch-sanitization', name: 'High-Touch Sanitization' },
    { id: 'restroom-restocking', name: 'Restroom Supply Restocking' },
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' },
  ];
  
  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2">
              Office Cleaning Service
            </h1>
            <p className="text-gray-200">Professional cleaning for any office space.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= step.id ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-green-500 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sm:p-8" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-white mb-6">Select Office Type</h2>
                   <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {officeTypes.map(type => (
                      <motion.button key={type.id} onClick={() => setFormData({...formData, officeType: type.id})} className={`p-4 rounded-xl border-2 text-left ${formData.officeType === type.id ? 'bg-green-500/20 border-green-400' : 'bg-white/5 border-white/20'}`}>
                        <div className="flex items-center gap-4">
                          <div className="text-green-300">{type.icon}</div>
                          <div>
                            <h3 className="font-semibold text-white">{type.name}</h3>
                            <p className="text-sm text-gray-300">{type.description}</p>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      onClick={() => setCurrentStep(2)} 
                      disabled={!formData.officeType}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}
               {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-white mb-6">Office & Schedule Details</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                     <GlassmorphismSelect
                        options={officeSizeOptions}
                        value={formData.officeSize}
                        onChange={value => setFormData({...formData, officeSize: value})}
                        placeholder="Select Size"
                      />
                     <GlassmorphismSelect
                        options={serviceFrequencies}
                        value={formData.serviceFrequency}
                        onChange={value => setFormData({...formData, serviceFrequency: value})}
                        placeholder="Select Frequency"
                      />
                     <input type="date" value={formData.preferredDate || ''} onChange={e => setFormData({...formData, preferredDate: e.target.value})} className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                     <GlassmorphismSelect
                        options={timeSlots}
                        value={formData.preferredTime}
                        onChange={value => setFormData({...formData, preferredTime: value})}
                        placeholder="Select Time"
                      />
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)} 
                      disabled={!formData.officeSize || !formData.serviceFrequency || !formData.preferredDate || !formData.preferredTime}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-white mb-6">Add-ons</h2>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {addOnServices.map(addon => (
                      <motion.button key={addon.id} onClick={() => handleAddOnToggle(addon.id)} className={`p-4 rounded-xl border-2 ${ (formData.addOns || []).includes(addon.id) ? 'bg-green-500/20 border-green-400' : 'bg-white/5 border-white/20' }`}>
                        <span className="text-white">{addon.name}</span>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-white mb-6">Contact Information</h2>
                   <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Company Name" value={formData.companyName || ''} onChange={e => setFormData({...formData, companyName: e.target.value})} className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                    </div>
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Contact Name" value={formData.contactName || ''} onChange={e => setFormData({...formData, contactName: e.target.value})} className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                    </div>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="email" placeholder="Email" value={formData.email || ''} onChange={e => setFormData({...formData, email: e.target.value})} className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                    </div>
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="tel" placeholder="Phone" value={formData.phone || ''} onChange={e => setFormData({...formData, phone: e.target.value})} className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                    </div>
                    <div className="relative sm:col-span-2">
                      <Home className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input type="text" placeholder="Address" value={formData.address || ''} onChange={e => setFormData({...formData, address: e.target.value})} className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                    </div>
                    <input type="text" placeholder="City" value={formData.city || ''} onChange={e => setFormData({...formData, city: e.target.value})} className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                    <input type="text" placeholder="ZIP Code" value={formData.zipCode || ''} onChange={e => setFormData({...formData, zipCode: e.target.value})} className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" />
                  </div>
                  <textarea placeholder="Special Instructions" value={formData.specialInstructions || ''} onChange={e => setFormData({...formData, specialInstructions: e.target.value})} className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" rows={4} />
                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={() => alert('Submit')}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Submit Request
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedOfficeForm; 
