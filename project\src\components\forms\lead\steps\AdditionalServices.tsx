import React from 'react';
import { Droplets, Sparkles, Package } from 'lucide-react';
import { ServiceToggle } from '../ui/ServiceToggle';

const additionalServices = [
  {
    id: 'sanitization',
    icon: Droplets, // Changed from Spray to Droplets
    label: 'Sanitization',
    description: 'Deep sanitization of high-touch surfaces'
  },
  {
    id: 'deep-cleaning',
    icon: Sparkles,
    label: 'Deep Cleaning',
    description: 'Thorough cleaning of all areas'
  },
  {
    id: 'supplies',
    icon: Package,
    label: 'Supply Restocking',
    description: 'Regular restocking of supplies'
  }
];

interface AdditionalServicesProps {
  selected: string[];
  onChange: (services: string[]) => void;
}

export function AdditionalServices({ selected, onChange }: AdditionalServicesProps) {
  const toggleService = (id: string) => {
    const newSelected = selected.includes(id)
      ? selected.filter(s => s !== id)
      : [...selected, id];
    onChange(newSelected);
  };

  return (
    <div>
      <h2 className="text-2xl font-medium mb-6">Additional Services</h2>
      <div className="space-y-4">
        {additionalServices.map((service) => (
          <ServiceToggle
            key={service.id}
            service={service}
            selected={selected.includes(service.id)}
            onToggle={() => toggleService(service.id)}
          />
        ))}
      </div>
    </div>
  );
}
