import React, { useState } from 'react';
import { Search } from 'lucide-react';

export function ZipCodeSearch() {
  const [zipCode, setZipCode] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle zip code submission
    console.log('Searching for services in:', zipCode);
  };

  return (
    <div className="max-w-md w-full">
      <form onSubmit={handleSubmit} className="flex">
        <div className="relative flex-grow">
          <input
            type="text"
            value={zipCode}
            onChange={(e) => setZipCode(e.target.value)}
            placeholder="Enter your zip code"
            className="block w-full rounded-l-lg border-gray-300 pl-4 pr-12 py-3 focus:border-green-500 focus:ring-green-500 text-gray-900 placeholder-gray-500 shadow-sm"
            maxLength={5}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
        </div>
        <button
          type="submit"
          className="flex-none rounded-r-lg border border-transparent bg-green-600 px-6 py-3 text-base font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Get Quote
        </button>
      </form>
    </div>
  );
}
