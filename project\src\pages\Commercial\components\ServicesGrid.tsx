import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Warehouse, HardHat, 
  Brush, Sofa, GlassWater
} from 'lucide-react';

const services = [
  {
    id: 'office',
    icon: Building,
    title: 'Office Cleaning',
    description: 'Daily, weekly, or monthly cleaning for a pristine office environment.',
    price: 'Custom',
    popular: true,
    features: ['Workstation & common area sanitization', 'Restroom cleaning', 'Trash & recycling', 'Floor care'],
    color: 'from-blue-500 to-blue-600',
    glowColor: 'rgba(59, 130, 246, 0.3)'
  },
  {
    id: 'industrial',
    icon: Warehouse,
    title: 'Industrial & Warehouse',
    description: 'Heavy-duty cleaning for industrial and warehouse facilities.',
    price: 'Custom',
    features: ['High-dusting', 'Machinery cleaning', 'Floor scrubbing', 'Degreasing services'],
    color: 'from-gray-500 to-gray-600',
    glowColor: 'rgba(107, 114, 128, 0.3)'
  },
  {
    id: 'post-construction',
    icon: HardHat,
    title: 'Post-Construction',
    description: 'Final cleaning after construction or renovation projects.',
    price: 'Custom',
    features: ['Debris removal', 'Fine dust cleaning', 'Window & surface polishing'],
    color: 'from-orange-400 to-orange-600',
    glowColor: 'rgba(251, 146, 60, 0.4)',
    path: '/commercial/post-construction'
  },
  {
    id: 'floor-care',
    icon: Brush,
    title: 'Specialized Floor Care',
    description: 'Stripping, waxing, buffing, and polishing for all floor types.',
    price: 'Custom',
    features: ['Vinyl, tile, hardwood', 'Deep scrub & recoat', 'High-speed burnishing'],
    color: 'from-green-500 to-green-600',
    glowColor: 'rgba(34, 197, 94, 0.3)'
  },
  {
    id: 'carpet-upholstery',
    icon: Sofa,
    title: 'Carpet & Upholstery',
    description: 'Commercial-grade deep cleaning for carpets and furniture.',
    price: 'Custom',
    features: ['Hot water extraction', 'Stain & spot treatment', 'Odor control'],
    color: 'from-indigo-500 to-indigo-600',
    glowColor: 'rgba(99, 102, 241, 0.3)'
  },
  {
    id: 'window-cleaning',
    icon: GlassWater,
    title: 'Window Cleaning',
    description: 'High-rise and ground-level window cleaning services.',
    price: 'Custom',
    features: ['Interior & exterior', 'Skylights & atriums', 'Hard water stain removal'],
    color: 'from-cyan-500 to-cyan-600',
    glowColor: 'rgba(6, 182, 212, 0.3)'
  },
];

export function ServicesGrid() {
  const navigate = useNavigate();

  const handleServiceClick = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    if (service && 'path' in service && service.path) {
      navigate(service.path);
    } else {
      navigate(`/commercial/${serviceId}`);
    }
  };

  return (
    <section className="py-24 sm:py-32" id="commercial-services">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Solutions for Every Business</h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto">
            We provide scalable cleaning solutions for businesses of all sizes and industries.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6, ease: 'easeOut' }}
                className="relative p-8 rounded-3xl border border-white/10 h-full flex flex-col group cursor-pointer"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(30px)',
                  WebkitBackdropFilter: 'blur(30px)',
                }}
                onClick={() => handleServiceClick(service.id)}
              >
                {service.popular && (
                  <div className="absolute top-0 right-8 -mt-4">
                    <div className="px-4 py-1.5 rounded-full text-sm font-semibold text-black bg-white">
                      Popular
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-center w-12 h-12 bg-white/10 rounded-xl mb-6 border border-white/20">
                  <Icon className="w-6 h-6 text-white" />
                </div>

                <div className="flex-grow">
                  <h3 className="text-xl font-semibold text-white mb-3">{service.title}</h3>
                  <p className="text-white/70 leading-relaxed mb-6">{service.description}</p>
                </div>
                
                <div className="mt-auto flex justify-between items-center">
                  <span className="text-lg font-semibold text-white">{service.price}</span>
                  <div className="text-white/70 group-hover:text-white transition-colors">
                    <span className="mr-2">Get Quote</span>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
} 
