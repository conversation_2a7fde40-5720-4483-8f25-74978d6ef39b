import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Lock, Eye, FileText, Bell, Users, Phone, MessageSquare } from 'lucide-react';
import { Header } from '../components/layout/Header';
import { Footer } from '../components/layout/Footer';

const sections = [
  {
    icon: Shield,
    title: 'Information We Collect',
    content: [
      {
        heading: '1. Personal Information',
        text: 'We collect contact information (name, email, phone), property details, service preferences, payment information, service history, and communication records.'
      },
      {
        heading: '2. Text Message Consent',
        text: 'When you provide your mobile number and opt-in to receive text messages, we collect and store your phone number, consent records, message history, and opt-in/opt-out preferences. This information is used solely for service-related communications and will never be shared with third parties or used for marketing purposes without your explicit consent.'
      },
      {
        heading: '3. Website Usage',
        text: 'We collect website analytics and usage data to improve our services.'
      }
    ]
  },
  {
    icon: Lock,
    title: 'Data Protection',
    content: [
      {
        heading: '4. Security Measures',
        text: 'We employ industry-standard encryption, secure data storage, and strict access controls to protect your information.'
      },
      {
        heading: '5. Text Message Privacy',
        text: 'Your text messaging originator opt-in data and consent records are stored securely and will never be shared with or sold to third parties. We maintain strict controls over access to this information and use it only for providing our services.'
      }
    ]
  },
  {
    icon: Eye,
    title: 'Information Usage',
    content: [
      {
        heading: '6. Service Delivery',
        text: 'We use your information to provide and improve our services, process payments, and communicate about your services.'
      },
      {
        heading: '7. Communication',
        text: 'We may contact you via email, phone, or text message about your services, appointments, and important updates. Text messages will only be sent to numbers that have explicitly opted in to receive them.'
      }
    ]
  },
  {
    icon: MessageSquare,
    title: 'Text Messaging Policy',
    content: [
      {
        heading: '8. Opt-In/Opt-Out',
        text: 'You can opt-in to receive text messages during service booking or by explicitly requesting it. You may opt-out at any time by replying STOP to any message or contacting our support team.'
      },
      {
        heading: '9. Message Frequency',
        text: 'We send text messages only for service-related communications such as appointment confirmations, reminders, and important updates. Message frequency varies based on your service schedule.'
      },
      {
        heading: '10. Carrier Charges',
        text: 'Standard message and data rates may apply for any text messages sent to or received from us. Please contact your wireless carrier for details about your text messaging plan.'
      }
    ]
  },
  {
    icon: Bell,
    title: 'Your Rights',
    content: [
      {
        heading: '11. Access and Control',
        text: 'You have the right to access your personal information, request corrections, opt-out of communications, and request data deletion.'
      },
      {
        heading: '12. Communication Preferences',
        text: 'You can manage your communication preferences, including text message settings, through your account settings or by contacting our support team.'
      }
    ]
  },
  {
    icon: FileText,
    title: 'Additional Terms',
    content: [
      {
        heading: '13. Policy Updates',
        text: 'We may update this privacy policy periodically. We will notify you of any material changes via email or through our website.'
      },
      {
        heading: '14. Contact Us',
        text: 'For questions about your privacy or text messaging preferences, please contact <NAME_EMAIL> or (*************.'
      }
    ]
  }
];

export function Privacy() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="pt-40 pb-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-20"
          >
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-6">
                Privacy Policy
              </h1>
              <p className="text-xl text-gray-600">
                Your privacy is important to us. This policy outlines how we collect, use, and protect your information.
              </p>
              <div className="mt-8 text-sm text-gray-500">
                Last updated: January 5, 2024
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <p className="text-gray-600 leading-relaxed">
              Empire Pro Cleaning Services ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our services or visit our website. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the site or use our services.
            </p>
          </motion.div>

          <div className="space-y-8">
            {sections.map((section, index) => {
              const Icon = section.icon;
              return (
                <motion.div
                  key={section.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + (index * 0.1) }}
                  className="bg-white rounded-2xl shadow-lg p-8"
                >
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="p-3 rounded-xl bg-brand-100">
                      <Icon className="w-6 h-6 text-brand-600" />
                    </div>
                    <h2 className="text-2xl font-semibold text-gray-900">
                      {section.title}
                    </h2>
                  </div>
                  <div className="space-y-6">
                    {section.content.map((item) => (
                      <div key={item.heading}>
                        <h3 className="text-lg font-medium text-gray-900 mb-3">
                          {item.heading}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {item.text}
                        </p>
                      </div>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-brand-50 rounded-2xl p-8 mt-8"
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Contact Us About Privacy
            </h2>
            <p className="text-gray-600 mb-4">
              If you have any questions about this Privacy Policy or your data, please contact us:
            </p>
            <ul className="space-y-2 text-gray-600">
              <li>Email: <EMAIL></li>
              <li>Phone: (*************</li>
              <li>Address: 47-38 Vernon Blvd, Long Island City, NY 11101</li>
            </ul>
          </motion.div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
