import React from 'react';
import { <PERSON>, <PERSON>rkles, Building2, Coffee, Printer, Users, Server, BookOpen } from 'lucide-react';
import { FormLayout } from '../../../../../components/forms/FormLayout';
import { FormSection } from '../../../../../components/forms/FormSection';
import { FormField } from '../../../../../components/forms/FormField';

interface CleaningDetailsProps {
  details: {
    frequency: string;
    preferredTime: string;
    specialAreas: string[];
    supplies: boolean;
    wasteManagement: boolean;
    specialInstructions: string;
  };
  onChange: (details: any) => void;
}

export function CleaningDetails({ details, onChange }: CleaningDetailsProps) {
  const frequencies = [
    { value: 'daily', label: 'Daily Service', description: 'Professional cleaning every business day' },
    { value: 'weekly', label: 'Weekly Service', description: 'Once per week deep cleaning' },
    { value: 'biweekly', label: 'Bi-Weekly Service', description: 'Every two weeks maintenance' },
    { value: 'monthly', label: 'Monthly Service', description: 'Monthly deep cleaning service' },
    { value: 'custom', label: 'Custom Schedule', description: 'Tailored to your needs' }
  ];

  const timeSlots = [
    { value: 'early-morning', label: 'Early Morning (5AM - 8AM)', description: 'Before office hours' },
    { value: 'morning', label: 'Morning (8AM - 12PM)', description: 'During morning hours' },
    { value: 'afternoon', label: 'Afternoon (12PM - 5PM)', description: 'During afternoon hours' },
    { value: 'evening', label: 'Evening (5PM - 9PM)', description: 'After office hours' },
    { value: 'night', label: 'Night (9PM - 12AM)', description: 'Night cleaning service' }
  ];

  const specialAreas = [
    { id: 'reception', label: 'Reception Area', icon: Users, description: 'Front desk and waiting areas' },
    { id: 'executive', label: 'Executive Offices', icon: Building2, description: 'Private executive spaces' },
    { id: 'conference', label: 'Conference Rooms', icon: Users, description: 'Meeting and conference spaces' },
    { id: 'break', label: 'Break Rooms', icon: Coffee, description: 'Kitchen and break areas' },
    { id: 'server', label: 'Server Rooms', icon: Server, description: 'IT and server rooms' },
    { id: 'private', label: 'Private Offices', icon: Building2, description: 'Individual office spaces' },
    { id: 'open', label: 'Open Workspace', icon: Users, description: 'Open plan areas' },
    { id: 'copy', label: 'Copy/Print Rooms', icon: Printer, description: 'Print and copy centers' }
  ];

  return (
    <FormLayout
      title="Cleaning Requirements"
      description="Customize your office cleaning schedule and services"
      icon={<Sparkles className="w-6 h-6 text-brand-600" />}
    >
      {/* Service Frequency */}
      <FormSection 
        title="Service Frequency" 
        description="How often would you like your space cleaned?"
        required
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {frequencies.map((freq) => (
            <label
              key={freq.value}
              className={`relative flex flex-col p-4 rounded-xl cursor-pointer transition-all ${
                details.frequency === freq.value
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                  : 'bg-white border-2 border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="frequency"
                value={freq.value}
                checked={details.frequency === freq.value}
                onChange={(e) => onChange({ ...details, frequency: e.target.value })}
                className="sr-only"
              />
              <div className="flex items-center mb-2">
                <Clock className={`w-5 h-5 mr-2 ${
                  details.frequency === freq.value ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="font-medium text-gray-900">{freq.label}</span>
              </div>
              <p className="text-sm text-gray-600">{freq.description}</p>
            </label>
          ))}
        </div>
      </FormSection>

      {/* Preferred Time */}
      <FormSection 
        title="Preferred Time" 
        description="When would you like the cleaning to be performed?"
        required
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {timeSlots.map((slot) => (
            <label
              key={slot.value}
              className={`relative flex flex-col p-4 rounded-xl cursor-pointer transition-all ${
                details.preferredTime === slot.value
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                  : 'bg-white border-2 border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="preferredTime"
                value={slot.value}
                checked={details.preferredTime === slot.value}
                onChange={(e) => onChange({ ...details, preferredTime: e.target.value })}
                className="sr-only"
              />
              <div className="flex items-center mb-2">
                <Clock className={`w-5 h-5 mr-2 ${
                  details.preferredTime === slot.value ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="font-medium text-gray-900">{slot.label}</span>
              </div>
              <p className="text-sm text-gray-600">{slot.description}</p>
            </label>
          ))}
        </div>
      </FormSection>

      {/* Priority Areas */}
      <FormSection 
        title="Priority Areas" 
        description="Select areas that need special attention"
        required
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {specialAreas.map((area) => {
            const Icon = area.icon;
            const isSelected = details.specialAreas.includes(area.id);
            
            return (
              <label
                key={area.id}
                className={`relative flex flex-col p-4 rounded-xl cursor-pointer transition-all ${
                  isSelected
                    ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                    : 'bg-white border-2 border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={(e) => {
                    const newAreas = e.target.checked
                      ? [...details.specialAreas, area.id]
                      : details.specialAreas.filter(a => a !== area.id);
                    onChange({ ...details, specialAreas: newAreas });
                  }}
                  className="sr-only"
                />
                <div className="flex items-center mb-2">
                  <Icon className={`w-5 h-5 mr-2 ${
                    isSelected ? 'text-brand-600' : 'text-gray-400'
                  }`} />
                  <span className="font-medium text-gray-900">{area.label}</span>
                </div>
                <p className="text-sm text-gray-600">{area.description}</p>
              </label>
            );
          })}
        </div>
      </FormSection>

      {/* Additional Services */}
      <FormSection title="Additional Services" description="Select any additional services needed">
        <div className="space-y-4">
          <label className="flex items-center p-4 rounded-xl border-2 border-gray-200 hover:border-brand-300 transition-all cursor-pointer">
            <input
              type="checkbox"
              checked={details.supplies}
              onChange={(e) => onChange({ ...details, supplies: e.target.checked })}
              className="sr-only"
            />
            <div className={`p-2 rounded-lg mr-4 ${
              details.supplies ? 'bg-brand-100' : 'bg-gray-100'
            }`}>
              <BookOpen className={`w-5 h-5 ${
                details.supplies ? 'text-brand-600' : 'text-gray-400'
              }`} />
            </div>
            <div>
              <div className="font-medium text-gray-900">Supply Restocking Service</div>
              <p className="text-sm text-gray-600">We'll manage and restock office supplies (paper towels, soap, etc.)</p>
            </div>
          </label>

          <label className="flex items-center p-4 rounded-xl border-2 border-gray-200 hover:border-brand-300 transition-all cursor-pointer">
            <input
              type="checkbox"
              checked={details.wasteManagement}
              onChange={(e) => onChange({ ...details, wasteManagement: e.target.checked })}
              className="sr-only"
            />
            <div className={`p-2 rounded-lg mr-4 ${
              details.wasteManagement ? 'bg-brand-100' : 'bg-gray-100'
            }`}>
              <Sparkles className={`w-5 h-5 ${
                details.wasteManagement ? 'text-brand-600' : 'text-gray-400'
              }`} />
            </div>
            <div>
              <div className="font-medium text-gray-900">Waste Management & Recycling</div>
              <p className="text-sm text-gray-600">Complete waste management and recycling service</p>
            </div>
          </label>
        </div>
      </FormSection>

      {/* Special Instructions */}
      <FormSection title="Special Instructions" description="Any additional cleaning requirements?">
        <FormField label="Additional Notes">
          <textarea
            value={details.specialInstructions}
            onChange={(e) => onChange({ ...details, specialInstructions: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            rows={4}
            placeholder="Please describe any specific cleaning requirements or areas that need special attention..."
          />
        </FormField>
      </FormSection>
    </FormLayout>
  );
}
