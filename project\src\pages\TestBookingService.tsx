import React, { useState } from 'react';
import { BookingService } from '../lib/api/bookingService';
import { useAuth } from '/lib/auth/useAuth';

const TestBookingService: React.FC = () => {
  const { user } = useAuth();
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testMoveOutBooking = async () => {
    setLoading(true);
    setResult('Testing...');
    
    try {
      console.log('🧪 TESTING BOOKING SERVICE DIRECTLY');
      
      const testFormData = {
        // All required fields for move-out cleaning
        propertyType: 'house',
        propertySize: 'medium',
        bedrooms: '3',
        bathrooms: '2',
        moveType: 'move-out',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '1234567890',
        address: '123 Main St',
        city: 'Anytown',
        zipCode: '12345',
        preferredDate: '2024-12-25',
        preferredTime: 'morning',
        serviceType: 'residential_move',
        cleaningType: 'move',
        frequency: 'one-time',
        addOns: [],
        specialInstructions: '',
        totalPrice: 299
      };

      console.log('🧪 Test form data:', testFormData);
      console.log('🧪 Calling BookingService.saveBooking with serviceType: residential_move');
      
      const booking = await BookingService.saveBooking(
        testFormData,
        'residential_move',
        user
      );
      
      console.log('🧪 SUCCESS! Booking created:', booking);
      setResult(`✅ SUCCESS! Booking created with ID: ${booking.id}`);
      
    } catch (error: any) {
      console.error('🧪 FAILED! Error:', error);
      setResult(`❌ FAILED! Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testRegularBooking = async () => {
    setLoading(true);
    setResult('Testing regular booking...');
    
    try {
      console.log('🧪 TESTING REGULAR BOOKING SERVICE');
      
      const testFormData = {
        propertyType: 'house',
        propertySize: 'medium',
        bedrooms: '3',
        bathrooms: '2',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '1234567890',
        address: '123 Main St',
        city: 'Anytown',
        zipCode: '12345',
        preferredDate: '2024-12-25',
        preferredTime: 'morning',
        serviceType: 'residential_regular',
        cleaningType: 'regular',
        frequency: 'weekly',
        addOns: [],
        specialInstructions: '',
        totalPrice: 199
      };

      console.log('🧪 Test form data:', testFormData);
      console.log('🧪 Calling BookingService.saveBooking with serviceType: residential_regular');
      
      const booking = await BookingService.saveBooking(
        testFormData,
        'residential_regular',
        user
      );
      
      console.log('🧪 SUCCESS! Regular booking created:', booking);
      setResult(`✅ SUCCESS! Regular booking created with ID: ${booking.id}`);
      
    } catch (error: any) {
      console.error('🧪 FAILED! Regular booking error:', error);
      setResult(`❌ FAILED! Regular booking error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🧪 BookingService Direct Test</h1>
      <p>This page tests the BookingService directly, bypassing PaymentOptionsModal</p>
      
      <div style={{ margin: '20px 0' }}>
        <button 
          onClick={testMoveOutBooking}
          disabled={loading}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Testing...' : '🧪 Test Move-Out Booking (residential_move)'}
        </button>
        
        <button 
          onClick={testRegularBooking}
          disabled={loading}
          style={{
            padding: '10px 20px',
            margin: '10px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Testing...' : '🧪 Test Regular Booking (residential_regular)'}
        </button>
      </div>
      
      <div style={{
        padding: '15px',
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>Result:</h3>
        <pre style={{ whiteSpace: 'pre-wrap' }}>{result || 'Click a button to test'}</pre>
      </div>
      
      <div style={{
        padding: '15px',
        backgroundColor: '#e7f3ff',
        border: '1px solid #b3d9ff',
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>Instructions:</h3>
        <ol>
          <li>Open browser DevTools (F12) → Console tab</li>
          <li>Click "Test Move-Out Booking" button</li>
          <li>Watch console for debug messages</li>
          <li>If move-out fails but regular succeeds, the issue is in move-out validation</li>
          <li>If both fail, the issue is in general BookingService validation</li>
        </ol>
      </div>
      
      <div style={{
        padding: '15px',
        backgroundColor: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '4px',
        marginTop: '20px'
      }}>
        <h3>Expected Results:</h3>
        <p><strong>Move-Out Test:</strong> Should succeed and create booking with residential_move service type</p>
        <p><strong>Regular Test:</strong> Should succeed and create booking with residential_regular service type</p>
        <p><strong>If move-out fails:</strong> The issue is specifically with residential_move validation logic</p>
      </div>
    </div>
  );
};

export default TestBookingService;

