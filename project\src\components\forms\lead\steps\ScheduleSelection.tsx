import React from 'react';
import { DatePicker } from '../ui/DatePicker';
import { TimePreference } from '../ui/TimePreference';
import type { ScheduleType } from '../types';

interface ScheduleSelectionProps {
  schedule: ScheduleType;
  onChange: (schedule: ScheduleType) => void;
}

export function ScheduleSelection({ schedule, onChange }: ScheduleSelectionProps) {
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-medium mb-6">Schedule Your Service</h2>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Start Date
          </label>
          <DatePicker
            selected={schedule.startDate}
            onChange={(date) => onChange({ ...schedule, startDate: date })}
            minDate={new Date()}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Preferred Time
          </label>
          <TimePreference
            selected={schedule.timePreference}
            onChange={(time) => onChange({ ...schedule, timePreference: time })}
          />
        </div>
      </div>
    </div>
  );
}
