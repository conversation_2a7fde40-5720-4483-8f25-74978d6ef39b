import React from 'react';
import { Leaf, Droplets, Sparkles } from 'lucide-react';

const icons = [
  { Icon: Leaf, className: 'text-brand-400' },
  { Icon: Droplets, className: 'text-brand-500' },
  { Icon: Sparkles, className: 'text-brand-300' }
];

export function FloatingIcons() {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {icons.map(({ Icon, className }, index) => (
        <div
          key={index}
          className={`absolute animate-float ${className}`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${index * 0.5}s`
          }}
        >
          <Icon className="w-8 h-8 opacity-20" />
        </div>
      ))}
    </div>
  );
}
