import React from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, Clock, Star, Users, CheckCircle, Building
} from 'lucide-react';

const benefits = [
  { icon: Shield, title: 'Licensed & Insured', description: 'Comprehensive insurance coverage for your commercial property and our staff.' },
  { icon: Star, title: 'Reliable & Consistent', description: 'We guarantee consistent, high-quality service tailored to your business needs.' },
  { icon: Clock, title: 'Flexible Contracts', description: 'Customizable cleaning schedules and contracts to fit your operational hours.' },
  { icon: Users, title: 'Trained & Uniformed Staff', description: 'Our professional team is trained, background-checked, and uniformed.' },
  { icon: CheckCircle, title: 'Quality Assurance', description: 'Regular inspections and quality checks to ensure the highest standards.' },
  { icon: Building, title: 'Improved Business Image', description: 'A clean and sanitary environment enhances your brand and impresses clients.' }
];

export function Benefits() {
  return (
    <section className="py-24 sm:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">The Professional Advantage</h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto">
            Discover the benefits of a cleaning partner that understands the demands of your business.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => {
            const Icon = benefit.icon;
            
            return (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6, ease: 'easeOut' }}
                className="p-8 rounded-3xl border border-white/10"
                style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(30px)',
                  WebkitBackdropFilter: 'blur(30px)',
                }}
              >
                <div className="flex items-center justify-center w-12 h-12 bg-white/10 rounded-xl mb-6 border border-white/20">
                      <Icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{benefit.title}</h3>
                <p className="text-white/70 leading-relaxed">{benefit.description}</p>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
} 
