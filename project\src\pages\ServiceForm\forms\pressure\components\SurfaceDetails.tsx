import React from 'react';
import { Grid, Target, AlertTriangle, Droplets, Waves, Shield } from 'lucide-react';
import { motion } from 'framer-motion';

interface SurfaceDetailsData {
  surfaceType: string;
  areas: string[];
  squareFootage: number;
  condition: string;
  specialRequirements: string[];
  surfaceMaterial: string;
  contaminants: string[];
  pressureLevel: string;
  chemicalTreatment: boolean;
  hotWaterRequired: boolean;
}

interface SurfaceDetailsProps {
  details: SurfaceDetailsData;
  onChange: (details: SurfaceDetailsData) => void;
}

export function SurfaceDetails({ details = {
  surfaceType: '',
  areas: [],
  squareFootage: 0,
  condition: '',
  specialRequirements: [],
  surfaceMaterial: '',
  contaminants: [],
  pressureLevel: '',
  chemicalTreatment: false,
  hotWaterRequired: false
}, onChange }: SurfaceDetailsProps) {
  const surfaceTypes = [
    { value: 'concrete', label: 'Concrete', description: 'Driveways, walkways, parking areas' },
    { value: 'brick', label: 'Brick', description: 'Walls, pathways, decorative surfaces' },
    { value: 'stone', label: 'Natural Stone', description: 'Granite, marble, limestone' },
    { value: 'wood', label: 'Wood', description: 'Decks, fences, outdoor furniture' },
    { value: 'metal', label: 'Metal', description: 'Equipment, gates, metal structures' },
    { value: 'composite', label: 'Composite', description: 'Decking, siding, panels' }
  ];

  const areas = [
    { id: 'building-exterior', label: 'Building Exterior', icon: Shield },
    { id: 'sidewalks', label: 'Sidewalks & Walkways', icon: Target },
    { id: 'parking', label: 'Parking Areas', icon: Target },
    { id: 'driveway', label: 'Driveways', icon: Target },
    { id: 'loading-dock', label: 'Loading Docks', icon: Shield },
    { id: 'dumpster', label: 'Dumpster Areas', icon: Shield },
    { id: 'equipment', label: 'Equipment & Machinery', icon: Shield },
    { id: 'fencing', label: 'Fencing & Gates', icon: Target }
  ];

  const contaminants = [
    { id: 'dirt', label: 'Dirt & Grime', icon: Target },
    { id: 'oil', label: 'Oil & Grease', icon: Droplets },
    { id: 'mold', label: 'Mold & Mildew', icon: Shield },
    { id: 'rust', label: 'Rust Stains', icon: Shield },
    { id: 'paint', label: 'Paint Overspray', icon: Droplets },
    { id: 'graffiti', label: 'Graffiti', icon: Shield },
    { id: 'algae', label: 'Algae Growth', icon: Shield },
    { id: 'efflorescence', label: 'Efflorescence', icon: Target }
  ];

  const pressureLevels = [
    { value: 'low', label: 'Low Pressure', description: 'Gentle cleaning for delicate surfaces' },
    { value: 'medium', label: 'Medium Pressure', description: 'Standard cleaning for most surfaces' },
    { value: 'high', label: 'High Pressure', description: 'Heavy-duty cleaning for tough stains' }
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Waves className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Surface Details</h3>
          <p className="text-gray-600">Tell us about the surfaces that need cleaning</p>
        </div>
      </motion.div>

      {/* Surface Type Selection */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Surface Type <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {surfaceTypes.map((type, index) => (
            <motion.label
              key={type.value}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                details.surfaceType === type.value
                  ? 'border-brand-500 bg-brand-50 shadow-md'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="surfaceType"
                value={type.value}
                checked={details.surfaceType === type.value}
                onChange={(e) => onChange({ ...details, surfaceType: e.target.value })}
                className="sr-only"
              />
              <div className="font-medium text-gray-900 mb-1">{type.label}</div>
              <p className="text-sm text-gray-600">{type.description}</p>
            </motion.label>
          ))}
        </div>
      </div>

      {/* Areas to Clean */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Areas to Clean <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {areas.map((area, index) => {
            const Icon = area.icon;
            return (
              <motion.label
                key={area.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 + (index * 0.1) }}
                className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  details.areas.includes(area.id)
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.areas.includes(area.id)}
                  onChange={(e) => {
                    const newAreas = e.target.checked
                      ? [...details.areas, area.id]
                      : details.areas.filter(a => a !== area.id);
                    onChange({ ...details, areas: newAreas });
                  }}
                  className="sr-only"
                />
                <Icon className={`w-5 h-5 mr-3 ${
                  details.areas.includes(area.id) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{area.label}</span>
              </motion.label>
            );
          })}
        </div>
      </div>

      {/* Contaminants */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Contaminants to Remove <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {contaminants.map((contaminant, index) => {
            const Icon = contaminant.icon;
            return (
              <motion.label
                key={contaminant.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + (index * 0.1) }}
                className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  details.contaminants.includes(contaminant.id)
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.contaminants.includes(contaminant.id)}
                  onChange={(e) => {
                    const newContaminants = e.target.checked
                      ? [...details.contaminants, contaminant.id]
                      : details.contaminants.filter(c => c !== contaminant.id);
                    onChange({ ...details, contaminants: newContaminants });
                  }}
                  className="sr-only"
                />
                <Icon className={`w-5 h-5 mr-3 ${
                  details.contaminants.includes(contaminant.id) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{contaminant.label}</span>
              </motion.label>
            );
          })}
        </div>
      </div>

      {/* Pressure Level */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Required Pressure Level <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {pressureLevels.map((level, index) => (
            <motion.label
              key={level.value}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + (index * 0.1) }}
              className={`flex flex-col p-4 rounded-xl border-2 transition-all cursor-pointer ${
                details.pressureLevel === level.value
                  ? 'border-brand-500 bg-brand-50 shadow-md'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="pressureLevel"
                value={level.value}
                checked={details.pressureLevel === level.value}
                onChange={(e) => onChange({ ...details, pressureLevel: e.target.value })}
                className="sr-only"
              />
              <div className="font-medium text-gray-900 mb-1">{level.label}</div>
              <p className="text-sm text-gray-600">{level.description}</p>
            </motion.label>
          ))}
        </div>
      </div>

      {/* Additional Requirements */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Additional Requirements
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <motion.label
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
              details.chemicalTreatment
                ? 'border-brand-500 bg-brand-50 shadow-md'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={details.chemicalTreatment}
              onChange={(e) => onChange({ ...details, chemicalTreatment: e.target.checked })}
              className="sr-only"
            />
            <Droplets className={`w-5 h-5 mr-3 ${
              details.chemicalTreatment ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Chemical Treatment Required</span>
          </motion.label>

          <motion.label
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6 }}
            className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
              details.hotWaterRequired
                ? 'border-brand-500 bg-brand-50 shadow-md'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={details.hotWaterRequired}
              onChange={(e) => onChange({ ...details, hotWaterRequired: e.target.checked })}
              className="sr-only"
            />
            <Waves className={`w-5 h-5 mr-3 ${
              details.hotWaterRequired ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Hot Water Required</span>
          </motion.label>
        </div>
      </div>

      {/* Warning for special surfaces */}
      {(details.surfaceType === 'stone' || details.surfaceType === 'wood') && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl"
        >
          <div className="flex items-start">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Special Surface Notice</h4>
              <p className="mt-1 text-sm text-yellow-700">
                {details.surfaceType === 'stone'
                  ? 'Natural stone requires specialized cleaning techniques and pressure settings to prevent damage. Our team will assess the surface and use appropriate methods.'
                  : 'Wood surfaces require careful pressure control to prevent splintering or damage. We will adjust our approach based on the wood type and condition.'}
              </p>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}
