import React from 'react';
import { Form, FormField } from '../components/FormFields';

export function WindowCleaningForm() {
  return (
    <Form onSubmit={(data) => console.log(data)}>
      <FormField label="Number of Windows" required>
        <input
          type="number"
          name="windowCount"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Service Type" required>
        <select
          name="serviceType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="interior">Interior Only</option>
          <option value="exterior">Exterior Only</option>
          <option value="both">Interior & Exterior</option>
        </select>
      </FormField>

      <FormField label="Building Height" required>
        <select
          name="buildingHeight"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="1-2">1-2 Stories</option>
          <option value="3-5">3-5 Stories</option>
          <option value="6+">6+ Stories</option>
        </select>
      </FormField>

      <FormField label="Special Instructions">
        <textarea
          name="instructions"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific requirements or access restrictions?"
        />
      </FormField>
    </Form>
  );
}
