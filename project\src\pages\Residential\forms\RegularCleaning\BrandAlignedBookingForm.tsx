import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  Home, Sparkles, Calendar, Clock, CheckCircle,
  Shield, Star, ArrowRight, Building2,
  Users, DollarSign, Heart,
  ChevronRight, ChevronLeft, Gift, Zap, Info,
  HomeIcon, Building, Castle, Warehouse, AlertCircle
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useBookingSubmission, useFormValidation } from '../../../../hooks/useBookingSubmission';


interface BookingFormData {
  // Property Info
  propertyType: string;
  propertySize: string;
  
  // Service Details  
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  
  // Add-ons
  addOns: string[];
  
  // Contact Info
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  
  // Marketing
  howDidYouHear: string;
  newsletter: boolean;
}

const BrandAlignedBookingForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPriceDetails, setShowPriceDetails] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Use standardized booking submission
  const {
    state: submissionState,
    submitBooking,
    processPayment,
    loadSavedFormData,
    isLoading: isSubmitting,
    error: submissionError
  } = useBookingSubmission('residential_regular', user, {
    saveFormData: true,
    redirectToLogin: true,
    openPaymentModal: true
  });

  // Use form validation
  const {
    errors: validationErrors,
    validateField,
    validateAllFields,
    clearFieldError
  } = useFormValidation('residential_regular', user);
  const [formData, setFormData] = useState<BookingFormData>({
    propertyType: '',
    propertySize: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    howDidYouHear: '',
    newsletter: false
  });

  // Dynamic price calculation
  const calculatePrice = () => {
    const addOnPrices: Record<string, number> = {
      'deep-clean': 50,
      'eco-friendly': 20,
      'inside-oven': 30,
      'inside-fridge': 30,
      'windows': 40,
      'laundry': 25
    };

    // Get base price from selected property size
    const selectedSize = getPropertySizes().find(size => size.id === formData.propertySize);
    const price = selectedSize?.basePrice || 120;
    
    // Add-on costs
    const addOnTotal = formData.addOns?.reduce((total, addon) => {
      return total + (addOnPrices[addon] || 0);
    }, 0) || 0;

    return Math.round(price + addOnTotal);
  };

  // Property types with brand colors
  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Studio to 3BR',
      floors: '1 level',
      time: '1-3 hours'
    },
    { 
      id: 'house', 
      name: 'House', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Single family',
      floors: '1-3 levels',
      time: '2-4 hours'
    },
    { 
      id: 'condo', 
      name: 'Condo', 
      icon: <Building2 className="w-6 h-6" />, 
      description: 'Any size',
      floors: '1 level',
      time: '1-3 hours'
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: <Home className="w-6 h-6" />, 
      description: 'Multi-level',
      floors: '2-3 levels',
      time: '2-4 hours'
    }
  ];

  // Get icon for property size
  const getPropertySizeIcon = (sizeId: string) => {
    // Icon mapping based on size
    const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
      // Studio/Small sizes
      'studio': HomeIcon,
      'small-house': Home,
      'small-condo': HomeIcon,
      'small-townhouse': Home,
      
      // Medium sizes
      '1bed': Building,
      'medium-house': Building2,
      'medium-condo': Building,
      'medium-townhouse': Building2,
      
      // Large sizes
      '2bed': Building2,
      'large-house': Castle,
      'large-condo': Building2,
      'large-townhouse': Castle,
      
      // Extra large sizes
      '3bed': Castle,
      'xl-house': Warehouse,
      
      // Default fallbacks
      'default': Home
    };
    
    const IconComponent = iconMap[sizeId] || iconMap['default'];
    return <IconComponent className="w-8 h-8" />;
  };

  // Property size configurations by property type
  const propertyTypeConfigs = {
    apartment: [
      { 
        id: 'studio', 
        name: 'Studio', 
        description: 'Under 600 sq ft',
        rooms: 'Open layout, 1 bath',
        time: '1-1.5 hours',
        price: 'From $75',
        basePrice: 75,
        popular: false 
      },
      { 
        id: '1bed', 
        name: '1 Bedroom', 
        description: '600-900 sq ft',
        rooms: '1 bedroom, 1 bath',
        time: '1.5-2 hours',
        price: 'From $95',
        basePrice: 95,
        popular: true 
      },
      { 
        id: '2bed', 
        name: '2 Bedroom', 
        description: '900-1,200 sq ft',
        rooms: '2 bedrooms, 1-2 baths',
        time: '2-2.5 hours',
        price: 'From $130',
        basePrice: 130,
        popular: false 
      },
      { 
        id: '3bed', 
        name: '3+ Bedroom', 
        description: 'Over 1,200 sq ft',
        rooms: '3+ bedrooms, 2+ baths',
        time: '2.5-3 hours',
        price: 'From $170',
        basePrice: 170,
        popular: false 
      }
    ],
    house: [
      { 
        id: 'small-house', 
        name: '2-3 Bedroom House', 
        description: '1,000-1,800 sq ft',
        rooms: '2-3 bedrooms, 1-2 baths',
        time: '2-3 hours',
        price: 'From $140',
        basePrice: 140,
        popular: true 
      },
      { 
        id: 'medium-house', 
        name: '3-4 Bedroom House', 
        description: '1,800-2,500 sq ft',
        rooms: '3-4 bedrooms, 2-3 baths',
        time: '3-4 hours',
        price: 'From $190',
        basePrice: 190,
        popular: false 
      },
      { 
        id: 'large-house', 
        name: '4-5 Bedroom House', 
        description: '2,500-3,500 sq ft',
        rooms: '4-5 bedrooms, 3-4 baths',
        time: '4-5 hours',
        price: 'From $250',
        basePrice: 250,
        popular: false 
      },
      { 
        id: 'xl-house', 
        name: '5+ Bedroom House', 
        description: 'Over 3,500 sq ft',
        rooms: '5+ bedrooms, 4+ baths',
        time: '5+ hours',
        price: 'From $320',
        basePrice: 320,
        popular: false 
      }
    ],
    condo: [
      { 
        id: 'small-condo', 
        name: '1-2 Bedroom Condo', 
        description: '700-1,100 sq ft',
        rooms: '1-2 bedrooms, 1-2 baths',
        time: '1.5-2.5 hours',
        price: 'From $110',
        basePrice: 110,
        popular: true 
      },
      { 
        id: 'medium-condo', 
        name: '2-3 Bedroom Condo', 
        description: '1,100-1,600 sq ft',
        rooms: '2-3 bedrooms, 2 baths',
        time: '2.5-3.5 hours',
        price: 'From $155',
        basePrice: 155,
        popular: false 
      },
      { 
        id: 'large-condo', 
        name: '3+ Bedroom Condo', 
        description: 'Over 1,600 sq ft',
        rooms: '3+ bedrooms, 2-3 baths',
        time: '3.5-4 hours',
        price: 'From $200',
        basePrice: 200,
        popular: false 
      }
    ],
    townhouse: [
      { 
        id: 'small-townhouse', 
        name: '2-3 Bedroom Townhouse', 
        description: '1,200-1,800 sq ft',
        rooms: '2-3 bedrooms, 1.5-2.5 baths',
        time: '2.5-3.5 hours',
        price: 'From $160',
        basePrice: 160,
        popular: true 
      },
      { 
        id: 'medium-townhouse', 
        name: '3-4 Bedroom Townhouse', 
        description: '1,800-2,400 sq ft',
        rooms: '3-4 bedrooms, 2.5-3.5 baths',
        time: '3.5-4.5 hours',
        price: 'From $210',
        basePrice: 210,
        popular: false 
      },
      { 
        id: 'large-townhouse', 
        name: '4+ Bedroom Townhouse', 
        description: 'Over 2,400 sq ft',
        rooms: '4+ bedrooms, 3+ baths',
        time: '4.5+ hours',
        price: 'From $270',
        basePrice: 270,
        popular: false 
      }
    ]
  };

  // Get property sizes based on selected property type
  const getPropertySizes = () => {
    if (!formData.propertyType) return [];
    const config = propertyTypeConfigs[formData.propertyType as keyof typeof propertyTypeConfigs];
    return config || [];
  };

  // Add-on services
  const addOnServices = [
    { 
      id: 'deep-clean', 
      name: 'Deep Clean First Visit', 
      description: 'Extra thorough initial cleaning',
      price: 50, 
      icon: <Sparkles className="w-5 h-5" />, 
      recommended: true 
    },
    { 
      id: 'eco-friendly', 
      name: 'Eco-Friendly Products', 
      description: 'Green & non-toxic cleaners',
      price: 20, 
      icon: <Heart className="w-5 h-5" /> 
    },
    { 
      id: 'inside-oven', 
      name: 'Inside Oven Cleaning', 
      description: 'Remove grease & buildup',
      price: 30, 
      icon: <Zap className="w-5 h-5" /> 
    },
    { 
      id: 'inside-fridge', 
      name: 'Inside Fridge Cleaning', 
      description: 'Deep clean & organize',
      price: 30, 
      icon: <Gift className="w-5 h-5" /> 
    },
    { 
      id: 'windows', 
      name: 'Interior Window Washing', 
      description: 'Streak-free shine',
      price: 40, 
      icon: <Shield className="w-5 h-5" /> 
    },
    { 
      id: 'laundry', 
      name: 'Laundry Wash & Fold', 
      description: 'Up to 2 loads',
      price: 25, 
      icon: <Users className="w-5 h-5" /> 
    }
  ];

  // Enhanced time slots with conversion psychology
  const timeSlots = [
    { 
      id: 'morning', 
      name: '8:00 AM - 11:00 AM', 
      label: 'Morning',
      available: true,
      slots: 5,
      urgency: 'Many available',
      urgencyColor: 'text-green-600'
    },
    { 
      id: 'midday', 
      name: '11:00 AM - 2:00 PM', 
      label: 'Midday',
      available: true, 
      popular: true,
      slots: 2,
      urgency: 'Only 2 slots left!',
      urgencyColor: 'text-orange-600'
    },
    { 
      id: 'afternoon', 
      name: '2:00 PM - 5:00 PM', 
      label: 'Afternoon',
      available: true,
      slots: 3,
      urgency: 'Limited availability',
      urgencyColor: 'text-yellow-600'
    },
    { 
      id: 'evening', 
      name: '5:00 PM - 7:00 PM', 
      label: 'Evening',
      available: false,
      slots: 0,
      urgency: 'Fully booked',
      urgencyColor: 'text-red-600'
    }
  ];

  // Step validation with real-time feedback
  const isStepValid = (step: number) => {
    switch (step) {
      case 0:
        return formData.propertyType && formData.propertySize;
      case 1:
        return formData.preferredDate && formData.preferredTime;
      case 2:
        return true; // Add-ons are optional
      case 3: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode'];
        return requiredFields.every(field => formData[field as keyof BookingFormData]);
      }
      default:
        return false;
    }
  };

  // Handle form submission with standardized flow
  const handleSubmit = async () => {
    try {
      // Validate current step
      if (!isStepValid(3)) {
        // Validate all fields to show specific errors
        validateAllFields(formData);
        return;
      }

      // Submit booking using standardized service
      await submitBooking(formData, () => setShowPaymentModal(true));

    } catch (error) {
      console.error('Form submission error:', error);
      // Error is already handled by the hook
    }
  };

  // Handle field changes with validation
  const handleFieldChange = (field: keyof BookingFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (validationErrors[field]) {
      clearFieldError(field);
    }

    // Validate field in real-time for better UX
    setTimeout(() => {
      validateField(field, value, { ...formData, [field]: value });
    }, 500);
  };

  // Calendar state
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Get calendar dates for current month
  const getCalendarDates = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    const dates = [];
    
    // Add empty cells for days before month starts
    const startPadding = firstDay.getDay();
    for (let i = 0; i < startPadding; i++) {
      dates.push(null);
    }
    
    // Add all days of the month
    for (let day = 1; day <= lastDay.getDate(); day++) {
      dates.push(new Date(year, month, day));
    }
    
    return dates;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(newDate.getMonth() - 1);
      } else {
        newDate.setMonth(newDate.getMonth() + 1);
      }
      return newDate;
    });
  };

  // Step components
  const steps = [
    {
      title: 'Property Details',
      subtitle: 'Tell us about your space',
      icon: <Home className="w-6 h-6" />
    },
    {
      title: 'Service Schedule',
      subtitle: 'Choose date and timing',
      icon: <Calendar className="w-6 h-6" />
    },
    {
      title: 'Customize Service',
      subtitle: 'Add extra services (optional)',
      icon: <Sparkles className="w-6 h-6" />
    },
    {
      title: 'Contact Info',
      subtitle: 'Complete your booking',
      icon: <CheckCircle className="w-6 h-6" />
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Header */}
      <div className="bg-white shadow-sm sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Sparkles className="w-8 h-8 text-brand-600" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Book Regular House Cleaning</h1>
                <p className="text-sm text-gray-600">Professional recurring service for your home</p>
              </div>
            </div>
            
            {/* Enhanced Trust badges */}
            <div className="hidden md:flex items-center gap-6">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-brand-600" />
                <span className="text-sm font-medium">Fully Insured & Bonded</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                <span className="text-sm font-medium">4.9★ (2,847 reviews)</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-sm font-medium">100% Satisfaction Guarantee</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex items-center">
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ 
                      scale: currentStep === index ? 1.1 : 1,
                      backgroundColor: currentStep >= index ? '#638907' : '#e5e7eb'
                    }}
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-white transition-all`}
                  >
                    {currentStep > index ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className={currentStep >= index ? 'text-white' : 'text-gray-600'}>
                        {index + 1}
                      </span>
                    )}
                  </motion.div>
                  <div className="hidden md:block ml-3">
                    <p className={`font-medium ${currentStep >= index ? 'text-brand-600' : 'text-gray-500'}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500">{step.subtitle}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <ChevronRight className={`mx-4 w-5 h-5 ${
                    currentStep > index ? 'text-brand-600' : 'text-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              {/* Step 0: Property Details */}
              {currentStep === 0 && (
                <motion.div
                  key="step0"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-4 sm:p-6 lg:p-8"
                >
                  <div className="mb-6 sm:mb-8">
                    <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2 leading-tight">What type of property?</h2>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">This helps us assign the right team and equipment</p>
                  </div>

                  {/* Property Type Selection */}
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-sm font-semibold text-gray-700">Property Type</label>
                      <span className="text-xs text-gray-500">All types welcome!</span>
                    </div>
                    {/* Mobile-optimized property type selection */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                      {propertyTypes.map((type) => (
                        <motion.button
                          key={type.id}
                          whileHover={{ scale: 1.01 }}
                          whileTap={{ scale: 0.99 }}
                          onClick={() => setFormData({ 
                            ...formData, 
                            propertyType: type.id,
                            propertySize: '' // Reset property size when type changes
                          })}
                          className={`relative p-4 sm:p-6 rounded-2xl border-2 transition-all text-left touch-manipulation ${
                            formData.propertyType === type.id
                              ? 'border-brand-500 bg-brand-50 shadow-md ring-2 ring-brand-200'
                              : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-sm'
                          }`}
                        >
                          {/* Selection indicator for mobile */}
                          {formData.propertyType === type.id && (
                            <div className="absolute -top-1 -right-1 w-6 h-6 bg-brand-500 rounded-full flex items-center justify-center">
                              <CheckCircle className="w-4 h-4 text-white" />
                            </div>
                          )}
                          
                          <div className={`mb-2 sm:mb-3 ${
                            formData.propertyType === type.id ? 'text-brand-600' : 'text-gray-600'
                          }`}>
                            <div className="w-6 h-6 sm:w-8 sm:h-8">
                              {type.icon}
                            </div>
                          </div>
                          <h3 className="font-semibold text-gray-900 text-base sm:text-lg leading-tight">{type.name}</h3>
                          <p className="text-sm text-gray-500 mt-1 leading-relaxed">{type.description}</p>
                          <div className="mt-2 sm:mt-3 space-y-1">
                            <div className="flex items-center gap-2 text-xs text-gray-600">
                              <Home className="w-3 h-3 flex-shrink-0" />
                              <span className="truncate">{type.floors}</span>
                            </div>
                            <div className="flex items-center gap-2 text-xs text-gray-600">
                              <Clock className="w-3 h-3 flex-shrink-0" />
                              <span className="truncate">{type.time}</span>
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Property Size - Dynamic based on property type */}
                  {formData.propertyType && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-8"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <label className="block text-sm font-semibold text-gray-700">
                          {formData.propertyType.charAt(0).toUpperCase() + formData.propertyType.slice(1)} Size
                        </label>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          onClick={() => {/* Could open a size guide modal */}}
                          className="text-xs text-brand-600 hover:text-brand-700 flex items-center gap-1"
                        >
                          <Info className="w-3 h-3" />
                          Size guide
                        </motion.button>
                      </div>
                      {/* Mobile-optimized property size selection */}
                      <div className="space-y-3 sm:space-y-4">
                        {getPropertySizes().map((size) => (
                        <motion.button
                          key={size.id}
                          whileHover={{ scale: 1.01 }}
                          whileTap={{ scale: 0.99 }}
                          onClick={() => setFormData({ ...formData, propertySize: size.id })}
                          className={`relative w-full p-4 sm:p-6 rounded-2xl border-2 transition-all text-left ${
                            formData.propertySize === size.id
                              ? 'border-brand-500 bg-brand-50 shadow-md ring-2 ring-brand-200'
                              : 'border-gray-200 hover:border-gray-300 bg-white hover:shadow-sm'
                          }`}
                        >
                          {/* Popular badge for mobile */}
                          {size.popular && (
                            <div className="absolute -top-2 right-3 sm:right-4 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                              🔥 Popular
                            </div>
                          )}

                          <div className="flex items-start gap-3 sm:gap-4">
                            {/* Icon - smaller on mobile */}
                            <div className={`p-2 sm:p-3 rounded-xl transition-colors flex-shrink-0 ${
                              formData.propertySize === size.id
                                ? 'bg-brand-100 text-brand-600'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              <div className="w-6 h-6 sm:w-8 sm:h-8">
                                {getPropertySizeIcon(size.id)}
                              </div>
                            </div>
                            
                            {/* Content - responsive layout */}
                            <div className="flex-1 min-w-0">
                              <h3 className="font-semibold text-gray-900 text-base sm:text-lg leading-tight">{size.name}</h3>
                              <p className="text-sm text-gray-600 mt-1 leading-relaxed">{size.description}</p>
                              
                              {/* Mobile-optimized info grid */}
                              <div className="mt-3 grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-3">
                                <div className="flex items-center gap-2 text-xs text-gray-600">
                                  <Home className="w-3 h-3 flex-shrink-0" />
                                  <span className="truncate">{size.rooms}</span>
                                </div>
                                <div className="flex items-center gap-2 text-xs text-gray-600">
                                  <Clock className="w-3 h-3 flex-shrink-0" />
                                  <span className="truncate">~{size.time}</span>
                                </div>
                                <div className="flex items-center gap-2 text-xs font-medium text-brand-600 xs:col-span-2 sm:col-span-1">
                                  <DollarSign className="w-3 h-3 flex-shrink-0" />
                                  <span className="truncate">{size.price}</span>
                                </div>
                              </div>
                            </div>
                            
                            {/* Selection indicator */}
                            {formData.propertySize === size.id && (
                              <div className="flex-shrink-0">
                                <CheckCircle className="w-5 h-5 text-brand-600" />
                              </div>
                            )}
                          </div>
                        </motion.button>
                      ))}
                    </div>
                    
                      {/* Helpful tip */}
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="mt-4 p-4 bg-blue-50 rounded-xl flex items-start gap-3"
                      >
                        <Sparkles className="w-5 h-5 text-blue-600 mt-0.5" />
                        <div className="flex-1">
                          <p className="text-sm text-blue-900 font-medium">Perfect choice!</p>
                          <p className="text-sm text-blue-700 mt-1">
                            Our pricing is based on {formData.propertyType} layout and size. 
                            We'll confirm all details before your first cleaning visit.
                          </p>
                        </div>
                      </motion.div>
                    </motion.div>
                  )}



                  {/* Additional Property Info (Optional) - Mobile Optimized */}
                  <div className="mb-6 sm:mb-8 p-4 sm:p-6 bg-gray-50 rounded-2xl">
                    <h3 className="font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center gap-2 text-sm sm:text-base">
                      <Info className="w-4 h-4 text-gray-600 flex-shrink-0" />
                      <span>Additional Information (Optional)</span>
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 sm:mb-4 leading-relaxed">
                      Help us prepare better for your cleaning:
                    </p>
                    <div className="space-y-3 sm:space-y-4">
                      <label className="flex items-start gap-3 cursor-pointer touch-manipulation">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700 leading-relaxed">I have pets</span>
                      </label>
                      <label className="flex items-start gap-3 cursor-pointer touch-manipulation">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700 leading-relaxed">Home has multiple levels/stairs</span>
                      </label>
                      <label className="flex items-start gap-3 cursor-pointer touch-manipulation">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700 leading-relaxed">Requires special attention to allergies</span>
                      </label>
                      <label className="flex items-start gap-3 cursor-pointer touch-manipulation">
                        <input type="checkbox" className="w-4 h-4 text-brand-600 rounded mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700 leading-relaxed">Has areas that need extra care</span>
                      </label>
                    </div>
                  </div>

                  {/* Continue Button */}
                  <Button
                    onClick={() => setCurrentStep(1)}
                    disabled={!isStepValid(0)}
                    className="w-full"
                    size="lg"
                  >
                    Continue
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>
                </motion.div>
              )}

              {/* Step 1: Service Schedule */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose your cleaning date</h2>
                    <p className="text-gray-600">Pick your preferred date and time for the service</p>
                  </div>

                  {/* Premium Calendar Experience */}
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mb-8"
                  >
                    {/* Enhanced Header */}
                    <div className="text-center mb-6">
                      <div className="flex items-center justify-center gap-3 mb-2">
                        <Calendar className="w-6 h-6 text-green-600" />
                        <h3 className="text-2xl font-bold text-gray-900">Choose Your Perfect Day</h3>
                      </div>
                      <p className="text-gray-600">Select the date that works best for your schedule</p>
                    </div>
                    
                    {/* Premium Calendar Widget */}
                    <div className="flex justify-center">
                      <motion.div 
                        whileHover={{ scale: 1.02 }}
                        className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-8 w-full max-w-md"
                      >
                        {/* Elegant Month Header */}
                        <div className="flex items-center justify-between mb-8">
                          <motion.h4 
                            key={currentMonth.getTime()}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent"
                          >
                            {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                          </motion.h4>
                          <div className="flex items-center gap-2">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => navigateMonth('prev')}
                              className="p-3 rounded-xl bg-gray-100 hover:bg-green-100 hover:text-green-700 transition-all duration-200 group"
                            >
                              <ChevronLeft className="w-5 h-5 text-gray-600 group-hover:text-green-700" />
                            </motion.button>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => navigateMonth('next')}
                              className="p-3 rounded-xl bg-gray-100 hover:bg-green-100 hover:text-green-700 transition-all duration-200 group"
                            >
                              <ChevronRight className="w-5 h-5 text-gray-600 group-hover:text-green-700" />
                            </motion.button>
                          </div>
                        </div>
                        
                        {/* Stylish Day Headers */}
                        <div className="grid grid-cols-7 gap-3 mb-4">
                          {['SU', 'MO', 'TU', 'WE', 'TH', 'FR', 'SA'].map((day, i) => (
                            <div key={day} className={`text-center text-xs font-bold tracking-wider py-3 ${
                              i === 0 || i === 6 ? 'text-green-600' : 'text-gray-600'
                            }`}>
                              {day}
                            </div>
                          ))}
                        </div>
                        
                        {/* Interactive Date Grid */}
                        <div className="grid grid-cols-7 gap-3">
                          {getCalendarDates().map((date, index) => {
                            if (!date) {
                              return <div key={`empty-${index}`} className="h-12"></div>;
                            }
                            
                            const dateStr = date.toLocaleDateString('en-US', { 
                              month: 'short', 
                              day: 'numeric',
                              year: 'numeric'
                            });
                            const isToday = date.toDateString() === new Date().toDateString();
                            const isSelected = formData.preferredDate === dateStr;
                            const isPast = date < new Date(new Date().setHours(0, 0, 0, 0));
                            const isWeekend = date.getDay() === 0 || date.getDay() === 6;
                            
                            return (
                              <motion.button
                                key={index}
                                whileHover={!isPast ? { scale: 1.1, y: -2 } : {}}
                                whileTap={!isPast ? { scale: 0.95 } : {}}
                                onClick={() => !isPast && setFormData({ ...formData, preferredDate: dateStr })}
                                disabled={isPast}
                                className={`relative h-12 w-full text-sm font-semibold rounded-xl transition-all duration-300 flex items-center justify-center group ${
                                  isPast
                                    ? 'text-gray-300 cursor-not-allowed bg-gray-50'
                                    : isSelected
                                      ? 'bg-gradient-to-br from-green-500 to-green-600 text-white shadow-lg shadow-green-500/30 ring-2 ring-green-300'
                                      : isToday
                                        ? 'bg-gradient-to-br from-orange-400 to-red-500 text-white shadow-lg shadow-orange-500/30'
                                        : isWeekend
                                          ? 'text-green-600 hover:bg-green-50 hover:shadow-md border-2 border-transparent hover:border-green-200'
                                          : 'text-gray-700 hover:bg-blue-50 hover:text-blue-700 hover:shadow-md border-2 border-transparent hover:border-blue-200'
                                }`}
                              >
                                {/* Special Day Indicators */}
                                {isToday && !isSelected && (
                                  <motion.div 
                                    animate={{ scale: [1, 1.2, 1] }}
                                    transition={{ duration: 2, repeat: Infinity }}
                                    className="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full shadow-sm"
                                  />
                                )}
                                
                                {isSelected && (
                                  <motion.div 
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    className="absolute -top-1 -right-1 w-4 h-4 bg-white rounded-full flex items-center justify-center"
                                  >
                                    <CheckCircle className="w-3 h-3 text-green-600" />
                                  </motion.div>
                                )}
                                
                                <span className="relative z-10">{date.getDate()}</span>
                                
                                {/* Hover glow effect */}
                                {!isPast && !isSelected && (
                                  <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                                )}
                              </motion.button>
                            );
                          })}
                        </div>
                      </motion.div>
                    </div>
                    
                    {/* Elegant Selected Date Display */}
                    <AnimatePresence>
                      {formData.preferredDate && (
                        <motion.div
                          initial={{ opacity: 0, y: 20, scale: 0.9 }}
                          animate={{ opacity: 1, y: 0, scale: 1 }}
                          exit={{ opacity: 0, y: -10, scale: 0.9 }}
                          className="flex justify-center mt-6"
                        >
                          <div className="relative">
                            <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-xl shadow-lg">
                              <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-600 rounded-full">
                                  <CheckCircle className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                  <p className="text-sm font-medium text-green-800">Perfect! Your date is set</p>
                                  <p className="text-lg font-bold text-green-900">{formData.preferredDate}</p>
                                </div>
                              </div>
                            </div>
                            {/* Subtle glow effect */}
                            <div className="absolute inset-0 rounded-xl bg-green-200/30 blur-xl -z-10" />
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>

                  {/* Enhanced Time Selection */}
                  <div className="mb-8">
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-lg font-semibold text-gray-900">Choose Your Time</label>
                      <div className="flex items-center gap-2 text-xs text-gray-600">
                        <Clock className="w-3 h-3" />
                        <span>Times filling up fast today</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {timeSlots.map((slot) => (
                        <motion.button
                          key={slot.id}
                          whileHover={slot.available ? { scale: 1.02, y: -2 } : {}}
                          whileTap={slot.available ? { scale: 0.98 } : {}}
                          onClick={() => slot.available && setFormData({ ...formData, preferredTime: slot.id })}
                          disabled={!slot.available}
                          className={`relative p-5 rounded-2xl border-2 transition-all ${
                            formData.preferredTime === slot.id
                              ? 'border-brand-500 bg-brand-50 shadow-lg ring-2 ring-brand-200'
                              : slot.available
                                ? 'border-gray-200 hover:border-brand-300 hover:shadow-md bg-white'
                                : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-50'
                          }`}
                        >
                          {slot.popular && slot.available && (
                            <span className="absolute -top-2 right-4 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-3 py-1 rounded-full font-medium">
                              🔥 Most Popular
                            </span>
                          )}
                          
                          {slot.slots <= 2 && slot.available && !slot.popular && (
                            <span className="absolute -top-2 right-4 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs px-3 py-1 rounded-full font-medium animate-pulse">
                              ⚡ Almost Full
                            </span>
                          )}

                          <div className="flex items-start gap-4">
                            <div className={`p-2 rounded-lg ${
                              formData.preferredTime === slot.id 
                                ? 'bg-brand-100 text-brand-600' 
                                : slot.available 
                                  ? 'bg-gray-100 text-gray-600' 
                                  : 'bg-gray-100 text-gray-400'
                            }`}>
                              <Clock className="w-6 h-6" />
                            </div>
                            <div className="text-left flex-1">
                              <div className="font-semibold text-gray-900">{slot.label}</div>
                              <div className="text-sm text-gray-600 mb-2">{slot.name}</div>
                              <div className={`text-xs font-medium ${slot.urgencyColor}`}>
                                {slot.urgency}
                              </div>
                              {slot.available && slot.slots <= 3 && (
                                <div className="flex items-center gap-1 mt-1">
                                  <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-pulse"></div>
                                  <span className="text-xs text-orange-600">High demand</span>
                                </div>
                              )}
                            </div>
                            {formData.preferredTime === slot.id && (
                              <CheckCircle className="w-5 h-5 text-brand-600" />
                            )}
                          </div>
                        </motion.button>
                      ))}
                    </div>
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800">
                        💡 <strong>Pro tip:</strong> Morning slots have the freshest teams and best availability for follow-up services.
                      </p>
                    </div>
                  </div>

                  {/* Special Instructions */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Special Instructions (Optional)
                    </label>
                    <textarea
                      placeholder="Any specific areas to focus on, allergies, gate codes, etc."
                      value={formData.specialInstructions}
                      onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors resize-none"
                      rows={3}
                    />
                  </div>

                  {/* Navigation */}
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStep(0)}
                      className="flex-1"
                    >
                      Back
                    </Button>
                    <Button
                      onClick={() => setCurrentStep(2)}
                      disabled={!isStepValid(1)}
                      className="flex-1"
                    >
                      Continue
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Add-ons */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Enhance your service</h2>
                    <p className="text-gray-600">Add extra services to customize your cleaning</p>
                  </div>

                  {/* Add-on Services */}
                  <div className="mb-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {addOnServices.map((addon) => (
                        <motion.button
                          key={addon.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => {
                            const newAddOns = formData.addOns.includes(addon.id)
                              ? formData.addOns.filter(a => a !== addon.id)
                              : [...formData.addOns, addon.id];
                            setFormData({ ...formData, addOns: newAddOns });
                          }}
                          className={`relative p-5 rounded-2xl border-2 transition-all text-left ${
                            formData.addOns.includes(addon.id)
                              ? 'border-brand-500 bg-brand-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          {addon.recommended && (
                            <span className="absolute -top-2 right-4 bg-warm-500 text-white text-xs px-2 py-0.5 rounded-full">
                              Recommended
                            </span>
                          )}
                          <div className="flex items-start gap-3">
                            <div className={formData.addOns.includes(addon.id) ? 'text-brand-600' : 'text-gray-600'}>
                              {addon.icon}
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900">{addon.name}</h3>
                              <p className="text-xs text-gray-600">{addon.description}</p>
                              <p className="text-sm font-medium text-brand-600 mt-1">+${addon.price}</p>
                            </div>
                            {formData.addOns.includes(addon.id) && (
                              <CheckCircle className="w-5 h-5 text-brand-600" />
                            )}
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Why Add-ons */}
                  <div className="bg-brand-50 rounded-2xl p-6 mb-8">
                    <h3 className="font-semibold text-gray-900 mb-3">Why choose add-ons?</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-brand-600" />
                        <span className="text-sm text-gray-700">Save time by bundling services</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-brand-600" />
                        <span className="text-sm text-gray-700">Professional grade equipment for best results</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-brand-600" />
                        <span className="text-sm text-gray-700">Trained specialists for each service</span>
                      </div>
                    </div>
                  </div>

                  {/* Navigation */}
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStep(1)}
                      className="flex-1"
                    >
                      Back
                    </Button>
                    <Button
                      onClick={() => setCurrentStep(3)}
                      className="flex-1"
                    >
                      Continue
                      <ArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Contact Info */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-soft p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Almost done!</h2>
                    <p className="text-gray-600">Enter your details to complete the booking</p>
                  </div>

                  {/* Contact Form */}
                  <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input
                          type="text"
                          value={formData.firstName}
                          onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                          placeholder="John"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input
                          type="text"
                          value={formData.lastName}
                          onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                          placeholder="Doe"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                        className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                        placeholder="(*************"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Service Address</label>
                      <input
                        type="text"
                        value={formData.address}
                        onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                        className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                        placeholder="123 Main St"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                        <input
                          type="text"
                          value={formData.city}
                          onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                          placeholder="New York"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">ZIP Code</label>
                        <input
                          type="text"
                          value={formData.zipCode}
                          onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                          placeholder="10001"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">How did you hear about us?</label>
                      <select
                        value={formData.howDidYouHear}
                        onChange={(e) => setFormData({ ...formData, howDidYouHear: e.target.value })}
                        className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-brand-500 focus:outline-none transition-colors"
                      >
                        <option value="">Select an option</option>
                        <option value="google">Google Search</option>
                        <option value="facebook">Facebook</option>
                        <option value="referral">Friend/Family Referral</option>
                        <option value="yelp">Yelp</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div>
                      <label className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          checked={formData.newsletter}
                          onChange={(e) => setFormData({ ...formData, newsletter: e.target.checked })}
                          className="w-4 h-4 text-brand-600 border-gray-300 rounded focus:ring-brand-500"
                        />
                        <span className="text-sm text-gray-700">
                          Send me tips and exclusive offers (you can unsubscribe anytime)
                        </span>
                      </label>
                    </div>
                  </div>

                  {/* Enhanced Risk Reduction & Terms */}
                  <div className="mt-8 mb-8 space-y-4">
                    {/* Security & Guarantee */}
                    <div className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200">
                      <div className="flex items-center gap-3 mb-2">
                        <Shield className="w-5 h-5 text-green-600" />
                        <h3 className="font-semibold text-green-900">100% Risk-Free Booking</h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm text-green-800">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4" />
                          <span>Free cancellation anytime</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4" />
                          <span>Easy rescheduling</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4" />
                          <span>Satisfaction guaranteed</span>
                        </div>
                      </div>
                    </div>

                    {/* Terms */}
                    <div className="p-4 bg-gray-50 rounded-xl">
                      <p className="text-sm text-gray-600">
                        By booking, you agree to our Terms of Service and Privacy Policy. 
                        Your information is secure and will never be shared. 🔒
                      </p>
                    </div>
                  </div>

                  {/* Navigation */}
                  <div className="flex gap-4">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentStep(2)}
                      className="flex-1"
                    >
                      Back
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      disabled={!isStepValid(3) || isSubmitting}
                      className="flex-1 bg-brand-600 hover:bg-brand-700"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Processing...
                        </div>
                      ) : (
                        <>
                          Complete Booking
                          <CheckCircle className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Enhanced Conversion Sidebar */}
          <div className="lg:sticky lg:top-32 h-fit space-y-6">
            {/* Price Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl shadow-soft p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Price Summary</h3>
                <div className="flex items-center gap-1 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                  <Star className="w-3 h-3 fill-current" />
                  <span className="font-medium">Best Value</span>
                </div>
              </div>
              
              {formData.propertySize ? (
                <>
                  <div className="space-y-3 mb-4">
                    <div className="flex items-baseline justify-between">
                      <span className="text-3xl font-bold text-gray-900">${calculatePrice()}</span>
                      <div className="text-right">
                        <span className="text-gray-600">/service</span>
                        <p className="text-xs text-green-600 font-medium">Save $30+ vs competitors</p>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => setShowPriceDetails(!showPriceDetails)}
                    className="text-sm text-brand-600 hover:text-brand-700 font-medium"
                  >
                    {showPriceDetails ? 'Hide' : 'Show'} price breakdown
                  </button>

                  <AnimatePresence>
                    {showPriceDetails && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-4 pt-4 border-t space-y-2 text-sm"
                      >
                        {formData.propertySize && (
                          <div className="flex justify-between text-gray-600">
                            <span>Base cleaning ({formData.propertySize})</span>
                            <span>${getPropertySizes().find(size => size.id === formData.propertySize)?.basePrice || 120}</span>
                          </div>
                        )}
                        {formData.addOns.map(addon => {
                          const service = addOnServices.find(s => s.id === addon);
                          return service ? (
                            <div key={addon} className="flex justify-between text-gray-600">
                              <span>{service.name}</span>
                              <span>+${service.price}</span>
                            </div>
                          ) : null;
                        })}
                        <div className="border-t pt-2 mt-2">
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>Avg. market price</span>
                            <span className="line-through">${calculatePrice() + 35}</span>
                          </div>
                          <div className="flex justify-between text-green-600 font-medium">
                            <span>Your savings</span>
                            <span>$35</span>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="w-12 h-12 text-gray-300 mx-auto mb-2" />
                  <p className="text-gray-500">Complete the form to see pricing</p>
                </div>
              )}
            </motion.div>

            {/* Enhanced Trust & Testimonial */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gradient-to-br from-brand-50 to-accent-50 rounded-2xl p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Heart className="w-5 h-5 text-red-500" />
                Why 2,847+ Customers Love Us
              </h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">100% Satisfaction Guarantee</p>
                    <p className="text-sm text-gray-600">Free re-clean if not completely happy</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Fully Insured & Bonded</p>
                    <p className="text-sm text-gray-600">$2M liability + background checks</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Star className="w-5 h-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Top-Rated Service</p>
                    <p className="text-sm text-gray-600">4.9/5 stars • Same day booking available</p>
                  </div>
                </div>

                {/* Quick Testimonial */}
                <div className="mt-6 p-4 bg-white rounded-xl border border-gray-100">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 fill-current" />
                      ))}
                    </div>
                    <span className="text-xs text-gray-500">Verified • 2 days ago</span>
                  </div>
                  <p className="text-sm text-gray-700 italic mb-2">
                    "Outstanding service! They cleaned my 3-bedroom house perfectly. The team was professional and thorough. Definitely booking again!"
                  </p>
                  <p className="text-xs text-gray-500">- Sarah Chen, Midtown</p>
                </div>
              </div>
            </motion.div>

            {/* Limited Time Offer */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15 }}
              className="bg-gradient-to-br from-orange-50 to-red-50 border border-orange-200 rounded-2xl p-6"
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-3">
                  <Gift className="w-6 h-6 text-orange-600" />
                  <h3 className="text-lg font-bold text-orange-900">New Customer Special</h3>
                </div>
                <p className="text-2xl font-bold text-orange-600 mb-2">20% OFF</p>
                <p className="text-sm text-orange-800 mb-4">First cleaning + free organizing tips</p>
                <div className="flex items-center justify-center gap-2 text-xs text-orange-700">
                  <Clock className="w-3 h-3" />
                  <span>Ends in 24 hours • Limited slots</span>
                </div>
              </div>
            </motion.div>

            {/* AI Chat Support */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-br from-brand-50 to-accent-50 rounded-2xl p-4"
            >
              <div className="flex items-center gap-2 mb-3">
                <div className="w-8 h-8 bg-brand-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">AI</span>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Need Help?</p>
                  <p className="text-xs text-gray-600">Ask our AI assistant</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full border-brand-200 hover:bg-brand-100"
                onClick={() => {/* TODO: Open chatbot */}}
              >
                <Sparkles className="w-4 h-4 mr-2 text-brand-600" />
                Chat with AI Helper
              </Button>
              <p className="text-xs text-gray-500 mt-2 text-center">Instant answers, 24/7 available</p>
            </motion.div>
          </div>
        </div>
      </div>

      {submissionError && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
          <p className="font-bold">Booking Error</p>
          <p className="text-sm">{submissionError}</p>
        </div>
      )}

      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculatePrice()}
        description={`Regular Cleaning - ${formData.propertyType}`}
        customerEmail={formData.email}
        formData={formData}
        user={user}
      />


    </div>
  );
};

export default BrandAlignedBookingForm;
