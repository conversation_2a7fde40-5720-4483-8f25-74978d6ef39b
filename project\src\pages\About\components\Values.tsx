import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Leaf, Clock, Users } from 'lucide-react';

const values = [
  {
    icon: Shield,
    title: 'Professional Excellence',
    description: 'Background-checked staff and real-time monitoring ensure quality and consistency'
  },
  {
    icon: Leaf,
    title: 'Eco-Friendly Solutions',
    description: 'Using sustainable products and practices for a healthier environment'
  },
  {
    icon: Clock,
    title: '24/7 Support',
    description: 'Round-the-clock availability for all your cleaning needs'
  },
  {
    icon: Users,
    title: 'Customer Focus',
    description: 'Customized cleaning plans and monthly service reports'
  }
];

export function Values() {
  return (
    <section className="py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Values</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            The principles that guide our service excellence
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => {
            const Icon = value.icon;
            return (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="rounded-full bg-brand-100 p-3 w-fit mb-6">
                  <Icon className="w-6 h-6 text-brand-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
