import React from 'react';
import { motion } from 'framer-motion';
import { MousePointerClick, Calendar, Sparkles } from 'lucide-react';

const steps = [
  { icon: MousePointerClick, title: 'Book Online in 60 Seconds', description: 'Select your service and choose a time that works for you.' },
  { icon: Calendar, title: 'We Arrive & Clean', description: 'Our trusted professionals arrive on schedule to transform your home.' },
  { icon: <PERSON>rk<PERSON>, title: 'Relax & Enjoy', description: 'Come home to a spotless space and enjoy our satisfaction guarantee.' }
];

export function Process() {
  return (
    <section className="py-24 sm:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">Our Simple 3-Step Process</h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto">
            Getting a professional cleaning has never been easier.
          </p>
        </motion.div>

        <div className="relative">
          {/* Connecting Line */}
          <div className="absolute left-1/2 top-6 bottom-6 w-0.5 bg-white/10 hidden md:block" />

          <div className="space-y-16">
          {steps.map((step, index) => {
            const Icon = step.icon;
              const isEven = index % 2 === 0;

            return (
              <motion.div
                  key={step.title}
                  initial={{ opacity: 0, x: isEven ? -30 : 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, amount: 0.5 }}
                  transition={{ duration: 0.6, ease: 'easeOut' }}
                  className={`relative flex flex-col md:flex-row items-center ${isEven ? '' : 'md:flex-row-reverse'}`}
                >
                  <div className={`md:w-5/12 text-center ${isEven ? 'md:text-right md:pr-12' : 'md:text-left md:pl-12'}`}>
                    <h3 className="text-2xl font-semibold text-white mb-3">{step.title}</h3>
                    <p className="text-white/70 leading-relaxed">{step.description}</p>
                  </div>
                  
                  <div className="flex-shrink-0 w-24 h-24 rounded-full bg-white/5 border-2 border-white/10 flex items-center justify-center my-4 md:my-0 z-10">
                    <div className="w-16 h-16 rounded-full bg-white/10 border border-white/20 flex items-center justify-center">
                      <Icon className="w-8 h-8 text-white" />
                    </div>
                  </div>

                  <div className="md:w-5/12" />
              </motion.div>
            );
          })}
          </div>
        </div>
      </div>
    </section>
  );
}
