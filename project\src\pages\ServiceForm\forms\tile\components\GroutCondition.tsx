import React from 'react';
import { AlertCircle, Check, History, Shield, Droplets, Brush, Wrench, AlertTriangle } from 'lucide-react';

interface GroutConditionProps {
  condition: {
    condition: string;
    staining: boolean;
    sealingNeeded: boolean;
    repairs: boolean;
    previousTreatments: string[];
  };
  onChange: (condition: any) => void;
}

export function GroutCondition({ condition = {
  condition: '',
  staining: false,
  sealingNeeded: false,
  repairs: false,
  previousTreatments: []
}, onChange }: GroutConditionProps) {
  const conditions = [
    { value: 'excellent', label: 'Excellent', description: 'Minor wear only' },
    { value: 'good', label: 'Good', description: 'Some visible wear' },
    { value: 'fair', label: 'Fair', description: 'Noticeable wear/damage' },
    { value: 'poor', label: 'Poor', description: 'Significant damage' },
    { value: 'severe', label: 'Severely Damaged', description: 'Major repairs needed' }
  ];

  const treatments = [
    { id: 'regular', label: 'Regular Maintenance', icon: Brush },
    { id: 'polish', label: 'Polishing', icon: Droplets },
    { id: 'refinish', label: 'Refinishing', icon: Shield },
    { id: 'wax', label: 'Waxing', icon: Shield },
    { id: 'repair', label: 'Repairs', icon: Wrench },
    { id: 'none', label: 'No Previous Treatment', icon: History }
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <AlertCircle className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Grout Condition</h3>
          <p className="text-gray-600">Tell us about your grout's current state</p>
        </div>
      </div>

      {/* Current Condition */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Current Condition <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {conditions.map((cond) => (
            <label
              key={cond.value}
              className={`relative flex flex-col p-4 rounded-xl cursor-pointer transition-all ${
                condition.condition === cond.value
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                  : 'bg-white border-2 border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="condition"
                value={cond.value}
                checked={condition.condition === cond.value}
                onChange={(e) => onChange({ ...condition, condition: e.target.value })}
                className="sr-only"
              />
              <div className="font-medium text-gray-900 mb-1">{cond.label}</div>
              <p className="text-sm text-gray-600">{cond.description}</p>
            </label>
          ))}
        </div>
      </div>

      {/* Issues & Requirements */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Current Issues
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <label
            className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
              condition.staining
                ? 'border-brand-500 bg-brand-50 shadow-md'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={condition.staining}
              onChange={(e) => onChange({ ...condition, staining: e.target.checked })}
              className="sr-only"
            />
            <AlertCircle className={`w-5 h-5 mr-3 ${
              condition.staining ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Visible Staining</span>
          </label>

          <label
            className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
              condition.sealingNeeded
                ? 'border-brand-500 bg-brand-50 shadow-md'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={condition.sealingNeeded}
              onChange={(e) => onChange({ ...condition, sealingNeeded: e.target.checked })}
              className="sr-only"
            />
            <Shield className={`w-5 h-5 mr-3 ${
              condition.sealingNeeded ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Sealing Required</span>
          </label>

          <label
            className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
              condition.repairs
                ? 'border-brand-500 bg-brand-50 shadow-md'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={condition.repairs}
              onChange={(e) => onChange({ ...condition, repairs: e.target.checked })}
              className="sr-only"
            />
            <Wrench className={`w-5 h-5 mr-3 ${
              condition.repairs ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Repairs Needed</span>
          </label>
        </div>
      </div>

      {/* Previous Treatments */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Previous Treatments
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {treatments.map((treatment) => {
            const Icon = treatment.icon;
            return (
              <label
                key={treatment.id}
                className={`flex items-center p-4 rounded-xl border-2 transition-all cursor-pointer ${
                  condition.previousTreatments.includes(treatment.id)
                    ? 'border-brand-500 bg-brand-50 shadow-md'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={condition.previousTreatments.includes(treatment.id)}
                  onChange={(e) => {
                    const newTreatments = e.target.checked
                      ? [...condition.previousTreatments, treatment.id]
                      : condition.previousTreatments.filter(t => t !== treatment.id);
                    onChange({ ...condition, previousTreatments: newTreatments });
                  }}
                  className="sr-only"
                />
                <Icon className={`w-5 h-5 mr-3 ${
                  condition.previousTreatments.includes(treatment.id) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{treatment.label}</span>
              </label>
            );
          })}
        </div>
      </div>

      {/* Warning for damaged condition */}
      {condition.condition === 'severe' && (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
          <div className="flex items-start">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Repair Assessment Required</h4>
              <p className="mt-1 text-sm text-yellow-700">
                Due to the damaged condition of your grout, our team may need to perform a detailed assessment to determine the best repair approach. This may affect the final service quote.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
