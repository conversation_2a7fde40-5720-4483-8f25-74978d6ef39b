import React from 'react';
import { ArrowRight } from 'lucide-react';
import type { Industry } from './types';

interface IndustryCardProps {
  industry: Industry;
}

export function IndustryCard({ industry }: IndustryCardProps) {
  const Icon = industry.icon;
  
  return (
    <div className="group relative bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-md hover:shadow-xl transition-all duration-300 h-full flex flex-col">
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-brand-400 to-brand-600 rounded-t-xl opacity-0 group-hover:opacity-100 transition-opacity" />
      
      <div className="flex items-start space-x-3 sm:space-x-4">
        <div className="rounded-xl bg-brand-50 p-3 sm:p-4 group-hover:bg-brand-100 transition-colors flex-shrink-0">
          <Icon className="w-6 h-6 sm:w-8 sm:h-8 text-brand-600" />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-lg sm:text-xl font-semibold text-gray-900">{industry.name}</h3>
          <p className="mt-2 text-sm sm:text-base text-gray-600 leading-relaxed">{industry.description}</p>
        </div>
      </div>
      
      <div className="mt-6 sm:mt-8 space-y-3 sm:space-y-4 flex-1">
        {industry.features.map((feature, index) => (
          <div 
            key={index} 
            className="flex items-center text-gray-700 bg-brand-50/50 rounded-lg p-2 sm:p-3 transition-colors group-hover:bg-brand-50"
          >
            <ArrowRight className="w-4 h-4 mr-2 sm:mr-3 text-brand-500 flex-shrink-0" />
            <span className="text-sm sm:text-base">{feature}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
