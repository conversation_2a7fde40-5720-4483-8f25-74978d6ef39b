import { useState } from 'react';
import { 
  PaymentForm, 
  CreditCard,
  ApplePay,
  GooglePay,
  PaymentFormProps
} from 'react-square-web-payments-sdk';
import { User } from '@supabase/supabase-js';
import { Loader, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from './ui/Button';
import { StandardizedBookingData } from '../lib/api/bookingService';

interface SquarePaymentFormProps {
  amount: number; // Amount in cents
  booking: StandardizedBookingData;
  user: User | null;
  onSuccess: (paymentDetails: { success: boolean; paymentRecordId?: string; [key: string]: unknown }) => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

export function SquarePaymentForm({ 
  amount, 
  booking, 
  user, 
  onSuccess, 
  onError, 
  onCancel 
}: SquarePaymentFormProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');

  const applicationId = import.meta.env.VITE_SQUARE_APPLICATION_ID;
  const locationId = import.meta.env.VITE_SQUARE_LOCATION_ID;
  
  if (!applicationId || !locationId) {
    return (
      <div className="p-4 bg-red-500/20 border border-red-400/30 rounded-xl">
        <h3 className="text-red-300 font-semibold">Configuration Error</h3>
        <p className="text-red-200 text-sm">
          Square payment configuration is missing. Please check your environment variables.
        </p>
      </div>
    );
  }

  const handlePaymentMethodSubmission: PaymentFormProps['cardTokenizeResponseReceived'] = async (token, _buyer) => {
    setIsProcessing(true);
    setPaymentStatus('processing');
    setErrorMessage(null);
    
    try {
      // Call our Edge Function to process the payment
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/process-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': import.meta.env.VITE_SUPABASE_ANON_KEY,
        },
        body: JSON.stringify({
          sourceId: token.token,
          amount: amount, // Already in cents
          bookingId: booking.id,
          userId: user?.id,
          customerEmail: booking.contact.email,
          description: `${booking.service_type} - ${booking.property_details.propertyType || 'Service'}`,
          serviceType: booking.service_type,
          formData: booking
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Payment processing error:', errorText);
        throw new Error(`Payment failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        setPaymentStatus('success');
        // Call the success callback with payment details
        onSuccess(result);
      } else {
        setPaymentStatus('error');
        setErrorMessage(result.error || 'Payment processing failed');
        onError(result.error || 'Payment processing failed');
      }
    } catch (error) {
      setPaymentStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Payment processing failed';
      setErrorMessage(errorMessage);
      onError(errorMessage);
      console.error('Payment submission error:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="w-full">
      {/* Payment Status Display */}
      {paymentStatus === 'processing' && (
        <div className="mb-6 p-4 bg-blue-500/20 border border-blue-400/30 rounded-xl flex items-center">
          <Loader className="w-5 h-5 text-blue-400 mr-3 animate-spin" />
          <p className="text-blue-300 text-sm">Processing your payment...</p>
        </div>
      )}
      
      {paymentStatus === 'success' && (
        <div className="mb-6 p-4 bg-green-500/20 border border-green-400/30 rounded-xl flex items-center">
          <CheckCircle className="w-5 h-5 text-green-400 mr-3" />
          <p className="text-green-300 text-sm">Payment successful! Redirecting...</p>
        </div>
      )}
      
      {errorMessage && (
        <div className="mb-6 p-4 bg-red-500/20 border border-red-400/30 rounded-xl flex items-start">
          <AlertCircle className="w-5 h-5 text-red-400 mr-3 flex-shrink-0 mt-0.5" />
          <p className="text-red-300 text-sm">{errorMessage}</p>
        </div>
      )}

      {/* Amount Display */}
      <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-4 mb-6 shadow-lg">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          <div>
            <p className="text-xs sm:text-sm text-gray-300 mb-1">Total Amount</p>
            <p className="text-2xl sm:text-3xl font-bold text-white">${(amount / 100).toFixed(2)}</p>
          </div>
          <div className="flex items-center text-green-400 text-xs sm:text-sm font-medium">
            <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
            Secure Payment
          </div>
        </div>
      </div>

      {/* Square Payment Form */}
      <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-4 mb-6">
        <PaymentForm
          applicationId={applicationId}
          locationId={locationId}
          cardTokenizeResponseReceived={handlePaymentMethodSubmission}
          createPaymentRequest={() => ({
            countryCode: "US",
            currencyCode: "USD",
            total: {
              amount: (amount / 100).toFixed(2),
              label: "Total",
            }
          })}
        >
          {/* Credit Card Input */}
          <div className="mb-6">
            <h3 className="text-white font-semibold mb-3">Card Details</h3>
            <CreditCard />
          </div>
          
          {/* Digital Wallets */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <ApplePay />
              </div>
              <div className="flex-1">
                <GooglePay />
              </div>
            </div>
          </div>
          
          {/* Submit Button */}
          <div className="mt-6">
            <Button
              type="submit"
              disabled={isProcessing}
              className="w-full h-12 sm:h-14 text-base sm:text-lg font-semibold bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation"
            >
              {isProcessing ? (
                <>
                  <Loader className="w-5 h-5 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                'Pay Now'
              )}
            </Button>
          </div>
        </PaymentForm>
      </div>
      
      {/* Cancel Button */}
      <Button
        variant="outline"
        onClick={onCancel}
        disabled={isProcessing}
        className="w-full bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 touch-manipulation"
      >
        Cancel
      </Button>
    </div>
  );
}
