import React from 'react';
import { Maximize2, Building2, Ruler, Shield, GlassWater, ArrowBigUp } from 'lucide-react';
import { FormLayout } from '../../../../../components/forms/FormLayout';
import { FormSection } from '../../../../../components/forms/FormSection';
import { FormField } from '../../../../../components/forms/FormField';

interface WindowDetailsData {
  windowCount: number;
  windowTypes: string[];
  serviceScope: string;
  specialEquipment: boolean;
  buildingHeight: string;
  surfaceTypes: string[];
  estimatedArea: number;
  specialCoatings: boolean;
  tintedWindows: boolean;
  screenCleaning: boolean;
  trackCleaning: boolean;
  sillCleaning: boolean;
}

interface WindowDetailsProps {
  details: WindowDetailsData;
  onChange: (details: WindowDetailsData) => void;
}

export function WindowDetails({ details, onChange }: WindowDetailsProps) {
  const windowTypes = [
    {
      id: 'standard',
      label: 'Standard Windows',
      description: 'Regular office windows',
      icon: Maximize2
    },
    {
      id: 'floor-ceiling',
      label: 'Floor-to-Ceiling',
      description: 'Full height glass windows',
      icon: Building2
    },
    {
      id: 'skylights',
      label: 'Skylights',
      description: 'Ceiling windows and panels',
      icon: GlassWater
    },
    {
      id: 'storefront',
      label: 'Storefront Windows',
      description: 'Commercial storefront glass',
      icon: Building2
    }
  ];

  const buildingHeights = [
    { value: '1-2', label: '1-2 Stories', icon: ArrowBigUp },
    { value: '3-5', label: '3-5 Stories', icon: ArrowBigUp },
    { value: '6-10', label: '6-10 Stories', icon: ArrowBigUp },
    { value: '11-20', label: '11-20 Stories', icon: ArrowBigUp },
    { value: '20+', label: '20+ Stories', icon: ArrowBigUp }
  ];

  const serviceScopes = [
    {
      id: 'interior',
      label: 'Interior Only',
      description: 'Inside window surfaces',
      icon: Building2
    },
    {
      id: 'exterior',
      label: 'Exterior Only',
      description: 'Outside window surfaces',
      icon: GlassWater
    },
    {
      id: 'both',
      label: 'Interior & Exterior',
      description: 'Complete window cleaning',
      icon: Maximize2
    },
    {
      id: 'full',
      label: 'Full Service',
      description: 'Windows, frames, tracks & sills',
      icon: Shield
    }
  ];

  return (
    <FormLayout
      title="Window Cleaning Details"
      description="Tell us about your windows"
      icon={<Maximize2 className="w-6 h-6 text-brand-600" />}
    >
      {/* Window Types */}
      <FormSection 
        title="Window Types" 
        description="Select all types of windows that need cleaning"
        required
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {windowTypes.map((type) => {
            const Icon = type.icon;
            return (
              <label
                key={type.id}
                className={`group relative flex items-start p-4 rounded-xl cursor-pointer transition-all ${
                  details.windowTypes.includes(type.id)
                    ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                    : 'bg-white border-2 border-gray-200 hover:border-brand-300 hover:shadow-sm'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.windowTypes.includes(type.id)}
                  onChange={(e) => {
                    const newTypes = e.target.checked
                      ? [...details.windowTypes, type.id]
                      : details.windowTypes.filter(t => t !== type.id);
                    onChange({ ...details, windowTypes: newTypes });
                  }}
                  className="sr-only"
                />
                <div className="flex-shrink-0 mr-4">
                  <div className={`p-3 rounded-xl transition-colors ${
                    details.windowTypes.includes(type.id) ? 'bg-brand-100' : 'bg-gray-100'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      details.windowTypes.includes(type.id) ? 'text-brand-600' : 'text-gray-500'
                    }`} />
                  </div>
                </div>
                <div>
                  <div className="font-medium text-gray-900">{type.label}</div>
                  <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                </div>
              </label>
            );
          })}
        </div>
      </FormSection>

      {/* Building Height */}
      <FormSection title="Building Height" required>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {buildingHeights.map((height) => {
            const Icon = height.icon;
            return (
              <label
                key={height.value}
                className={`relative flex flex-col items-center p-4 rounded-xl cursor-pointer transition-all ${
                  details.buildingHeight === height.value
                    ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                    : 'bg-white border-2 border-gray-200 hover:border-brand-300 hover:shadow-sm'
                }`}
              >
                <input
                  type="radio"
                  name="buildingHeight"
                  value={height.value}
                  checked={details.buildingHeight === height.value}
                  onChange={(e) => onChange({ ...details, buildingHeight: e.target.value })}
                  className="sr-only"
                  required
                />
                <Icon className={`w-8 h-8 mb-2 ${
                  details.buildingHeight === height.value ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm font-medium text-center">{height.label}</span>
              </label>
            );
          })}
        </div>
      </FormSection>

      {/* Service Scope */}
      <FormSection title="Service Scope" required>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {serviceScopes.map((scope) => {
            const Icon = scope.icon;
            return (
              <label
                key={scope.id}
                className={`relative flex items-start p-4 rounded-xl cursor-pointer transition-all ${
                  details.serviceScope === scope.id
                    ? 'bg-brand-50 border-2 border-brand-500 shadow-md'
                    : 'bg-white border-2 border-gray-200 hover:border-brand-300 hover:shadow-sm'
                }`}
              >
                <input
                  type="radio"
                  name="serviceScope"
                  value={scope.id}
                  checked={details.serviceScope === scope.id}
                  onChange={(e) => onChange({ ...details, serviceScope: e.target.value })}
                  className="sr-only"
                  required
                />
                <div className="flex-shrink-0 mr-4">
                  <div className={`p-3 rounded-xl transition-colors ${
                    details.serviceScope === scope.id ? 'bg-brand-100' : 'bg-gray-100'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      details.serviceScope === scope.id ? 'text-brand-600' : 'text-gray-500'
                    }`} />
                  </div>
                </div>
                <div>
                  <div className="font-medium text-gray-900">{scope.label}</div>
                  <p className="text-sm text-gray-600 mt-1">{scope.description}</p>
                </div>
              </label>
            );
          })}
        </div>
      </FormSection>

      {/* Area Estimation */}
      <FormSection title="Area Estimation" required>
        <FormField label="Estimated Window Area (sq ft)" required>
          <div className="relative">
            <Ruler className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.estimatedArea || ''}
              onChange={(e) => onChange({ ...details, estimatedArea: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Total window surface area"
              min="1"
              required
            />
          </div>
        </FormField>
      </FormSection>
    </FormLayout>
  );
}
