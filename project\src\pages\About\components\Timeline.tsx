import React from 'react';
import { motion } from 'framer-motion';
import { Building2, Award, Users, MapPin, Shield, Star, Sparkles, TrendingUp } from 'lucide-react';

const milestones = [
  {
    year: '2015',
    icon: Building2,
    title: 'Founded in Queens',
    description: 'Started with a small team serving local businesses in Queens, NY',
    highlight: 'First commercial contract secured'
  },
  {
    year: '2017',
    icon: Award,
    title: 'Quality Recognition',
    description: 'Achieved CIMS (Cleaning Industry Management Standard) certification',
    highlight: 'Expanded to Manhattan & Brooklyn'
  },
  {
    year: '2019',
    icon: Users,
    title: 'Team Growth',
    description: 'Reached 100+ trained cleaning professionals',
    highlight: 'Launched specialized medical facility cleaning division'
  },
  {
    year: '2020',
    icon: Shield,
    title: 'COVID-19 Response',
    description: 'Developed advanced sanitization protocols and emergency response teams',
    highlight: 'Served 500+ essential businesses during pandemic'
  },
  {
    year: '2021',
    icon: Star,
    title: 'Service Excellence',
    description: 'Achieved 98% client satisfaction rate',
    highlight: 'Expanded to New Jersey & Pennsylvania'
  },
  {
    year: '2022',
    icon: MapPin,
    title: 'National Expansion',
    description: 'Established presence in major US cities',
    highlight: 'Opened regional offices in Florida & Texas'
  },
  {
    year: '2023',
    icon: Spark<PERSON>,
    title: 'Innovation Leader',
    description: 'Introduced eco-friendly cleaning solutions and smart monitoring',
    highlight: 'Launched proprietary cleaning management system'
  },
  {
    year: '2024',
    icon: TrendingUp,
    title: 'Continued Growth',
    description: 'Now serving 1000+ businesses across 8 states',
    highlight: 'Recognized as industry leader in commercial cleaning'
  }
];

export function Timeline() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Journey</h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            From our humble beginnings in Queens to becoming a national leader in commercial cleaning
          </p>
        </div>

        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-brand-200" />

          <div className="space-y-16">
            {milestones.map((milestone, index) => {
              const Icon = milestone.icon;
              const isEven = index % 2 === 0;

              return (
                <motion.div
                  key={milestone.year}
                  initial={{ opacity: 0, x: isEven ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className={`relative flex items-center ${
                    isEven ? 'justify-end' : ''
                  }`}
                >
                  {/* Timeline Point */}
                  <div className="absolute left-1/2 transform -translate-x-1/2">
                    <div className="w-12 h-12 rounded-full bg-brand-600 flex items-center justify-center">
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                  </div>

                  {/* Content Card */}
                  <div className={`w-5/12 ${isEven ? 'pr-16 text-right' : 'pl-16'}`}>
                    <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                      <div className="text-2xl font-bold text-brand-600 mb-2">
                        {milestone.year}
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {milestone.title}
                      </h3>
                      <p className="text-gray-600 mb-4">{milestone.description}</p>
                      <div className="bg-brand-50 p-3 rounded-lg">
                        <p className="text-brand-700 font-medium">
                          {milestone.highlight}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
}
