import React from 'react';
import { Home, MapPin, Ruler, DoorOpen, Brush, Building } from 'lucide-react';
import { motion } from 'framer-motion';

interface PropertyDetailsProps {
  details: {
    propertyType: string;
    industryType: string;
    squareFootage: number;
    bedrooms: number;
    bathrooms: number;
    floors: number;
    carpetedAreas: string[];
    propertyAddress: string;
  };
  onChange: (details: any) => void;
}

export function PropertyDetails({ details, onChange }: PropertyDetailsProps) {
  const propertyTypes = [
    'House',
    'Apartment',
    'Condo',
    'Townhouse',
    'Mobile Home',
    'Other'
  ];

  const industryTypes = [
    'Residential',
    'Small Business',
    'Home Office',
    'Rental Property',
    'Vacation Home',
    'Other'
  ];

  const carpetedAreas = [
    'Living Room',
    'Bedrooms',
    'Dining Room',
    'Hallways',
    'Stairs',
    'Basement',
    'Office',
    'Family Room'
  ];

  return (
    <div className="space-y-6">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-6"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Home className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Property Details</h3>
          <p className="text-gray-600">Tell us about your home</p>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Property Type <span className="text-red-500">*</span>
          </label>
          <select
            value={details.propertyType}
            onChange={(e) => onChange({ ...details, propertyType: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select property type</option>
            {propertyTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Industry Type <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={details.industryType}
              onChange={(e) => onChange({ ...details, industryType: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select industry type</option>
              {industryTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Square Footage <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Ruler className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.squareFootage || ''}
              onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Approximate square footage"
              min="1"
              required
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">We charge approximately $0.50 per square foot</p>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Number of Rooms <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <DoorOpen className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.bedrooms || ''}
              onChange={(e) => onChange({ ...details, bedrooms: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              min="1"
              required
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">We charge approximately $90 per room</p>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Bedrooms <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            value={details.bedrooms}
            onChange={(e) => onChange({ ...details, bedrooms: Number(e.target.value) })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            min="0"
            required
          />
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Bathrooms <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            value={details.bathrooms}
            onChange={(e) => onChange({ ...details, bathrooms: Number(e.target.value) })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            min="0"
            step="0.5"
            required
          />
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Floors <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <DoorOpen className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.floors}
              onChange={(e) => onChange({ ...details, floors: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              min="1"
              required
            />
          </div>
        </motion.div>
      </div>

      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="space-y-3"
      >
        <label className="block text-sm font-medium text-gray-700">
          Carpeted Areas <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {carpetedAreas.map((area) => (
            <label
              key={area}
              className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                details.carpetedAreas.includes(area)
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="checkbox"
                checked={details.carpetedAreas.includes(area)}
                onChange={(e) => {
                  const newAreas = e.target.checked
                    ? [...details.carpetedAreas, area]
                    : details.carpetedAreas.filter(a => a !== area);
                  onChange({ ...details, carpetedAreas: newAreas });
                }}
                className="sr-only"
              />
              <Brush className={`w-5 h-5 mr-2 ${
                details.carpetedAreas.includes(area) ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span className="text-sm text-gray-700">{area}</span>
            </label>
          ))}
        </div>
      </motion.div>

      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="space-y-2"
      >
        <label className="block text-sm font-medium text-gray-700">
          Property Address <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={details.propertyAddress}
            onChange={(e) => onChange({ ...details, propertyAddress: e.target.value })}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            placeholder="Enter your complete address"
            required
          />
        </div>
      </motion.div>
    </div>
  );
}
