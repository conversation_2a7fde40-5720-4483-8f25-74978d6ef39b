import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowRight } from 'lucide-react';

// Modern custom select component
export const ModernSelect = ({ label, value, onChange, options, placeholder }: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void, 
  options: { id: string, name: string }[], 
  placeholder: string 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-semibold text-white block">{label}</label>
      <div className="relative">
        <motion.button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full p-4 rounded-xl border border-white/30 text-left text-white focus:outline-none focus:border-green-400 transition-all duration-300 flex items-center justify-between"
          style={{
            background: 'rgba(255, 255, 255, 0.25)',
            backdropFilter: 'blur(8px)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
          }}
          whileTap={{ scale: 0.98 }}
          whileHover={{
            background: 'rgba(255, 255, 255, 0.35)',
            borderColor: 'rgba(255, 255, 255, 0.5)',
          }}
        >
          <span className={value ? 'text-white' : 'text-gray-400'}>
            {value ? options.find(opt => opt.id === value)?.name : placeholder}
          </span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRight className="w-5 h-5 rotate-90" />
          </motion.div>
        </motion.button>
        
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 rounded-xl overflow-hidden z-50"
              style={{
                background: 'rgba(255, 255, 255, 0.3)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.4)',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.15)',
              }}
            >
              {options.map((option) => (
                <motion.button
                  key={option.id}
                  type="button"
                  onClick={() => {
                    onChange(option.id);
                    setIsOpen(false);
                  }}
                  className="w-full p-4 text-left text-white transition-all duration-200 border-b last:border-b-0 font-medium"
                  style={{
                    borderColor: 'rgba(255, 255, 255, 0.15)',
                  }}
                  whileHover={{ 
                    backgroundColor: 'rgba(255,255,255,0.25)',
                    x: 4,
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-white/95 hover:text-white transition-colors">
                    {option.name}
                  </span>
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Modern number input component with steppers
export const NumberInput = ({ label, value, onChange, min = 0, suffix }: { 
  label: string, 
  value: number, 
  onChange: (value: number) => void,
  min?: number,
  suffix?: string
}) => (
  <div className="space-y-2">
    <label className="text-sm font-semibold text-white block">{label}</label>
    <div className="flex items-center bg-white/10 rounded-xl border border-white/20 overflow-hidden">
      <motion.button 
        whileHover={{ backgroundColor: 'rgba(255,255,255,0.1)' }}
        whileTap={{ scale: 0.95 }}
        onClick={() => onChange(Math.max(min, value - 1))} 
        className="p-4 text-white hover:bg-white/10 transition-colors"
        type="button"
      >
        <span className="text-lg font-bold">−</span>
      </motion.button>
      <div className="flex-1 text-center relative">
        <input 
          type="number" 
          readOnly 
          value={value} 
          className="w-full bg-transparent text-center text-white text-lg font-semibold focus:outline-none py-2" 
        />
        {suffix && (
          <span className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 text-sm">
            {suffix}
          </span>
        )}
      </div>
      <motion.button 
        whileHover={{ backgroundColor: 'rgba(255,255,255,0.1)' }}
        whileTap={{ scale: 0.95 }}
        onClick={() => onChange(value + 1)} 
        className="p-4 text-white hover:bg-white/10 transition-colors"
        type="button"
      >
        <span className="text-lg font-bold">+</span>
      </motion.button>
    </div>
  </div>
);

// Modern text input component
export const ModernTextInput = ({ 
  label, 
  value, 
  onChange, 
  placeholder, 
  type = 'text',
  icon,
  suffix
}: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void, 
  placeholder: string,
  type?: string,
  icon?: React.ReactNode,
  suffix?: string
}) => (
  <div className="space-y-2">
    <label className="text-sm font-semibold text-white block">{label}</label>
    <div className="relative">
      {icon && (
        <div className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400">
          {icon}
        </div>
      )}
      <input 
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={`w-full p-4 rounded-xl border border-white/30 text-white text-lg placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300 ${
          icon ? 'pl-12' : ''
        } ${suffix ? 'pr-16' : ''}`}
        style={{
          background: 'rgba(255, 255, 255, 0.25)',
          backdropFilter: 'blur(8px)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
        }}
      />
      {suffix && (
        <span className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 text-sm">
          {suffix}
        </span>
      )}
    </div>
  </div>
);

// Modern option button component
export const ModernOptionButton = ({ 
  children, 
  isSelected, 
  onClick, 
  icon,
  description 
}: { 
  children: React.ReactNode, 
  isSelected: boolean, 
  onClick: () => void,
  icon?: React.ReactNode,
  description?: string
}) => (
  <motion.button
    type="button"
    onClick={onClick}
    whileHover={{ scale: 1.02, y: -2 }}
    whileTap={{ scale: 0.98 }}
    className={`group relative p-6 rounded-2xl cursor-pointer transition-all duration-300 text-left ${
      isSelected 
        ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
        : 'bg-white/5 border-2 border-white/10 hover:border-white/30 hover:bg-white/10'
    }`}
  >
    {/* Selection Indicator */}
    {isSelected && (
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className="absolute top-4 right-4 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
      >
        <div className="w-3 h-3 bg-white rounded-full" />
      </motion.div>
    )}
    
    <div className="flex items-start gap-4">
      {icon && (
        <div className={`p-3 rounded-xl ${
          isSelected ? 'bg-green-400/20 text-green-300' : 'bg-white/10 text-gray-300 group-hover:text-white'
        } transition-colors duration-300`}>
          {icon}
        </div>
      )}
      <div className="flex-1">
        <h3 className="font-bold text-white text-lg mb-1">{children}</h3>
        {description && (
          <p className="text-gray-300 text-sm leading-relaxed">{description}</p>
        )}
      </div>
    </div>
  </motion.button>
);

// Modern date input component
export const ModernDateInput = ({ 
  label, 
  value, 
  onChange, 
  min 
}: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void,
  min?: string
}) => (
  <div className="space-y-2">
    <label className="text-sm font-semibold text-white block">{label}</label>
    <div className="relative">
      <input 
        type="date" 
        className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white text-lg focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300 [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert [&::-webkit-calendar-picker-indicator]:brightness-0 [&::-webkit-calendar-picker-indicator]:contrast-100"
        min={min}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  </div>
);

// Modern textarea component
export const ModernTextArea = ({ 
  label, 
  value, 
  onChange, 
  placeholder, 
  rows = 4 
}: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void, 
  placeholder: string,
  rows?: number
}) => (
  <div className="space-y-2">
    <label className="text-sm font-semibold text-white block">{label}</label>
    <textarea 
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      rows={rows}
      className="w-full p-4 rounded-xl border border-white/30 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300 resize-none"
      style={{
        background: 'rgba(255, 255, 255, 0.25)',
        backdropFilter: 'blur(8px)',
        boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
      }}
    />
  </div>
); 
