import React from 'react';
import type { LucideIcon } from 'lucide-react';

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
}

export function FeatureCard({ icon: Icon, title, description }: FeatureCardProps) {
  return (
    <div className="group p-6 bg-white rounded-xl shadow-sm transition-all hover:shadow-md">
      <div className="inline-flex items-center justify-center rounded-lg p-3 bg-brand-50 group-hover:bg-brand-100 transition-colors">
        <Icon className="w-6 h-6 text-brand-600 transition-colors group-hover:text-brand-700" />
      </div>
      <h3 className="mt-4 text-lg font-semibold text-[#212121]">{title}</h3>
      <p className="mt-2 text-[#757575]">{description}</p>
    </div>
  );
}
