import React from 'react';
import { Input } from '../ui/Input';
import { PhoneInput } from '../ui/PhoneInput';
import type { ContactInfoType } from '../types';

interface ContactInfoProps {
  contact: ContactInfoType;
  onChange: (contact: ContactInfoType) => void;
}

export function ContactInfo({ contact, onChange }: ContactInfoProps) {
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-medium mb-6">Contact Information</h2>
      
      <div className="space-y-6">
        <Input
          label="Full Name"
          value={contact.fullName}
          onChange={(value) => onChange({ ...contact, fullName: value })}
          placeholder="Enter your full name"
          required
        />

        <Input
          label="Email Address"
          type="email"
          value={contact.email}
          onChange={(value) => onChange({ ...contact, email: value })}
          placeholder="<EMAIL>"
          required
        />

        <PhoneInput
          label="Phone Number"
          value={contact.phone}
          onChange={(value) => onChange({ ...contact, phone: value })}
          placeholder="(*************"
          required
        />
      </div>
    </div>
  );
}
