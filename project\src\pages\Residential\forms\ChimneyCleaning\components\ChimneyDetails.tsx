import React from 'react';
import { Flame, Clock, AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';

interface ChimneyDetailsProps {
  details: {
    chimneyType: string;
    lastCleaned: string;
    serviceType: string;
    issues: string[];
    stories: number;
  };
  onChange: (details: any) => void;
}

export function ChimneyDetails({ details, onChange }: ChimneyDetailsProps) {
  const chimneyTypes = [
    'Wood-burning',
    'Gas',
    'Pellet',
    'Electric',
    'Masonry',
    'Prefabricated',
    'Insert',
    'Other'
  ];

  const lastCleanedOptions = [
    'Within last year',
    '1-2 years ago',
    '3-5 years ago',
    '5+ years ago',
    'Never cleaned',
    'Unknown'
  ];

  const serviceTypes = [
    { value: 'one-time', label: 'One-time Cleaning' },
    { value: 'subscription', label: 'Subscription/Maintenance Plan' }
  ];

  const chimneyIssues = [
    'Smoke Problems',
    'Odors',
    'Drafts',
    'Blockages',
    'Animal Intrusion',
    'Creosote Buildup',
    'Water Leaks',
    'Damaged Flue'
  ];

  const storyOptions = [
    { value: 1, label: 'Single-story' },
    { value: 2, label: 'Two-story' },
    { value: 3, label: 'Three-story' },
    { value: 4, label: 'Four or more stories' }
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Flame className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Chimney Details</h3>
          <p className="text-gray-600">Tell us about your chimney</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What type of chimney do you have? <span className="text-red-500">*</span>
            </label>
            <select
              value={details.chimneyType}
              onChange={(e) => onChange({ ...details, chimneyType: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select chimney type</option>
              {chimneyTypes.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              When was your chimney last cleaned? <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={details.lastCleaned}
                onChange={(e) => onChange({ ...details, lastCleaned: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              >
                <option value="">Select option</option>
                {lastCleanedOptions.map((option) => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Do you need a one-time cleaning or would you prefer a subscription/maintenance plan? <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {serviceTypes.map((type) => (
              <label
                key={type.value}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  details.serviceType === type.value
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="serviceType"
                  value={type.value}
                  checked={details.serviceType === type.value}
                  onChange={(e) => onChange({ ...details, serviceType: e.target.value })}
                  className="sr-only"
                />
                <Flame className={`w-5 h-5 mr-3 ${
                  details.serviceType === type.value ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{type.label}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Are there any known blockages or issues? <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {chimneyIssues.map((issue) => (
              <label
                key={issue}
                className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  details.issues.includes(issue)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.issues.includes(issue)}
                  onChange={(e) => {
                    const newIssues = e.target.checked
                      ? [...details.issues, issue]
                      : details.issues.filter(i => i !== issue);
                    onChange({ ...details, issues: newIssues });
                  }}
                  className="sr-only"
                />
                <AlertTriangle className={`w-5 h-5 mr-2 ${
                  details.issues.includes(issue) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm">{issue}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Is this in a single-story or multi-story property? <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {storyOptions.map((option) => (
              <label
                key={option.value}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  details.stories === option.value
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="stories"
                  value={option.value}
                  checked={details.stories === option.value}
                  onChange={(e) => onChange({ ...details, stories: Number(e.target.value) })}
                  className="sr-only"
                />
                <Flame className={`w-5 h-5 mr-3 ${
                  details.stories === option.value ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </div>

        {details.issues.includes('Blockages') && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Blockage Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Blockages may require a camera inspection before cleaning. Our technician will assess the situation during the appointment.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
