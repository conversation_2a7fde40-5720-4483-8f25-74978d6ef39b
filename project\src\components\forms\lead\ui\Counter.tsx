import React from 'react';
import { Plus, Minus } from 'lucide-react';

interface CounterProps {
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
}

export function Counter({ value, onChange, min, max }: CounterProps) {
  const decrement = () => onChange(Math.max(min, value - 1));
  const increment = () => onChange(Math.min(max, value + 1));

  return (
    <div className="flex items-center space-x-4">
      <button
        onClick={decrement}
        disabled={value <= min}
        className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
        aria-label="Decrease"
      >
        <Minus className="w-4 h-4" />
      </button>
      
      <span className="text-xl font-medium w-12 text-center">{value}</span>
      
      <button
        onClick={increment}
        disabled={value >= max}
        className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50"
        aria-label="Increase"
      >
        <Plus className="w-4 h-4" />
      </button>
    </div>
  );
}
