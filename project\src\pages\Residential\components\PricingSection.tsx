import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { DollarSign, ArrowRight } from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { PricingCalculator } from '../../../components/payment/PricingCalculator';
import { PaymentOptionsModal } from '../../../components/PaymentOptionsModal';

export function PricingSection() {
  const navigate = useNavigate();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [estimatedPrice, setEstimatedPrice] = useState(0);

  const handleGetQuote = (price: number) => {
    setEstimatedPrice(price);
    setShowPaymentModal(true);
  };

  const handleBookNow = () => {
    navigate('/residential/regular');
  };

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-6"
          >
            <DollarSign className="w-6 h-6 text-brand-600" />
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-3xl font-bold text-gray-900 mb-4"
          >
            Transparent Pricing
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Get an instant estimate for your home cleaning needs
          </motion.p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Pricing Calculator */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
          >
            <PricingCalculator 
              initialData={{
                serviceType: 'regular',
                squareFootage: 1500,
                bedrooms: 3,
                bathrooms: 2,
                frequency: 'one-time'
              }}
              onGetQuote={handleGetQuote}
            />
          </motion.div>

          {/* Pricing Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
            className="space-y-8"
          >
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                What's Included
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-brand-100 flex items-center justify-center mt-0.5">
                    <span className="text-brand-600 font-bold text-sm">1</span>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-base font-medium text-gray-900">Professional Cleaning Team</h4>
                    <p className="mt-1 text-sm text-gray-600">Trained, background-checked, and insured cleaning professionals</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-brand-100 flex items-center justify-center mt-0.5">
                    <span className="text-brand-600 font-bold text-sm">2</span>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-base font-medium text-gray-900">All Cleaning Supplies</h4>
                    <p className="mt-1 text-sm text-gray-600">We bring all necessary equipment and eco-friendly cleaning products</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-6 w-6 rounded-full bg-brand-100 flex items-center justify-center mt-0.5">
                    <span className="text-brand-600 font-bold text-sm">3</span>
                  </div>
                  <div className="ml-3">
                    <h4 className="text-base font-medium text-gray-900">100% Satisfaction Guarantee</h4>
                    <p className="mt-1 text-sm text-gray-600">If you're not completely satisfied, we'll re-clean at no additional cost</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8">
                <Button 
                  onClick={handleBookNow}
                  className="w-full"
                >
                  Book Now
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </div>
            </div>
            
            <div className="bg-brand-50 rounded-xl p-6 border border-brand-100">
              <h4 className="font-medium text-brand-800 mb-2">Flexible Payment Options</h4>
              <p className="text-brand-700 text-sm">
                We accept all major credit cards, debit cards, and digital wallets through our secure Square payment system.
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={estimatedPrice}
        description="Residential Cleaning Service"
        customerEmail=""
        formData={{}}
        user={null}
        onPaymentComplete={() => setShowPaymentModal(false)}
      />
    </section>
  );
}
