import React from 'react';
import { AlertCircle, Upload, Droplets, Brush, Zap } from 'lucide-react';
import { motion } from 'framer-motion';

interface StainTreatmentProps {
  treatment: {
    hasStains: boolean;
    stainTypes: string[];
    stainLocations: string[];
    stainDescription: string;
    previousTreatments: string[];
  };
  onChange: (treatment: any) => void;
}

export function StainTreatment({ treatment, onChange }: StainTreatmentProps) {
  const stainTypes = [
    { id: 'food', label: 'Food/Beverage', icon: Droplets },
    { id: 'pet', label: 'Pet Stains', icon: Droplets },
    { id: 'dirt', label: 'Dirt/Mud', icon: Brush },
    { id: 'oil', label: 'Oil/Grease', icon: Droplets },
    { id: 'ink', label: 'Ink/Marker', icon: Droplets },
    { id: 'rust', label: 'Rust', icon: Brush },
    { id: 'unknown', label: 'Unknown', icon: AlertCircle },
    { id: 'other', label: 'Other', icon: AlertCircle }
  ];

  const stainLocations = [
    'Living Room',
    'Bedrooms',
    'Dining Room',
    'Hallways',
    'Stairs',
    'High Traffic Areas'
  ];

  const previousTreatments = [
    { id: 'none', label: 'No Previous Treatment', icon: AlertCircle },
    { id: 'diy', label: 'DIY Spot Treatment', icon: Brush },
    { id: 'store', label: 'Store-Bought Cleaner', icon: Droplets },
    { id: 'professional', label: 'Professional Treatment', icon: Zap }
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <AlertCircle className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Stain Treatment</h3>
          <p className="text-gray-600">Tell us about any stains that need special attention</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        {/* Does carpet have stains */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-4"
        >
          <label className="block text-sm font-medium text-gray-700">
            Does your carpet have stains that need treatment?
          </label>
          <div className="grid grid-cols-2 gap-4">
            <label
              className={`flex flex-col items-center justify-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                treatment.hasStains
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="hasStains"
                value="yes"
                checked={treatment.hasStains === true}
                onChange={() => onChange({ ...treatment, hasStains: true })}
                className="sr-only"
              />
              <Droplets className={`w-6 h-6 mb-2 ${treatment.hasStains ? 'text-brand-600' : 'text-gray-400'}`} />
              <span className="text-gray-700 font-medium">Yes</span>
              <span className="text-xs text-gray-500 mt-1">I have stains to treat</span>
            </label>
            <label
              className={`flex flex-col items-center justify-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                treatment.hasStains === false && treatment.hasStains !== undefined
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="hasStains"
                value="no"
                checked={treatment.hasStains === false}
                onChange={() => onChange({ ...treatment, hasStains: false })}
                className="sr-only"
              />
              <Brush className={`w-6 h-6 mb-2 ${treatment.hasStains === false ? 'text-brand-600' : 'text-gray-400'}`} />
              <span className="text-gray-700 font-medium">No</span>
              <span className="text-xs text-gray-500 mt-1">Just regular cleaning</span>
            </label>
          </div>
        </motion.div>

        {/* Show stain details only if hasStains is true */}
        {treatment.hasStains && (
          <>
            {/* Stain Types */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-4"
            >
              <label className="block text-sm font-medium text-gray-700">
                Stain Types <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {stainTypes.map((stain, index) => {
                  const Icon = stain.icon;
                  return (
                    <motion.label
                      key={stain.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 + (index * 0.05) }}
                      className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                        treatment.stainTypes.includes(stain.id)
                          ? 'border-brand-500 bg-brand-50'
                          : 'border-gray-200 hover:border-brand-300'
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={treatment.stainTypes.includes(stain.id)}
                        onChange={(e) => {
                          const newTypes = e.target.checked
                            ? [...treatment.stainTypes, stain.id]
                            : treatment.stainTypes.filter(t => t !== stain.id);
                          onChange({ ...treatment, stainTypes: newTypes });
                        }}
                        className="sr-only"
                      />
                      <Icon className={`w-5 h-5 mr-3 ${
                        treatment.stainTypes.includes(stain.id) ? 'text-brand-600' : 'text-gray-400'
                      }`} />
                      <span className="text-gray-700">{stain.label}</span>
                    </motion.label>
                  );
                })}
              </div>
            </motion.div>

            {/* Stain Locations */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="space-y-4"
            >
              <label className="block text-sm font-medium text-gray-700">
                Stain Locations <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {stainLocations.map((location, index) => (
                  <motion.label
                    key={location}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 + (index * 0.05) }}
                    className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                      treatment.stainLocations.includes(location)
                        ? 'border-brand-500 bg-brand-50'
                        : 'border-gray-200 hover:border-brand-300'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={treatment.stainLocations.includes(location)}
                      onChange={(e) => {
                        const newLocations = e.target.checked
                          ? [...treatment.stainLocations, location]
                          : treatment.stainLocations.filter(l => l !== location);
                        onChange({ ...treatment, stainLocations: newLocations });
                      }}
                      className="sr-only"
                    />
                    <AlertCircle className={`w-5 h-5 mr-3 ${
                      treatment.stainLocations.includes(location) ? 'text-brand-600' : 'text-gray-400'
                    }`} />
                    <span className="text-gray-700">{location}</span>
                  </motion.label>
                ))}
              </div>
            </motion.div>

            {/* Stain Description */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="space-y-2"
            >
              <label className="block text-sm font-medium text-gray-700">
                Stain Description
              </label>
              <textarea
                value={treatment.stainDescription}
                onChange={(e) => onChange({ ...treatment, stainDescription: e.target.value })}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                placeholder="Please describe the stains in detail (age, size, what caused them, etc.)"
              />
            </motion.div>

            {/* Previous Treatments */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="space-y-4"
            >
              <label className="block text-sm font-medium text-gray-700">
                Previous Treatments
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {previousTreatments.map((treatment, index) => {
                  const Icon = treatment.icon;
                  return (
                    <motion.label
                      key={treatment.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.8 + (index * 0.05) }}
                      className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                        treatment.previousTreatments.includes(treatment.id)
                          ? 'border-brand-500 bg-brand-50'
                          : 'border-gray-200 hover:border-brand-300'
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={treatment.previousTreatments.includes(treatment.id)}
                        onChange={(e) => {
                          const newTreatments = e.target.checked
                            ? [...treatment.previousTreatments, treatment.id]
                            : treatment.previousTreatments.filter(t => t !== treatment.id);
                          onChange({ ...treatment, previousTreatments: newTreatments });
                        }}
                        className="sr-only"
                      />
                      <Icon className={`w-5 h-5 mr-3 ${
                        treatment.previousTreatments.includes(treatment.id) ? 'text-brand-600' : 'text-gray-400'
                      }`} />
                      <span className="text-gray-700">{treatment.label}</span>
                    </motion.label>
                  );
                })}
              </div>
            </motion.div>
          </>
        )}
      </div>
    </div>
  );
}
