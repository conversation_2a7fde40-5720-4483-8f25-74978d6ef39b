import React from 'react';
import { Building2, GraduationCap, Stethoscope, ShoppingBag, Warehouse } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'commercial',
    icon: Building2,
    label: 'Commercial Office',
    description: 'Office buildings & business centers'
  },
  {
    id: 'medical',
    icon: Stethoscope,
    label: 'Medical Facility',
    description: 'Hospitals & medical offices'
  },
  {
    id: 'education',
    icon: GraduationCap,
    label: 'Educational',
    description: 'Schools & universities'
  },
  {
    id: 'retail',
    icon: ShoppingBag,
    label: 'Retail Space',
    description: 'Stores & shopping centers'
  },
  {
    id: 'industrial',
    icon: Warehouse,
    label: 'Industrial',
    description: 'Warehouses & factories'
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h3 className="text-2xl font-semibold text-gray-900">
          Select Your Industry
        </h3>
        <p className="text-gray-600 mt-2">
          Choose your facility type for specialized cleaning solutions
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`group relative w-full p-8 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex items-start space-x-6">
                <div className={`p-4 rounded-lg transition-colors ${
                  isSelected ? 'bg-brand-100' : 'bg-gray-50 group-hover:bg-brand-50'
                }`}>
                  <Icon className={`w-8 h-8 ${
                    isSelected ? 'text-brand-600' : 'text-gray-600'
                  }`} />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{service.label}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
