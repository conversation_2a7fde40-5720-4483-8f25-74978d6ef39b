import React from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, Scale, FileText, AlertCircle, Clock, Users, 
  Phone, Mail, Globe, MessageSquare, Lock, Building2
} from 'lucide-react';
import { Header } from '../components/layout/Header';
import { Footer } from '../components/layout/Footer';

const sections = [
  {
    icon: Shield,
    title: 'Scope of Services',
    content: [
      {
        heading: '1. Scope of Services',
        text: 'Empire Pro operates as a communications and service facilitation agency for commercial cleaning. Outside of New York, we organize, source, and coordinate cleaning services through our network of partners and subcontractors. Insurance, referrals, and documentation for services outside of New York are provided by the respective partners, and Empire Pro does not carry insurance for these services. In New York, Empire Pro may use its own cleaners and subcontractors to deliver services directly. All services are performed to meet the highest industry standards, utilizing eco-friendly products and advanced cleaning techniques.'
      }
    ]
  },
  {
    icon: MessageSquare,
    title: 'SMS Messaging Terms',
    content: [
      {
        heading: '2. SMS Messaging Terms',
        text: 'By opting in to receive text messages, you agree to receive text messages related to appointment reminders, service updates, account notifications, and other service-related communications. Messaging frequency may vary depending on your interaction with our services. Message and data rates may apply based on your mobile carrier plan. To opt out of SMS communications, you can reply "STOP" at any time. For assistance, reply "HELP" or visit our website at https://empireprocleaning.com.'
      }
    ]
  },
  {
    icon: Clock,
    title: 'Booking and Scheduling',
    content: [
      {
        heading: '3. Booking and Scheduling',
        text: 'Clients can book services through our website, by phone, or via authorized representatives. All bookings are subject to confirmation. We strive to accommodate your preferred schedule and will provide a detailed service itinerary upon confirmation. Clients must ensure safe and unobstructed access to the premises during scheduled service times.'
      }
    ]
  },
  {
    icon: Scale,
    title: 'Pricing and Payment Terms',
    content: [
      {
        heading: '4. Pricing and Payment Terms',
        text: 'All pricing will be communicated upfront and confirmed before the service begins. Additional charges for extra services will only be applied after prior approval. Payments are due upon receipt of the invoice unless otherwise arranged. Accepted payment methods include ACH transfers and approved corporate billing arrangements. Late payments may incur a 10% monthly late fee and may result in service suspension.'
      }
    ]
  },
  {
    icon: AlertCircle,
    title: 'Cancellation and Refund Policy',
    content: [
      {
        heading: '5. Cancellation and Refund Policy',
        text: 'Cancellations made within 24 hours of the scheduled service time are non-refundable due to resource allocation. Services can be rescheduled without penalty if at least 24 hours\' notice is provided. If you are unsatisfied with our service, notify us within 24 hours to receive a free reclean of the affected areas. Unauthorized chargebacks are prohibited and may result in additional fees and legal action.'
      }
    ]
  },
  {
    icon: Users,
    title: 'Client Responsibilities',
    content: [
      {
        heading: '6. Client Responsibilities',
        text: 'Clients are responsible for: Providing safe access to the property, securing fragile or valuable items, notifying us of any special care instructions or restrictions, and ensuring the premises comply with applicable workplace safety and environmental regulations.'
      }
    ]
  },
  {
    icon: Lock,
    title: 'Data Protection',
    content: [
      {
        heading: '7. Data Protection and Confidentiality',
        text: 'We respect your privacy and are committed to protecting your information. All personal data is handled in compliance with applicable data protection laws and is used solely for service provision. Your SMS opt-in data and consent will never be shared with third parties.'
      }
    ]
  },
  {
    icon: Shield,
    title: 'Liability and Insurance',
    content: [
      {
        heading: '8. Liability and Insurance',
        text: 'Empire Pro carries comprehensive liability and workers\' compensation insurance for services performed in New York. For services provided outside of New York through our partners, all liability and insurance coverage remain under the partner\'s responsibility. We are not responsible for pre-existing damages or loss of unsecured items. Any damages must be reported within 24 hours of service completion to be eligible for remediation.'
      }
    ]
  },
  {
    icon: FileText,
    title: 'Contact Information',
    content: [
      {
        heading: '13. Contact Information',
        text: 'For any questions regarding these Terms of Service, SMS communications, or other inquiries, please contact us:'
      }
    ]
  }
];

export function Terms() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="pt-40 pb-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-20"
          >
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-6">
                Terms of Service
              </h1>
              <p className="text-xl text-gray-600">
                Please read these terms carefully before using our services
              </p>
              <div className="mt-8 text-sm text-gray-500">
                Effective Date: January 1st, 2022
              </div>
            </div>
          </motion.div>

          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl shadow-lg p-8 mb-8"
          >
            <p className="text-gray-600 leading-relaxed">
              Welcome to Empire Pro Commercial Cleaning ("Empire Pro," "we," "us," or "our"). By accessing or using our services, you ("Client," "you," or "your") agree to be bound by these Terms of Service ("Terms"). Please read them carefully to understand your rights and obligations.
            </p>
          </motion.div>

          {/* Main Content */}
          <div className="space-y-8">
            {sections.map((section, index) => {
              const Icon = section.icon;
              return (
                <motion.div
                  key={section.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + (index * 0.1) }}
                  className="bg-white rounded-2xl shadow-lg p-8"
                >
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="p-3 rounded-xl bg-brand-100">
                      <Icon className="w-6 h-6 text-brand-600" />
                    </div>
                    <h2 className="text-2xl font-semibold text-gray-900">
                      {section.title}
                    </h2>
                  </div>
                  <div className="space-y-6">
                    {section.content.map((item) => (
                      <div key={item.heading}>
                        <h3 className="text-lg font-medium text-gray-900 mb-3">
                          {item.heading}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">
                          {item.text}
                        </p>
                        {item.heading === '13. Contact Information' && (
                          <div className="mt-4 space-y-3">
                            <div className="flex items-center space-x-2 text-gray-600">
                              <Phone className="w-5 h-5 text-brand-600" />
                              <span>(*************</span>
                            </div>
                            <div className="flex items-center space-x-2 text-gray-600">
                              <Mail className="w-5 h-5 text-brand-600" />
                              <span><EMAIL></span>
                            </div>
                            <div className="flex items-center space-x-2 text-gray-600">
                              <Globe className="w-5 h-5 text-brand-600" />
                              <span>https://empireprocleaning.com</span>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Agreement Notice */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-brand-50 rounded-2xl p-8 mt-8"
          >
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Agreement to Terms
            </h2>
            <p className="text-gray-600 mb-4">
              By engaging our services, you acknowledge that you have read, understood, and agree to these Terms of Service.
            </p>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <Phone className="w-4 h-4 text-brand-600" />
              <span>(*************</span>
              <Mail className="w-4 h-4 text-brand-600" />
              <span><EMAIL></span>
            </div>
          </motion.div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
