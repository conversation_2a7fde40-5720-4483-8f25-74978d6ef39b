/**
 * Comprehensive Payment Modal and UI Component Tests
 * 
 * This test suite provides complete coverage of payment UI components including:
 * - Payment modal interactions and state management
 * - User interface responsiveness and accessibility
 * - Form validation and error handling
 * - Loading states and transitions
 * - Cross-device compatibility
 * - User experience flows
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { TestDataFactory, MockFactory, TestUtils, TestAssertions } from '../utils/testHelpers';
import { PaymentOptionsModal } from '../../components/PaymentOptionsModal';
import { PaymentModal } from '../../components/payment/PaymentModal';
import { PaymentSuccessPopup } from '../../components/PaymentSuccessPopup';
import { supabase } from '../../lib/supabase/client';

// Mock dependencies
vi.mock('../../lib/supabase/client', () => ({
  supabase: MockFactory.createSupabaseMock()
}));

vi.mock('../../lib/square/config', () => ({
  getSquareConfig: vi.fn().mockReturnValue({
    applicationId: 'test-app-id',
    accessToken: 'test-token',
    locationId: 'test-location',
    environment: 'sandbox'
  }),
  isSquareConfigured: vi.fn().mockReturnValue(true)
}));

// Mock window.open for payment redirects
Object.defineProperty(window, 'open', {
  value: vi.fn(),
  writable: true
});

describe('Comprehensive Payment Modal and UI Component Tests', () => {
  let mockUser: any;
  let mockFormData: any;
  let user: any;
  let mockOnClose: any;
  let mockOnPaymentComplete: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUser = TestDataFactory.createMockUser();
    mockFormData = TestDataFactory.createMockResidentialFormData();
    user = userEvent.setup();
    mockOnClose = vi.fn();
    mockOnPaymentComplete = vi.fn();
    
    // Setup successful payment response
    vi.mocked(supabase.functions.invoke).mockResolvedValue({
      data: TestDataFactory.createMockPaymentResponse(),
      error: null
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('PaymentOptionsModal Component Tests', () => {
    it('should render payment options correctly', () => {
      // Arrange & Act
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Assert
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText(/payment.*options/i)).toBeInTheDocument();
      expect(screen.getByText(/square/i)).toBeInTheDocument();
      expect(screen.getByText(/\$150\.00/)).toBeInTheDocument();
    });

    it('should handle payment method selection', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);
      
      // Assert
      expect(squareOption.closest('button')).toHaveClass(/selected|active/);
    });

    it('should show loading state during payment processing', async () => {
      // Arrange
      vi.mocked(supabase.functions.invoke).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          data: TestDataFactory.createMockPaymentResponse(),
          error: null
        }), 1000))
      );
      
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);
      
      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);
      
      // Assert
      expect(screen.getByText(/processing/i)).toBeInTheDocument();
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });

    it('should display error messages appropriately', async () => {
      // Arrange
      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: null,
        error: { message: 'Payment service unavailable' }
      });
      
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);
      
      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);
      
      // Assert
      await waitFor(() => {
        expect(screen.getByText(/payment service unavailable/i)).toBeInTheDocument();
        expect(screen.getByRole('alert')).toBeInTheDocument();
      });
    });

    it('should handle modal close functionality', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act - Close via X button
      const closeButton = screen.getByRole('button', { name: /close/i });
      await user.click(closeButton);
      
      // Assert
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should handle escape key to close modal', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act
      await user.keyboard('{Escape}');
      
      // Assert
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should prevent closing during payment processing', async () => {
      // Arrange
      vi.mocked(supabase.functions.invoke).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({
          data: TestDataFactory.createMockPaymentResponse(),
          error: null
        }), 2000))
      );
      
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act - Start payment processing
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);
      
      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);
      
      // Try to close during processing
      await user.keyboard('{Escape}');
      
      // Assert - Should not close during processing
      expect(mockOnClose).not.toHaveBeenCalled();
      expect(screen.getByText(/processing/i)).toBeInTheDocument();
    });
  });

  describe('PaymentModal Component Tests', () => {
    it('should render payment modal with correct information', () => {
      // Arrange & Act
      render(
        <BrowserRouter>
          <PaymentModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            description="House Cleaning Service"
            customerEmail="<EMAIL>"
            formData={mockFormData}
            user={mockUser}
            onPaymentComplete={mockOnPaymentComplete}
          />
        </BrowserRouter>
      );
      
      // Assert
      expect(screen.getByText(/house cleaning service/i)).toBeInTheDocument();
      expect(screen.getByText(/\$150\.00/)).toBeInTheDocument();
      expect(screen.getByText(/test@example\.com/)).toBeInTheDocument();
    });

    it('should handle successful payment completion', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            description="House Cleaning Service"
            customerEmail="<EMAIL>"
            formData={mockFormData}
            user={mockUser}
            onPaymentComplete={mockOnPaymentComplete}
          />
        </BrowserRouter>
      );
      
      // Act
      const payButton = screen.getByText(/pay.*now/i);
      await user.click(payButton);
      
      // Assert
      await waitFor(() => {
        expect(mockOnPaymentComplete).toHaveBeenCalledTimes(1);
      });
    });

    it('should open payment link in new window', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            description="House Cleaning Service"
            customerEmail="<EMAIL>"
            formData={mockFormData}
            user={mockUser}
            onPaymentComplete={mockOnPaymentComplete}
          />
        </BrowserRouter>
      );
      
      // Act
      const payButton = screen.getByText(/pay.*now/i);
      await user.click(payButton);
      
      // Assert
      await waitFor(() => {
        expect(window.open).toHaveBeenCalledWith(
          expect.stringMatching(/^https:\/\/square\.link\/payment\//),
          '_blank'
        );
      });
    });
  });

  describe('PaymentSuccessPopup Component Tests', () => {
    it('should render success popup with correct information', () => {
      // Arrange & Act
      render(
        <PaymentSuccessPopup
          isVisible={true}
          onClose={mockOnClose}
          bookingDetails={{
            id: 'booking-123',
            serviceType: 'Residential Cleaning',
            amount: 150,
            date: '2025-12-25',
            time: '10:00 AM'
          }}
        />
      );
      
      // Assert
      expect(screen.getByText(/payment.*successful/i)).toBeInTheDocument();
      expect(screen.getByText(/booking-123/i)).toBeInTheDocument();
      expect(screen.getByText(/residential cleaning/i)).toBeInTheDocument();
      expect(screen.getByText(/\$150/)).toBeInTheDocument();
    });

    it('should auto-dismiss after specified time', async () => {
      // Arrange
      vi.useFakeTimers();
      
      render(
        <PaymentSuccessPopup
          isVisible={true}
          onClose={mockOnClose}
          bookingDetails={{
            id: 'booking-123',
            serviceType: 'Residential Cleaning',
            amount: 150,
            date: '2025-12-25',
            time: '10:00 AM'
          }}
          autoDismissTime={3000}
        />
      );
      
      // Act
      vi.advanceTimersByTime(3000);
      
      // Assert
      await waitFor(() => {
        expect(mockOnClose).toHaveBeenCalledTimes(1);
      });
      
      vi.useRealTimers();
    });

    it('should handle manual close', async () => {
      // Arrange
      render(
        <PaymentSuccessPopup
          isVisible={true}
          onClose={mockOnClose}
          bookingDetails={{
            id: 'booking-123',
            serviceType: 'Residential Cleaning',
            amount: 150,
            date: '2025-12-25',
            time: '10:00 AM'
          }}
        />
      );
      
      // Act
      const closeButton = screen.getByRole('button', { name: /close/i });
      await user.click(closeButton);
      
      // Assert
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Responsive Design Tests', () => {
    it('should adapt to mobile viewport', () => {
      // Arrange - Simulate mobile viewport
      Object.defineProperty(window, 'innerWidth', { value: 375 });
      Object.defineProperty(window, 'innerHeight', { value: 667 });
      
      // Act
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Assert
      const modal = screen.getByRole('dialog');
      expect(modal).toHaveClass(/mobile|responsive/);
    });

    it('should handle tablet viewport', () => {
      // Arrange - Simulate tablet viewport
      Object.defineProperty(window, 'innerWidth', { value: 768 });
      Object.defineProperty(window, 'innerHeight', { value: 1024 });
      
      // Act
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Assert
      const modal = screen.getByRole('dialog');
      expect(modal).toBeVisible();
    });
  });

  describe('Accessibility Tests', () => {
    it('should have proper ARIA attributes', () => {
      // Arrange & Act
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Assert
      expect(screen.getByRole('dialog')).toHaveAttribute('aria-modal', 'true');
      expect(screen.getByRole('dialog')).toHaveAttribute('aria-labelledby');
      expect(screen.getByLabelText(/payment.*method/i)).toBeInTheDocument();
    });

    it('should support keyboard navigation', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act - Navigate with keyboard
      await user.tab(); // Focus first interactive element
      await user.keyboard('{Enter}'); // Activate element
      
      await user.tab(); // Move to next element
      await user.keyboard(' '); // Activate with space
      
      // Assert - Should handle keyboard interactions
      expect(document.activeElement).toBeInTheDocument();
    });

    it('should announce status changes to screen readers', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );
      
      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);
      
      // Assert
      expect(screen.getByRole('status')).toBeInTheDocument();
      expect(screen.getByText(/square.*selected/i)).toBeInTheDocument();
    });
  });

  describe('Form Validation and Error Handling', () => {
    it('should validate required fields before payment', async () => {
      // Arrange
      const incompleteFormData = {
        ...mockFormData,
        contact: {
          ...mockFormData.contact,
          email: '',
          phone: ''
        }
      };

      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={incompleteFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/email.*required/i)).toBeInTheDocument();
        expect(screen.getByText(/phone.*required/i)).toBeInTheDocument();
      });
    });

    it('should show field-specific validation errors', async () => {
      // Arrange
      const invalidFormData = {
        ...mockFormData,
        contact: {
          ...mockFormData.contact,
          email: 'invalid-email',
          phone: '123'
        }
      };

      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={invalidFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/invalid.*email.*format/i)).toBeInTheDocument();
        expect(screen.getByText(/invalid.*phone.*format/i)).toBeInTheDocument();
      });
    });

    it('should clear errors when user corrects input', async () => {
      // Arrange
      const { rerender } = render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={{
              ...mockFormData,
              contact: { ...mockFormData.contact, email: 'invalid' }
            }}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act - Trigger validation error
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      await waitFor(() => {
        expect(screen.getByText(/invalid.*email/i)).toBeInTheDocument();
      });

      // Fix the data and re-render
      rerender(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData} // Valid data
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Assert - Error should be cleared
      expect(screen.queryByText(/invalid.*email/i)).not.toBeInTheDocument();
    });
  });

  describe('Loading States and Transitions', () => {
    it('should show appropriate loading indicators', async () => {
      // Arrange
      vi.mocked(supabase.functions.invoke).mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({
          data: TestDataFactory.createMockPaymentResponse(),
          error: null
        }), 2000))
      );

      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      // Assert
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.getByText(/processing.*payment/i)).toBeInTheDocument();
      expect(continueButton).toBeDisabled();
    });

    it('should handle smooth transitions between states', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act - Go through state transitions
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      // Assert - Selection state
      expect(squareOption.closest('button')).toHaveClass(/selected/);

      // Act - Continue to processing
      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      // Assert - Processing state
      await waitFor(() => {
        expect(screen.getByText(/processing/i)).toBeInTheDocument();
      });

      // Assert - Success state
      await waitFor(() => {
        expect(screen.getByText(/redirecting/i)).toBeInTheDocument();
      }, { timeout: 5000 });
    });

    it('should handle timeout scenarios gracefully', async () => {
      // Arrange
      vi.mocked(supabase.functions.invoke).mockImplementation(() =>
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      // Assert
      await waitFor(() => {
        expect(screen.getByText(/request.*timeout/i)).toBeInTheDocument();
        expect(screen.getByText(/try.*again/i)).toBeInTheDocument();
      });
    });
  });

  describe('User Experience and Interaction Tests', () => {
    it('should provide clear visual feedback for user actions', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act - Hover over payment option
      const squareOption = screen.getByText(/square/i);
      fireEvent.mouseEnter(squareOption);

      // Assert - Should show hover state
      expect(squareOption.closest('button')).toHaveClass(/hover/);

      // Act - Click payment option
      await user.click(squareOption);

      // Assert - Should show selected state
      expect(squareOption.closest('button')).toHaveClass(/selected/);
    });

    it('should handle rapid user interactions gracefully', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act - Rapid clicking
      const squareOption = screen.getByText(/square/i);

      for (let i = 0; i < 5; i++) {
        await user.click(squareOption);
      }

      // Assert - Should handle rapid clicks without issues
      expect(squareOption.closest('button')).toHaveClass(/selected/);
    });

    it('should provide helpful tooltips and guidance', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act - Hover over info icon
      const infoIcon = screen.getByRole('button', { name: /info/i });
      fireEvent.mouseEnter(infoIcon);

      // Assert - Should show tooltip
      await waitFor(() => {
        expect(screen.getByRole('tooltip')).toBeInTheDocument();
      });
    });

    it('should handle focus management correctly', async () => {
      // Arrange
      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Assert - Focus should be trapped within modal
      const modal = screen.getByRole('dialog');
      const focusableElements = within(modal).getAllByRole('button');

      expect(focusableElements.length).toBeGreaterThan(0);
      expect(document.activeElement).toBeInTheDocument();
    });
  });

  describe('Error Recovery and Retry Mechanisms', () => {
    it('should provide retry functionality after errors', async () => {
      // Arrange
      let callCount = 0;
      vi.mocked(supabase.functions.invoke).mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          return Promise.resolve({
            data: null,
            error: { message: 'Temporary error' }
          });
        }
        return Promise.resolve({
          data: TestDataFactory.createMockPaymentResponse(),
          error: null
        });
      });

      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act - First attempt (fails)
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      // Assert - Should show error
      await waitFor(() => {
        expect(screen.getByText(/temporary error/i)).toBeInTheDocument();
      });

      // Act - Retry
      const retryButton = screen.getByText(/try.*again/i);
      await user.click(retryButton);

      // Assert - Should succeed on retry
      await waitFor(() => {
        expect(screen.getByText(/processing/i)).toBeInTheDocument();
      });
    });

    it('should limit retry attempts appropriately', async () => {
      // Arrange
      vi.mocked(supabase.functions.invoke).mockResolvedValue({
        data: null,
        error: { message: 'Persistent error' }
      });

      render(
        <BrowserRouter>
          <PaymentOptionsModal
            isOpen={true}
            onClose={mockOnClose}
            amount={150}
            formData={mockFormData}
            user={mockUser}
          />
        </BrowserRouter>
      );

      // Act - Multiple retry attempts
      const squareOption = screen.getByText(/square/i);
      await user.click(squareOption);

      const continueButton = screen.getByText(/continue/i);
      await user.click(continueButton);

      // Retry multiple times
      for (let i = 0; i < 3; i++) {
        await waitFor(() => {
          expect(screen.getByText(/try.*again/i)).toBeInTheDocument();
        });

        const retryButton = screen.getByText(/try.*again/i);
        await user.click(retryButton);
      }

      // Assert - Should eventually show contact support message
      await waitFor(() => {
        expect(screen.getByText(/contact.*support/i)).toBeInTheDocument();
      });
    });
  });
});

