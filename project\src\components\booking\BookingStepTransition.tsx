import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface BookingStepTransitionProps {
  children: React.ReactNode;
  currentStep: number;
  direction?: 'forward' | 'backward';
  className?: string;
}

export function BookingStepTransition({
  children,
  currentStep,
  direction = 'forward',
  className = ''
}: BookingStepTransitionProps) {
  const variants = {
    enter: {
      x: direction === 'forward' ? 300 : -300,
      opacity: 0,
      scale: 0.95
    },
    center: {
      x: 0,
      opacity: 1,
      scale: 1
    },
    exit: {
      x: direction === 'forward' ? -300 : 300,
      opacity: 0,
      scale: 0.95
    }
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <AnimatePresence mode="wait" custom={direction}>
        <motion.div
          key={currentStep}
          custom={direction}
          variants={variants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            duration: 0.4,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
          className="w-full"
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

interface BookingProgressTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  delay?: number;
  className?: string;
}

export function BookingProgressTransition({
  children,
  isVisible,
  delay = 0,
  className = ''
}: BookingProgressTransitionProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{
            duration: 0.5,
            delay,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface PaymentTransitionProps {
  children: React.ReactNode;
  isVisible: boolean;
  className?: string;
}

export function PaymentTransition({
  children,
  isVisible,
  className = ''
}: PaymentTransitionProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ 
            opacity: 0, 
            y: 50, 
            scale: 0.9,
            filter: 'blur(10px)'
          }}
          animate={{ 
            opacity: 1, 
            y: 0, 
            scale: 1,
            filter: 'blur(0px)'
          }}
          exit={{ 
            opacity: 0, 
            y: -50, 
            scale: 0.9,
            filter: 'blur(10px)'
          }}
          transition={{
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94]
          }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
} 
