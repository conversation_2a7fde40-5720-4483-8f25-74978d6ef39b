import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { Mail, Lock, User, AlertCircle, Check, X } from 'lucide-react';
import { useAuth } from '../../lib/auth/AuthProvider';
import { Button } from '../../components/ui/Button';
import { checkSupabaseConnection } from '../../lib/supabase/client';

export function Register() {
  const navigate = useNavigate();
  const location = useLocation();
  const { signUp, pendingFormData } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [existingAccount, setExistingAccount] = useState(false);

  // Pre-fill email and name if coming from form submission
  useEffect(() => {
    if (pendingFormData?.contact) {
      setEmail(pendingFormData.contact.email || '');
      setFullName(pendingFormData.contact.fullName || '');
    }
  }, [pendingFormData]);

  // Clear error when inputs change
  useEffect(() => {
    if (error) setError('');
    if (existingAccount) setExistingAccount(false);
  }, [email, password, fullName]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setError('');
      setLoading(true);
      
      // Check Supabase connection before attempting sign up
      await checkSupabaseConnection();
      
      await signUp(email, password, fullName);
      setSuccess(true);
      
      // If we have pending form data, redirect to thank you page
      if (pendingFormData) {
        navigate('/thank-you', { 
          state: { formData: pendingFormData },
          replace: true 
        });
      } else {
        // Otherwise go to account dashboard
        navigate('/accountdashboard');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create account';
      
      // Check if the error is due to existing account
      if (errorMessage.includes('already registered') || errorMessage.includes('already exists')) {
        setExistingAccount(true);
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (location.state?.from === '/thank-you' && pendingFormData) {
      navigate('/thank-you', { 
        state: { formData: pendingFormData },
        replace: true 
      });
    } else {
      navigate(-1);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10 text-center">
            <div className="mb-4 flex justify-center">
              <div className="rounded-full bg-green-100 p-3">
                <Check className="w-8 h-8 text-green-600" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Account Created!</h2>
            <p className="text-gray-600">
              Your account has been created successfully. You can now proceed with your service request.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md relative">
        <button
          onClick={handleClose}
          className="absolute -top-4 -right-4 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>

        <h2 className="text-center text-3xl font-bold text-gray-900">
          {existingAccount ? 'Account Already Exists' : 'Create your account'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {existingAccount ? (
            <>
              This email is already registered.{' '}
              <Link to="/auth/login" className="font-medium text-brand-600 hover:text-brand-500">
                Sign in instead
              </Link>
            </>
          ) : (
            <>
              Already have an account?{' '}
              <Link to="/auth/login" className="font-medium text-brand-600 hover:text-brand-500">
                Sign in
              </Link>
            </>
          )}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
          {error && !existingAccount && (
            <div className="mb-4 p-4 bg-red-50 rounded-md flex items-center text-red-700">
              <AlertCircle className="w-5 h-5 mr-2" />
              {error}
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="fullName" className="block text-sm font-medium text-gray-700">
                Full Name
              </label>
              <div className="mt-1 relative">
                <User className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="fullName"
                  type="text"
                  required
                  value={fullName}
                  onChange={(e) => setFullName(e.target.value)}
                  className="pl-10 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="email"
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  minLength={8}
                  className="pl-10 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                  disabled={loading}
                />
              </div>
              <p className="mt-1 text-sm text-gray-500">
                Must be at least 8 characters long
              </p>
            </div>

            {existingAccount ? (
              <Button
                type="button"
                onClick={() => navigate('/auth/login')}
                className="w-full flex justify-center py-3"
              >
                Sign in instead
              </Button>
            ) : (
              <Button
                type="submit"
                className="w-full flex justify-center py-3"
                disabled={loading}
              >
                {loading ? 'Creating account...' : 'Create account'}
              </Button>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}
