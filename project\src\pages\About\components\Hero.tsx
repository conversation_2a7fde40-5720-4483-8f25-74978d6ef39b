import React from 'react';
import { motion } from 'framer-motion';
import { Building2, Shield, Users, Award } from 'lucide-react';

const stats = [
  {
    icon: Shield,
    value: '8+',
    label: 'Years Experience',
    description: 'Trusted industry leader'
  },
  {
    icon: Users,
    value: '250+',
    label: 'Happy Clients',
    description: 'Across multiple states'
  },
  {
    icon: Building2,
    value: '1,400+',
    label: 'Buildings Serviced',
    description: 'Spaces transformed'
  },
  {
    icon: Award,
    value: '98%',
    label: 'Satisfaction Rate',
    description: 'Client happiness score'
  }
];

export function Hero() {
  return (
    <section className="relative min-h-[600px] pt-32 pb-24 bg-brand-600 overflow-hidden">
      {/* Background Image & Overlay */}
      <div className="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1613665813446-82a78c468a1d?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80"
          alt="Professional cleaning team"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-brand-900/95 via-brand-800/90 to-brand-700/85" />
      </div>

      {/* Content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Text Content */}
          <div className="text-left">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="inline-flex items-center justify-center p-3 bg-brand-500/20 backdrop-blur-sm rounded-xl mb-6"
            >
              <Building2 className="w-6 h-6 text-white" />
            </motion.div>
            
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"
            >
              Setting the Standard <br />
              in Commercial Cleaning
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-xl text-brand-100 max-w-xl mb-12"
            >
              Since 2015, we've been transforming commercial spaces with our professional cleaning solutions. Our commitment to excellence has made us a trusted partner for businesses across the nation.
            </motion.p>

            {/* Trust Badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="flex flex-wrap gap-4"
            >
              <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-lg">
                <span className="text-white">✓ Licensed & Insured</span>
              </div>
              <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-lg">
                <span className="text-white">✓ Eco-Friendly Products</span>
              </div>
              <div className="bg-white/10 backdrop-blur-sm px-4 py-2 rounded-lg">
                <span className="text-white">✓ 24/7 Support</span>
              </div>
            </motion.div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20"
                >
                  <Icon className="w-8 h-8 text-brand-200 mb-4" />
                  <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-brand-200">{stat.label}</div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-gray-50 to-transparent" />
    </section>
  );
}
