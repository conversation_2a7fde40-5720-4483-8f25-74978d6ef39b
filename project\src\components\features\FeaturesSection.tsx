import React from 'react';
import { Leaf, DollarSign, Clock, Shield } from 'lucide-react';
import { FeatureCard } from './FeatureCard';
import { WaveBackground } from '../ui/WaveBackground';

const features = [
  {
    icon: Leaf,
    title: 'Eco-Friendly',
    description: 'We use 100% green-certified cleaning products.'
  },
  {
    icon: DollarSign,
    title: 'Affordable Pricing',
    description: 'Custom plans that fit any budget.'
  },
  {
    icon: Clock,
    title: 'Flexible Scheduling',
    description: 'Available 24/7 to match your needs.'
  },
  {
    icon: Shield,
    title: 'Trusted Professionals',
    description: 'All cleaners are vetted and insured.'
  }
];

export function FeaturesSection() {
  return (
    <section className="relative bg-gradient-to-b from-[#E3F2FD] to-[#B2EBF2] py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature) => (
            <FeatureCard key={feature.title} {...feature} />
          ))}
        </div>
      </div>
      <WaveBackground />
    </section>
  );
}
