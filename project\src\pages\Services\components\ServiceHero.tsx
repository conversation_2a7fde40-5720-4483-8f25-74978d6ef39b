import React from 'react';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';

export function ServiceHero() {
  return (
    <section className="relative pt-32 pb-16 bg-brand-600">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center justify-center p-3 bg-brand-500/20 rounded-xl mb-6"
          >
            <Sparkles className="w-6 h-6 text-white" />
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-4xl font-bold text-white mb-6"
          >
            Professional Cleaning Services
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-xl text-brand-100 max-w-2xl mx-auto"
          >
            Comprehensive cleaning solutions tailored to your business needs
          </motion.p>
        </div>
      </div>
    </section>
  );
}
