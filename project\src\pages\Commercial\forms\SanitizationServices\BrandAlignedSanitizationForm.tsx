import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, 
  ArrowRight, Briefcase, Mail, Phone, Home, User,
  Shield, Truck, Heart
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';

interface SanitizationFormData {
  servicePackage: string;
  facilityType: string;
  disinfectionLevel: string;
  areaSize: string;
  sanitizationAreas: string[];
  treatmentType: string;
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedSanitizationForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [formData, setFormData] = useState<Partial<SanitizationFormData>>({
    servicePackage: 'standard',
    facilityType: 'office',
    disinfectionLevel: 'standard',
    areaSize: 'medium',
    sanitizationAreas: [],
    treatmentType: 'chemical',
    serviceFrequency: 'monthly',
    addOns: [],
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    specialInstructions: '',
  });
  
  useEffect(() => {
    const savedData = localStorage.getItem('sanitizationFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('sanitizationFormData', JSON.stringify(formData));
  }, [formData]);

  const calculatePrice = () => {
    let basePrice = 0;
    
    // Base price by service package
    if (formData.servicePackage === 'standard') basePrice = 300;
    else if (formData.servicePackage === 'medical-grade') basePrice = 500;
    else if (formData.servicePackage === 'food-service') basePrice = 450;
    else if (formData.servicePackage === 'emergency') basePrice = 600;
    
    // Area size multiplier
    const areaMultiplier = formData.areaSize === 'small' ? 0.8 : 
                          formData.areaSize === 'medium' ? 1 : 
                          formData.areaSize === 'large' ? 1.6 : 
                          formData.areaSize === 'xl' ? 2.5 : 1;
    
    // Sanitization areas multiplier
    const areasMultiplier = 1 + ((formData.sanitizationAreas?.length || 0) * 0.15);
    
    // Disinfection level multiplier
    const levelMultiplier = formData.disinfectionLevel === 'hospital-grade' ? 1.4 : 1;
    
    const addOnPrice = (formData.addOns?.length || 0) * 125;
    return Math.round(basePrice * areaMultiplier * areasMultiplier * levelMultiplier + addOnPrice);
  };

  const steps = [
    { id: 1, name: 'Service Type' },
    { id: 2, name: 'Facility & Scope' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact' },
  ];

  const servicePackages = [
    { 
      id: 'standard', 
      name: 'Standard Sanitization', 
      icon: <Shield className="w-8 h-8" />, 
      description: 'Basic disinfection for routine maintenance' 
    },
    { 
      id: 'medical-grade', 
      name: 'Medical Grade', 
      icon: <Heart className="w-8 h-8" />, 
      description: 'Hospital-grade disinfection with EPA-approved chemicals' 
    },
    { 
      id: 'food-service', 
      name: 'Food Service Safe', 
      icon: <Briefcase className="w-8 h-8" />, 
      description: 'Food-safe sanitization for restaurants and kitchens' 
    },
    { 
      id: 'emergency', 
      name: 'Emergency Response', 
      icon: <Truck className="w-8 h-8" />, 
      description: 'Urgent sanitization for contamination events' 
    },
  ];

  const facilityTypes = [
    { id: 'office', name: 'Office Building' },
    { id: 'medical', name: 'Medical Facility' },
    { id: 'restaurant', name: 'Restaurant/Food Service' },
    { id: 'retail', name: 'Retail Store' },
    { id: 'school', name: 'Educational Facility' },
    { id: 'warehouse', name: 'Warehouse/Industrial' },
    { id: 'gym', name: 'Fitness Center' },
    { id: 'daycare', name: 'Daycare/Childcare' },
  ];

  const disinfectionLevels = [
    { id: 'standard', name: 'Standard Disinfection' },
    { id: 'hospital-grade', name: 'Hospital Grade (+40%)' },
  ];

  const areaSizes = [
    { id: 'small', name: 'Under 2,000 sq ft' },
    { id: 'medium', name: '2,000 - 8,000 sq ft' },
    { id: 'large', name: '8,000 - 20,000 sq ft' },
    { id: 'xl', name: '20,000+ sq ft' },
  ];

  const sanitizationAreaOptions = [
    { id: 'common-areas', name: 'Common Areas' },
    { id: 'workstations', name: 'Workstations/Desks' },
    { id: 'restrooms', name: 'Restrooms' },
    { id: 'break-rooms', name: 'Break Rooms/Kitchens' },
    { id: 'conference-rooms', name: 'Conference Rooms' },
    { id: 'reception', name: 'Reception Areas' },
    { id: 'elevators', name: 'Elevators' },
    { id: 'stairwells', name: 'Stairwells' },
  ];

  const treatmentTypes = [
    { id: 'chemical', name: 'Chemical Disinfection' },
    { id: 'uv-light', name: 'UV Light Treatment' },
    { id: 'electrostatic', name: 'Electrostatic Spraying' },
    { id: 'fogging', name: 'Disinfectant Fogging' },
  ];
  
  const serviceFrequencies = [
    { id: 'one-time', name: 'One-Time' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly' },
    { id: 'quarterly', name: 'Quarterly' },
    { id: 'as-needed', name: 'As Needed' },
  ];

  const addOnServices = [
    { id: 'air-purification', name: 'Air Purification', description: 'HEPA filtration and air sanitization', price: 200 },
    { id: 'surface-coating', name: 'Antimicrobial Coating', description: 'Long-lasting surface protection', price: 300 },
    { id: 'deep-carpet', name: 'Deep Carpet Sanitization', description: 'Steam cleaning with disinfection', price: 150 },
    { id: 'hvac-treatment', name: 'HVAC System Treatment', description: 'Ductwork and system sanitization', price: 400 },
    { id: 'high-touch', name: 'High-Touch Surface Focus', description: 'Extra attention to door handles, switches', price: 100 },
    { id: 'certification', name: 'Sanitization Certificate', description: 'Official completion documentation', price: 75 },
  ];

  const timeSlots = [
    { id: 'early-morning', name: 'Early Morning (5AM - 8AM)' },
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (12PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' },
    { id: 'overnight', name: 'Overnight (9PM - 5AM)' },
  ];
  
  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  const handleSanitizationAreaToggle = (areaId: string) => {
    const currentAreas = formData.sanitizationAreas || [];
    if (currentAreas.includes(areaId)) {
      setFormData({
        ...formData,
        sanitizationAreas: currentAreas.filter(id => id !== areaId)
      });
    } else {
      setFormData({
        ...formData,
        sanitizationAreas: [...currentAreas, areaId]
      });
    }
  };

  const validateField = (fieldName: string, value: string) => {
    const errors = { ...validationErrors };
    
    switch (fieldName) {
      case 'email': {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (value && !emailRegex.test(value)) {
          errors.email = 'Please enter a valid email address';
        } else {
          delete errors.email;
        }
        break;
      }
      case 'phone': {
        const phoneRegex = /\d{10,}/;
        if (value && !phoneRegex.test(value.replace(/\D/g, ''))) {
          errors.phone = 'Please enter a valid phone number (at least 10 digits)';
        } else {
          delete errors.phone;
        }
        break;
      }
      case 'companyName':
        if (!value || value.trim().length < 2) {
          errors.companyName = 'Company name must be at least 2 characters';
        } else {
          delete errors.companyName;
        }
        break;
      case 'contactName':
        if (!value || value.trim().length < 2) {
          errors.contactName = 'Contact name must be at least 2 characters';
        } else {
          delete errors.contactName;
        }
        break;
      case 'address':
        if (!value || value.trim().length < 5) {
          errors.address = 'Please enter a valid address';
        } else {
          delete errors.address;
        }
        break;
      case 'city':
        if (!value || value.trim().length < 2) {
          errors.city = 'Please enter a valid city';
        } else {
          delete errors.city;
        }
        break;
      case 'zipCode': {
        const zipRegex = /^\d{5}(-\d{4})?$/;
        if (!value || !zipRegex.test(value)) {
          errors.zipCode = 'Please enter a valid ZIP code (12345 or 12345-6789)';
        } else {
          delete errors.zipCode;
        }
        break;
      }
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateForm = () => {
    const fieldsToValidate = ['companyName', 'contactName', 'email', 'phone', 'address', 'city', 'zipCode'];
    let isValid = true;
    
    fieldsToValidate.forEach(field => {
      const fieldValue = formData[field as keyof SanitizationFormData] as string || '';
      if (!validateField(field, fieldValue)) {
        isValid = false;
      }
    });
    
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      // Simulate API call to save the estimate and schedule
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Clear form data from localStorage
      localStorage.removeItem('sanitizationFormData');
      
      // Navigate to user dashboard to view booking
      navigate('/accountdashboard', { 
        state: { 
          newBooking: {
            id: Date.now(), // Temporary ID until backend assigns real ID
            serviceType: 'Commercial Sanitization Service',
            estimate: calculatePrice(),
            status: 'Pending Confirmation',
            date: formData.preferredDate,
            time: formData.preferredTime,
            facility: formData.facilityType,
            package: formData.servicePackage,
            address: `${formData.address}, ${formData.city}, ${formData.zipCode}`,
            contact: {
              company: formData.companyName,
              name: formData.contactName,
              email: formData.email,
              phone: formData.phone
            },
            createdAt: new Date().toISOString(),
            message: 'Your sanitization service has been scheduled! We will contact you within 24 hours to confirm the appointment details.'
          }
        } 
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      setValidationErrors({ submit: 'There was an error submitting your request. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2">
              Commercial Sanitization Services
            </h1>
            <p className="text-gray-200">Professional sanitization and disinfection for commercial facilities.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= step.id ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-green-500 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sm:p-8" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-white mb-6">Select Service Package</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {servicePackages.map(pkg => (
                      <motion.button 
                        key={pkg.id} 
                        onClick={() => setFormData({...formData, servicePackage: pkg.id})} 
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-300 ${
                          formData.servicePackage === pkg.id 
                            ? 'bg-green-500/20 border-green-400 shadow-lg shadow-green-400/20' 
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center gap-4">
                          <div className="text-green-300">{pkg.icon}</div>
                          <div>
                            <h3 className="font-semibold text-white">{pkg.name}</h3>
                            <p className="text-sm text-gray-300">{pkg.description}</p>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      onClick={() => setCurrentStep(2)} 
                      disabled={!formData.servicePackage}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-white mb-6">Facility & Scope Details</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <GlassmorphismSelect
                      options={facilityTypes}
                      value={formData.facilityType}
                      onChange={value => setFormData({...formData, facilityType: value})}
                      placeholder="Facility Type"
                    />
                    <GlassmorphismSelect
                      options={disinfectionLevels}
                      value={formData.disinfectionLevel}
                      onChange={value => setFormData({...formData, disinfectionLevel: value})}
                      placeholder="Disinfection Level"
                    />
                    <GlassmorphismSelect
                      options={areaSizes}
                      value={formData.areaSize}
                      onChange={value => setFormData({...formData, areaSize: value})}
                      placeholder="Facility Size"
                    />
                    <GlassmorphismSelect
                      options={treatmentTypes}
                      value={formData.treatmentType}
                      onChange={value => setFormData({...formData, treatmentType: value})}
                      placeholder="Treatment Method"
                    />
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-white mb-3">Sanitization Areas (Select all that apply)</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                      {sanitizationAreaOptions.map(area => (
                        <motion.button
                          key={area.id}
                          onClick={() => handleSanitizationAreaToggle(area.id)}
                          className={`p-3 rounded-lg border-2 text-sm transition-all duration-300 ${
                            (formData.sanitizationAreas || []).includes(area.id)
                              ? 'bg-green-500/20 border-green-400 text-white'
                              : 'bg-white/5 border-white/20 text-gray-300 hover:border-white/40'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          {area.name}
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <GlassmorphismSelect
                      options={serviceFrequencies}
                      value={formData.serviceFrequency}
                      onChange={value => setFormData({...formData, serviceFrequency: value})}
                      placeholder="Service Frequency"
                    />
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={e => setFormData({...formData, preferredDate: e.target.value})} 
                      className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>

                  <div className="mb-6">
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={value => setFormData({...formData, preferredTime: value})}
                      placeholder="Preferred Time"
                    />
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)} 
                      disabled={!formData.facilityType || !formData.disinfectionLevel || !formData.areaSize || !formData.treatmentType || !formData.serviceFrequency || !formData.preferredDate || !formData.preferredTime}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-white mb-6">Add-on Services</h2>
                  <p className="text-gray-300 mb-6">Select any additional services you need:</p>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map(addon => (
                      <motion.button 
                        key={addon.id} 
                        onClick={() => handleAddOnToggle(addon.id)} 
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-300 ${
                          (formData.addOns || []).includes(addon.id) 
                            ? 'bg-green-500/20 border-green-400 shadow-lg shadow-green-400/20' 
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-white">{addon.name}</h3>
                          <span className="text-green-400 font-medium">+${addon.price}</span>
                        </div>
                        <p className="text-sm text-gray-300">{addon.description}</p>
                      </motion.button>
                    ))}
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-white mb-6">Contact Information</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Company Name" 
                        value={formData.companyName || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, companyName: value});
                          validateField('companyName', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.companyName 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.companyName && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.companyName}</p>
                      )}
                    </div>
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Contact Name" 
                        value={formData.contactName || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, contactName: value});
                          validateField('contactName', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.contactName 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.contactName && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.contactName}</p>
                      )}
                    </div>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, email: value});
                          validateField('email', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.email 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, phone: value});
                          validateField('phone', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.phone 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    <div className="relative sm:col-span-2">
                      <Home className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Facility Address" 
                        value={formData.address || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, address: value});
                          validateField('address', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.address 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                      )}
                    </div>
                    <div>
                      <input 
                        type="text" 
                        placeholder="City" 
                        value={formData.city || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, city: value});
                          validateField('city', value);
                        }}
                        className={`w-full bg-white/10 p-3 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.city 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.city && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                      )}
                    </div>
                    <div>
                      <input 
                        type="text" 
                        placeholder="ZIP Code" 
                        value={formData.zipCode || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, zipCode: value});
                          validateField('zipCode', value);
                        }}
                        className={`w-full bg-white/10 p-3 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.zipCode 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.zipCode && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                      )}
                    </div>
                  </div>

                  <textarea 
                    placeholder="Special Instructions (contamination concerns, access requirements, etc.)" 
                    value={formData.specialInstructions || ''} 
                    onChange={e => setFormData({...formData, specialInstructions: e.target.value})} 
                    className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all resize-none" 
                    rows={4} 
                  />

                  {/* Estimate Display */}
                  <div className="mb-6 p-4 bg-green-500/10 border border-green-400/30 rounded-xl">
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-white mb-2">Service Estimate</h3>
                      <div className="text-3xl font-bold text-green-400 mb-2">
                        ${calculatePrice()}
                      </div>
                      <p className="text-white/70 text-sm">
                        Final price confirmed after site inspection
                      </p>
                    </div>
                  </div>

                  {validationErrors.submit && (
                    <div className="mb-4 p-3 bg-red-500/10 border border-red-400/30 rounded-xl">
                      <p className="text-red-400 text-sm">{validationErrors.submit}</p>
                    </div>
                  )}

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!formData.companyName || !formData.contactName || !formData.email || !formData.phone || !formData.address || !formData.city || !formData.zipCode || isSubmitting || Object.keys(validationErrors).length > 0}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Schedule Service <CheckCircle className="ml-2 h-5 w-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>


    </AnimatedBackground>
  );
};

export default BrandAlignedSanitizationForm; 
