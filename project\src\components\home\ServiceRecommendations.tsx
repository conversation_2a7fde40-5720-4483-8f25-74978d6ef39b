import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Home, Building2, Sparkles, GlassWater, Brush, Construction,
  Sprout, Grid, Waves, Hammer, ChevronRight, Star, Clock
} from 'lucide-react';

interface ServiceRecommendation {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  route: string;
  category: 'residential' | 'commercial';
  popular?: boolean;
  price?: string;
}

export function ServiceRecommendations() {
  const navigate = useNavigate();

  const recommendations: ServiceRecommendation[] = [
    // Residential Services
    {
      id: 'regular',
      title: 'Regular House Cleaning',
      description: 'Weekly, bi-weekly, or monthly cleaning',
      icon: Home,
      route: '/residential/regular',
      category: 'residential',
      popular: true,
      price: 'From $89'
    },
    {
      id: 'deep',
      title: 'Deep Cleaning',
      description: 'Thorough one-time cleaning service',
      icon: Sparkles,
      route: '/residential/deep',
      category: 'residential',
      popular: true,
      price: 'From $149'
    },
    {
      id: 'carpet',
      title: 'Carpet Cleaning',
      description: 'Professional carpet & upholstery care',
      icon: Brush,
      route: '/residential/carpet',
      category: 'residential',
      price: 'From $99'
    },
    {
      id: 'window',
      title: 'Window Cleaning',
      description: 'Interior & exterior window cleaning',
      icon: GlassWater,
      route: '/residential/window',
      category: 'residential',
      price: 'From $79'
    },
    {
      id: 'pressure',
      title: 'Pressure Washing',
      description: 'Driveways, patios, and exterior surfaces',
      icon: Waves,
      route: '/residential/pressure',
      category: 'residential',
      price: 'From $129'
    },
    {
      id: 'sanitization',
      title: 'Sanitization',
      description: 'Medical-grade disinfection service',
      icon: Sprout,
      route: '/residential/sanitization',
      category: 'residential',
      price: 'From $99'
    },
    // Commercial Services
    {
      id: 'office',
      title: 'Office Cleaning',
      description: 'Daily, weekly, or monthly office cleaning',
      icon: Building2,
      route: '/service-form/office',
      category: 'commercial',
      popular: true,
      price: 'From $199'
    },
    {
      id: 'construction',
      title: 'Post-Construction',
      description: 'Construction cleanup & debris removal',
      icon: Construction,
      route: '/service-form/construction',
      category: 'commercial',
      price: 'From $299'
    }
  ];

  const residentialServices = recommendations.filter(s => s.category === 'residential');
  const commercialServices = recommendations.filter(s => s.category === 'commercial');

  const handleServiceClick = (service: ServiceRecommendation) => {
    navigate(service.route);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Popular Cleaning Services
          </h2>
          <motion.div
            initial={{ scaleX: 0 }}
            whileInView={{ scaleX: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="w-24 h-1 bg-brand-600 mx-auto mb-6 origin-center"
          />
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Choose from our most requested professional cleaning solutions
          </p>
        </motion.div>

        {/* Residential Services */}
        <div className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Home className="w-6 h-6 text-brand-600" />
            <h3 className="text-2xl font-semibold text-gray-900">Residential Services</h3>
            <span className="text-sm text-gray-500 ml-auto">For your home</span>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
            {residentialServices.map((service, index) => {
              const Icon = service.icon;
              return (
                <motion.button
                  key={service.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleServiceClick(service)}
                  className="relative group bg-white rounded-xl p-6 text-center hover:shadow-lg 
                           transition-all duration-300 border border-gray-100 hover:border-brand-200
                           hover:-translate-y-1"
                >
                  {service.popular && (
                    <div className="absolute -top-2 -right-2 bg-brand-600 text-white text-xs 
                                  px-2 py-1 rounded-full flex items-center gap-1">
                      <Star className="w-3 h-3 fill-current" />
                      Popular
                    </div>
                  )}
                  
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-brand-50 flex items-center 
                                justify-center group-hover:bg-brand-100 transition-colors">
                    <Icon className="w-8 h-8 text-brand-600" />
                  </div>
                  
                  <h4 className="font-semibold text-gray-900 mb-2 group-hover:text-brand-600 transition-colors">
                    {service.title}
                  </h4>
                  
                  <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                    {service.description}
                  </p>
                  
                  {service.price && (
                    <div className="text-brand-600 font-semibold text-sm mb-2">
                      {service.price}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-center text-brand-600 text-sm font-medium
                                opacity-0 group-hover:opacity-100 transition-opacity">
                    Get Quote
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </div>
                </motion.button>
              );
            })}
          </div>
          
          <motion.button
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            onClick={() => navigate('/residential')}
            className="w-full sm:w-auto mx-auto block px-8 py-3 bg-brand-50 hover:bg-brand-100 
                     text-brand-600 hover:text-brand-700 font-medium rounded-lg transition-colors
                     flex items-center justify-center gap-2"
          >
            View All Residential Services
            <ChevronRight className="w-4 h-4" />
          </motion.button>
        </div>

        {/* Commercial Services */}
        <div>
          <div className="flex items-center gap-3 mb-6">
            <Building2 className="w-6 h-6 text-blue-600" />
            <h3 className="text-2xl font-semibold text-gray-900">Commercial Services</h3>
            <span className="text-sm text-gray-500 ml-auto">For your business</span>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {commercialServices.map((service, index) => {
              const Icon = service.icon;
              return (
                <motion.button
                  key={service.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleServiceClick(service)}
                  className="relative group bg-white rounded-xl p-6 text-center hover:shadow-lg 
                           transition-all duration-300 border border-gray-100 hover:border-blue-200
                           hover:-translate-y-1"
                >
                  {service.popular && (
                    <div className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs 
                                  px-2 py-1 rounded-full flex items-center gap-1">
                      <Star className="w-3 h-3 fill-current" />
                      Popular
                    </div>
                  )}
                  
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-50 flex items-center 
                                justify-center group-hover:bg-blue-100 transition-colors">
                    <Icon className="w-8 h-8 text-blue-600" />
                  </div>
                  
                  <h4 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                    {service.title}
                  </h4>
                  
                  <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                    {service.description}
                  </p>
                  
                  {service.price && (
                    <div className="text-blue-600 font-semibold text-sm mb-2">
                      {service.price}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-center text-blue-600 text-sm font-medium
                                opacity-0 group-hover:opacity-100 transition-opacity">
                    Get Quote
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </div>
                </motion.button>
              );
            })}
          </div>
          
          <motion.button
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            onClick={() => navigate('/commercial')}
            className="w-full sm:w-auto mx-auto block px-8 py-3 bg-blue-50 hover:bg-blue-100 
                     text-blue-600 hover:text-blue-700 font-medium rounded-lg transition-colors
                     flex items-center justify-center gap-2"
          >
            View All Commercial Services
            <ChevronRight className="w-4 h-4" />
          </motion.button>
        </div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6 text-center"
        >
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="text-2xl font-bold text-brand-600 mb-1">4.5M+</div>
            <div className="text-sm text-gray-600">Customers Served</div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="text-2xl font-bold text-brand-600 mb-1 flex items-center justify-center gap-1">
              4.9 <Star className="w-5 h-5 text-yellow-400 fill-current" />
            </div>
            <div className="text-sm text-gray-600">Average Rating</div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="text-2xl font-bold text-brand-600 mb-1 flex items-center justify-center gap-1">
              <Clock className="w-6 h-6" /> 24/7
            </div>
            <div className="text-sm text-gray-600">Emergency Service</div>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <div className="text-2xl font-bold text-brand-600 mb-1">100%</div>
            <div className="text-sm text-gray-600">Satisfaction Guarantee</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
} 
