import React from 'react';
import type { LucideIcon } from 'lucide-react';

interface ServiceCardProps {
  service: {
    id: string;
    icon: LucideIcon;
    title: string;
    description: string;
    price: string;
  };
  onBook: (serviceId: string) => void;
}

export function ServiceCard({ service, onBook }: ServiceCardProps) {
  const Icon = service.icon;
  
  const handleClick = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    onBook(service.id);
  };

  return (
    <button
      onClick={handleClick}
      className="group relative w-full p-6 rounded-xl text-left transition-all duration-200 
                bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md
                active:scale-[0.98] touch-manipulation"
    >
      <div className="flex items-start space-x-4">
        <div className="rounded-lg bg-brand-50 p-3 transition-colors group-hover:bg-brand-100">
          <Icon className="w-6 h-6 text-brand-600" />
        </div>
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 mb-1">{service.title}</h3>
          <p className="text-sm text-gray-600">{service.description}</p>
          <p className="mt-2 font-medium text-brand-600">{service.price}</p>
        </div>
      </div>
    </button>
  );
}
