import React from 'react';
import { Users, <PERSON>, Bath, Leaf } from 'lucide-react';

export function LayoutLegend() {
  const items = [
    { icon: Users, label: 'Work Area', color: 'green' },
    { icon: Users, label: 'Collaboration', color: 'blue' },
    { icon: Coffee, label: 'Break Room', color: 'yellow' },
    { icon: Bath, label: 'Facilities', color: 'purple' },
    { icon: Leaf, label: 'Green Space', color: 'emerald' }
  ];

  return (
    <div className="flex items-center space-x-4">
      {items.map(({ icon: Icon, label, color }) => (
        <div key={label} className="flex items-center text-sm text-gray-600">
          <Icon className={`w-4 h-4 mr-1 text-${color}-600`} />
          <span>{label}</span>
        </div>
      ))}
    </div>
  );
}
