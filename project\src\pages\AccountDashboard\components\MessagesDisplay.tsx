import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, Send, MoreHorizontal, Info, Users, Headphones, User, Paperclip } from 'lucide-react';

interface Contact {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  isOnline: boolean;
  unreadCount?: number;
  role: 'cleaner' | 'support' | 'customer';
}

const mockContacts: Contact[] = [
  {
    id: '1',
    name: '<PERSON>',
    avatar: '👩‍🧹',
    lastMessage: 'Your cleaning is scheduled for tomorrow at 2 PM',
    timestamp: '2m ago',
    isOnline: true,
    unreadCount: 1,
    role: 'cleaner'
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar: '👨‍🔧',
    lastMessage: 'Deep cleaning completed! Everything looks great.',
    timestamp: '1h ago',
    isOnline: true,
    role: 'cleaner'
  },
  {
    id: '3',
    name: 'Support Team',
    avatar: '🎧',
    lastMessage: 'How can we help you today?',
    timestamp: '3h ago',
    isOnline: true,
    unreadCount: 2,
    role: 'support'
  },
  {
    id: '4',
    name: '<PERSON>',
    avatar: '👩‍💼',
    lastMessage: 'Thank you for the excellent service!',
    timestamp: 'Yesterday',
    isOnline: false,
    role: 'customer'
  },
  {
    id: '5',
    name: 'Alex Rodriguez',
    avatar: '👨‍🧹',
    lastMessage: 'Running 10 minutes late, sorry!',
    timestamp: 'Yesterday',
    isOnline: false,
    role: 'cleaner'
  }
];



// Role configuration with proper icons and styling
const roleConfig = {
  cleaner: {
    icon: Users,
    label: 'Cleaner',
    bgColor: 'bg-blue-500/15',
    textColor: 'text-blue-400',
    borderColor: 'border-blue-500/30'
  },
  support: {
    icon: Headphones,
    label: 'Support',
    bgColor: 'bg-purple-500/15',
    textColor: 'text-purple-400',
    borderColor: 'border-purple-500/30'
  },
  customer: {
    icon: User,
    label: 'Customer',
    bgColor: 'bg-emerald-500/15',
    textColor: 'text-emerald-400',
    borderColor: 'border-emerald-500/30'
  }
};

// Role Tag Component
const RoleTag = ({ role }: { role: keyof typeof roleConfig }) => {
  const config = roleConfig[role];
  const Icon = config.icon;
  
  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${config.bgColor} ${config.textColor} ${config.borderColor}`}>
      <Icon className="w-3 h-3" />
      {config.label}
    </div>
  );
};

// Contact Item Component
const ContactItem = ({ 
  contact, 
  isSelected, 
  onClick 
}: { 
  contact: Contact; 
  isSelected: boolean; 
  onClick: () => void; 
}) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    onClick={onClick}
    whileHover={{ scale: 1.02, x: 4 }}
    whileTap={{ scale: 0.98 }}
    className={`p-3 md:p-4 cursor-pointer transition-all duration-300 relative overflow-hidden touch-manipulation ${
      isSelected 
        ? 'bg-gradient-to-r from-emerald-500/20 to-emerald-400/10 border-r-4 border-r-emerald-400 shadow-lg shadow-emerald-500/10' 
        : 'hover:bg-white/8 hover:shadow-md'
    }`}
  >
    {/* Animated background for selected state */}
    {isSelected && (
      <motion.div
        initial={{ opacity: 0, x: -100 }}
        animate={{ opacity: 1, x: 0 }}
        className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 via-emerald-400/5 to-transparent"
      />
    )}
    
    {/* Hover glow effect */}
    <motion.div
      className="absolute inset-0 bg-gradient-to-r from-white/5 to-transparent opacity-0"
      whileHover={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    />
    
    <div className="flex items-start gap-2 md:gap-3 relative z-10">
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <motion.h3 
            className={`font-semibold text-xs md:text-sm truncate pr-1 md:pr-2 transition-colors duration-300 ${
              isSelected ? 'text-white' : 'text-white/90'
            }`}
            animate={{ x: isSelected ? 2 : 0 }}
          >
            {contact.name}
          </motion.h3>
          <span className={`text-xs flex-shrink-0 transition-colors duration-300 ${
            isSelected ? 'text-emerald-300' : 'text-gray-400'
          }`}>
            {contact.timestamp}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <p className={`text-xs truncate pr-1 md:pr-2 transition-colors duration-300 ${
            isSelected ? 'text-white/80' : 'text-gray-400'
          }`}>
            {contact.lastMessage}
          </p>
          {contact.unreadCount && (
            <motion.div 
              className="w-4 h-4 md:w-5 md:h-5 bg-emerald-500 text-white text-xs rounded-full flex items-center justify-center font-bold flex-shrink-0 shadow-lg"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            >
              {contact.unreadCount}
            </motion.div>
          )}
        </div>
        
        <motion.div 
          className="mt-2"
          animate={{ y: isSelected ? -1 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <RoleTag role={contact.role} />
        </motion.div>
        
        {/* Online status indicator */}
        {contact.isOnline && (
          <motion.div 
            className="flex items-center gap-1 mt-2"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <div className="w-2 h-2 bg-green-400 rounded-full" />
            <span className="text-xs text-green-400">Online</span>
          </motion.div>
        )}
      </div>
    </div>
  </motion.div>
);

const MessagesDisplay = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedContact, setSelectedContact] = useState<Contact>(mockContacts[0]);
  const [messageText, setMessageText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Contact-specific messages
  const [contactMessages, setContactMessages] = useState<Record<string, Array<{id: string, text: string, timestamp: string, isFromUser: boolean}>>>({
    '1': [
      { id: '1', text: 'Hi! I have a question about my upcoming cleaning service.', timestamp: '2:30 PM', isFromUser: true },
      { id: '2', text: 'Hello! I\'d be happy to help you with any questions about your service.', timestamp: '2:32 PM', isFromUser: false },
      { id: '3', text: 'Great! Can you confirm the time for tomorrow?', timestamp: '2:35 PM', isFromUser: true }
    ],
    '2': [
      { id: '4', text: 'Your cleaning is scheduled for 2 PM today. We\'ll be there on time!', timestamp: '10:15 AM', isFromUser: false },
      { id: '5', text: 'Perfect! Thank you for the update.', timestamp: '10:16 AM', isFromUser: true }
    ],
    '3': [
      { id: '6', text: 'How can I help you today?', timestamp: '1:45 PM', isFromUser: false },
      { id: '7', text: 'I need to reschedule my appointment.', timestamp: '1:46 PM', isFromUser: true },
      { id: '8', text: 'No problem! What date works better for you?', timestamp: '1:47 PM', isFromUser: false }
    ]
  });

  const filteredContacts = mockContacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const currentMessages = contactMessages[selectedContact.id] || [];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentMessages, selectedContact.id]);

  const handleSendMessage = () => {
    if (messageText.trim()) {
      const newMessage = {
        id: Date.now().toString(),
        text: messageText.trim(),
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isFromUser: true
      };
      
      setContactMessages(prev => ({
        ...prev,
        [selectedContact.id]: [...(prev[selectedContact.id] || []), newMessage]
      }));
      setMessageText('');
      
      // Simulate response after 1-2 seconds
      setTimeout(() => {
        const responses = [
          "Thanks for your message! I'll get back to you shortly.",
          "Got it! Let me check on that for you.",
          "I'll look into this right away.",
          "Thank you for reaching out. I'll help you with that.",
          "Perfect! I'll take care of this for you."
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        const responseMessage = {
          id: (Date.now() + 1).toString(),
          text: randomResponse,
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          isFromUser: false
        };
        
        setContactMessages(prev => ({
          ...prev,
          [selectedContact.id]: [...(prev[selectedContact.id] || []), responseMessage]
        }));
      }, 1000 + Math.random() * 1000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="pt-6 md:pt-8">
      {/* Clean Header */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between mb-4 md:mb-6"
      >
        <h2 className="text-xl md:text-2xl font-bold text-white">Messages</h2>
        <div className="text-xs md:text-sm text-gray-400">
          {filteredContacts.filter(c => c.unreadCount).length} unread
        </div>
      </motion.div>

      {/* Modern Messages Interface */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="h-[500px] md:h-[680px] flex flex-col md:flex-row bg-black/20 backdrop-blur-3xl border-2 border-white/10 rounded-2xl md:rounded-3xl overflow-hidden shadow-2xl relative"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-white/[0.08] via-transparent to-black/[0.05] pointer-events-none" />
        
        {/* Contacts Sidebar */}
        <div className="w-full md:w-80 bg-black/10 backdrop-blur-xl border-b md:border-b-0 md:border-r border-white/10 flex flex-col relative z-10 max-h-48 md:max-h-none">
          {/* Search */}
          <div className="p-3 md:p-4 border-b border-white/10">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-3 h-3 md:w-4 md:h-4" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-8 md:pl-10 pr-3 md:pr-4 py-2 md:py-3 bg-white/10 border border-white/20 rounded-lg md:rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400/50 focus:bg-white/15 transition-all text-sm md:text-base"
              />
            </div>
          </div>

          {/* Contacts List */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
            {filteredContacts.map((contact) => (
              <ContactItem
                key={contact.id}
                contact={contact}
                isSelected={selectedContact.id === contact.id}
                onClick={() => setSelectedContact(contact)}
              />
            ))}
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col relative z-10">
          {/* Chat Header */}
          <div className="p-3 md:p-6 border-b border-white/10 flex items-center justify-between">
            <motion.div 
              className="flex items-center gap-2 md:gap-4 min-w-0 flex-1"
              key={selectedContact.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="min-w-0 flex-1">
                <motion.div 
                  className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 mb-1"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <h3 className="text-white font-semibold text-base md:text-lg truncate">{selectedContact.name}</h3>
                  <div className="flex items-center gap-2">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    >
                      <RoleTag role={selectedContact.role} />
                    </motion.div>
                    {selectedContact.isOnline && (
                      <motion.div 
                        className="flex items-center gap-1"
                        animate={{ opacity: [0.5, 1, 0.5] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <div className="w-2 h-2 bg-green-400 rounded-full" />
                        <span className="text-xs text-green-400 hidden sm:inline">Online</span>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
                <motion.p 
                  className="text-xs md:text-sm text-gray-400"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.15 }}
                >
                  {selectedContact.isOnline ? 'Active now' : 'Last seen recently'}
                </motion.p>
              </div>
            </motion.div>
            <div className="flex items-center gap-1 md:gap-2 flex-shrink-0">
              <motion.button 
                className="p-2 md:p-3 hover:bg-white/10 rounded-lg md:rounded-xl transition-all duration-200 group touch-manipulation"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Info className="w-4 h-4 md:w-5 md:h-5 text-gray-400 group-hover:text-white transition-colors" />
              </motion.button>
              <motion.button 
                className="p-2 md:p-3 hover:bg-white/10 rounded-lg md:rounded-xl transition-all duration-200 group touch-manipulation"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <MoreHorizontal className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
              </motion.button>
            </div>
          </div>

          {/* Messages Area */}
          <motion.div 
            className="flex-1 overflow-y-auto p-3 md:p-6 space-y-3 md:space-y-4"
            key={`messages-${selectedContact.id}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            {currentMessages.map((message) => (
              <motion.div 
                key={message.id}
                className={`bg-${message.isFromUser ? 'emerald' : 'white'}-500/20 backdrop-blur-lg p-3 md:p-4 rounded-xl md:rounded-2xl max-w-[85%] sm:max-w-xs ${message.isFromUser ? 'ml-auto' : ''} border border-${message.isFromUser ? 'emerald' : 'white'}-400/30`}
                initial={{ opacity: 0, x: message.isFromUser ? 20 : -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: message.isFromUser ? 0.2 : 0.3 }}
              >
                <p className="text-white text-sm">{message.text}</p>
                <span className="text-xs text-gray-400 mt-2 block">{message.timestamp}</span>
              </motion.div>
            ))}
            <div ref={messagesEndRef} />
          </motion.div>

          {/* Message Input */}
          <motion.div 
            className="p-3 md:p-6 border-t border-white/10"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <div className="flex items-center gap-2 md:gap-3">
              <motion.button 
                className="p-2 md:p-3 hover:bg-white/10 rounded-lg md:rounded-xl transition-all duration-200 group touch-manipulation"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Paperclip className="w-4 h-4 md:w-5 md:h-5 text-gray-400 group-hover:text-white transition-colors" />
              </motion.button>
              <motion.div 
                className="flex-1 relative"
                whileFocus={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
              >
                <input
                  type="text"
                  placeholder="Type a message..."
                  className="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-lg md:rounded-2xl px-3 md:px-4 py-2 md:py-3 text-sm md:text-base text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-400/50 focus:border-emerald-400/50 transition-all duration-300 touch-manipulation"
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  onKeyDown={handleKeyPress}
                />
              </motion.div>
              <motion.button 
                className="p-2 md:p-3 bg-emerald-500 hover:bg-emerald-600 rounded-lg md:rounded-xl transition-all duration-200 group shadow-lg shadow-emerald-500/25 touch-manipulation"
                whileHover={{ scale: 1.1, boxShadow: "0 20px 25px -5px rgba(16, 185, 129, 0.4)" }}
                whileTap={{ scale: 0.9 }}
                onClick={handleSendMessage}
              >
                <Send className="w-4 h-4 md:w-5 md:h-5 text-white" />
              </motion.button>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export { MessagesDisplay };
