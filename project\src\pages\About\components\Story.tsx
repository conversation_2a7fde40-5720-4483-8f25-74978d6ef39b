import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Users, Award, Star } from 'lucide-react';

const leadership = {
  founder: {
    name: '<PERSON>',
    role: 'Founder & CEO',
    quote: 'Our mission is to deliver exceptional cleaning services that transform spaces and exceed expectations.'
  },
  cofounder: {
    name: '<PERSON><PERSON>',
    role: 'Co-Founder & COO',
    quote: 'We believe in building lasting relationships through quality, reliability, and trust.'
  }
};

const achievements = [
  {
    icon: Shield,
    label: 'Licensed & Insured',
    description: 'Fully bonded with comprehensive coverage'
  },
  {
    icon: Users,
    label: '250+ Happy Clients',
    description: 'Across 8 states'
  },
  {
    icon: Award,
    label: '1,400+ Buildings',
    description: 'Serviced and maintained'
  },
  {
    icon: Star,
    label: '4.9/5 Rating',
    description: '98% client satisfaction'
  }
];

export function Story() {
  return (
    <section className="py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-24">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <h2 className="text-3xl font-bold text-gray-900">Our Story</h2>
            <p className="text-xl text-gray-600">
              Founded in Queens, NY in 2015 by Mohammad Zaman, Empire Pro Cleaning started with a simple mission: to provide exceptional cleaning services that businesses can trust. What began as a local operation has grown into a multi-state service provider, thanks to our unwavering commitment to quality and customer satisfaction.
            </p>
            <p className="text-xl text-gray-600">
              In 2017, Gerson Mendoza joined as Co-Founder and COO, bringing additional expertise and vision to the company. Together, they have built Empire Pro into a leading commercial cleaning provider, serving hundreds of clients across multiple states and maintaining millions of square feet of commercial space daily.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="relative aspect-w-16 aspect-h-9"
          >
            <img
              src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80"
              alt="Professional cleaning team"
              className="rounded-2xl shadow-2xl object-cover"
            />
            <div className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 max-w-xs">
              <div className="flex items-center space-x-3 mb-2">
                <Shield className="w-6 h-6 text-brand-600" />
                <span className="font-semibold text-gray-900">Trust & Excellence</span>
              </div>
              <p className="text-gray-600 text-sm">
                Over 1,400 buildings serviced across the United States
              </p>
            </div>
          </motion.div>
        </div>

        {/* Leadership Section */}
        <div className="mb-24">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Leadership</h3>
            <p className="text-xl text-gray-600">The visionaries behind Empire Pro Cleaning</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[leadership.founder, leadership.cofounder].map((leader) => (
              <motion.div
                key={leader.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow"
              >
                <div className="mb-6">
                  <h4 className="text-xl font-semibold text-gray-900">{leader.name}</h4>
                  <p className="text-brand-600">{leader.role}</p>
                </div>
                <blockquote className="text-gray-600 italic">
                  "{leader.quote}"
                </blockquote>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Achievements Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {achievements.map((achievement, index) => {
            const Icon = achievement.icon;
            return (
              <motion.div
                key={achievement.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-brand-100 mb-4">
                  <Icon className="w-6 h-6 text-brand-600" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{achievement.label}</h4>
                <p className="text-gray-600">{achievement.description}</p>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
