import React from 'react';
import { motion } from 'framer-motion';
import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { AnimatedBackground } from '../../components/layout/AnimatedBackground';
import { ResidentialHero } from './components/ResidentialHero';
import { ServicesGrid } from './components/ServicesGrid';
import { Features } from './components/Features';
import { Process } from './components/Process';
import { Testimonials } from './components/Testimonials';
import { CallToAction } from './components/CallToAction';
import { Benefits } from './components/Benefits';

const Section: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="py-12 md:py-16 lg:py-20">{children}</div>
);

export function Residential() {
  return (
    <AnimatedBackground>
      <div className="relative h-screen overflow-hidden">
        <Header />
        
        {/* Scrollable Content Container */}
        <div className="h-full overflow-y-auto" style={{ paddingTop: '80px' }}>
          <main className="relative">
            {/* Hero Section with Dark Glass */}
            <ResidentialHero />
            
            {/* Glass Content Sections with Enhanced Styling */}
            <motion.div
              className="backdrop-blur-[30px] bg-white/[0.02] border-t border-white/10"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <Section><Benefits /></Section>
            </motion.div>
            
            <motion.div
              className="backdrop-blur-[35px] bg-white/[0.015] border-t border-white/08"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Section><ServicesGrid /></Section>
            </motion.div>
            
            <motion.div
              className="backdrop-blur-[30px] bg-white/[0.02] border-t border-white/10"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <Section><Features /></Section>
            </motion.div>
            
            <motion.div
              className="backdrop-blur-[35px] bg-white/[0.015] border-t border-white/08"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Section><Process /></Section>
            </motion.div>
            
            <motion.div
              className="backdrop-blur-[30px] bg-white/[0.02] border-t border-white/10"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <Section><Testimonials /></Section>
            </motion.div>
            
            <motion.div
              className="backdrop-blur-[40px] bg-white/[0.025] border-t border-white/15"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              <Section><CallToAction /></Section>
            </motion.div>
            
            <Footer />
          </main>
        </div>
      </div>
    </AnimatedBackground>
  );
}
