import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Sparkles, Calendar, CheckCircle, 
  Shield, Briefcase, Clock, MapPin,
  Users, Heart, ArrowLeft, ArrowRight,
  ShoppingBag, Zap, Home, Factory,
  ChevronDown, User, Mail, Phone, MapPin as LocationIcon
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { useAuth } from '../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../components/PaymentOptionsModal';
import { AnimatedBackground } from '../../../components/layout/AnimatedBackground';

interface WindowCleaningFormData {
  servicePackage: string;
  buildingType: string;
  windowType: string;
  accessType: string;
  floors: string;
  frequency: string;
  windowCount: string;
  specialServices: string[];
  preferredDate: string;
  preferredTime: string;
  urgency: string;
  specialInstructions: string;
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedWindowForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [errors, setErrors] = useState<Partial<WindowCleaningFormData>>({});

  const [formData, setFormData] = useState<WindowCleaningFormData>({
    servicePackage: '',
    buildingType: '',
    windowType: '',
    accessType: '',
    floors: '',
    frequency: '',
    windowCount: '',
    specialServices: [],
    preferredDate: '',
    preferredTime: '',
    urgency: '',
    specialInstructions: '',
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
  });

  const servicePackages = [
    { id: 'standard', name: 'Standard Clean', icon: <Sparkles className="w-6 h-6" />, description: 'Basic interior and exterior cleaning' },
    { id: 'premium', name: 'Premium Clean', icon: <Shield className="w-6 h-6" />, description: 'Deep clean with screen removal' },
    { id: 'high-rise', name: 'High-Rise Specialist', icon: <Building className="w-6 h-6" />, description: 'Specialized high-rise equipment' },
    { id: 'storefront', name: 'Storefront Clean', icon: <ShoppingBag className="w-6 h-6" />, description: 'Customer-facing window cleaning' },
  ];

  const buildingTypes = [
    { id: 'office', name: 'Office Building', description: 'Corporate and business facilities' },
    { id: 'retail', name: 'Retail Store', description: 'Shops and commercial spaces' },
    { id: 'medical', name: 'Medical Facility', description: 'Hospitals and clinics' },
    { id: 'warehouse', name: 'Warehouse/Industrial', description: 'Storage and manufacturing' },
  ];

  const windowTypes = [
    { id: 'standard', name: 'Standard Windows', description: 'Regular business windows' },
    { id: 'floor-ceiling', name: 'Floor-to-Ceiling', description: 'Large glass panels' },
    { id: 'storefront', name: 'Storefront Glass', description: 'Display windows' },
    { id: 'skylights', name: 'Skylights', description: 'Overhead glass panels' },
  ];

  const accessTypes = [
    { id: 'ground', name: 'Ground Level', description: 'Accessible from ground' },
    { id: 'ladder', name: 'Ladder Access', description: 'Requires ladder work' },
    { id: 'lift', name: 'Lift Required', description: 'Scissor or boom lift' },
    { id: 'rappelling', name: 'Rope Access', description: 'Specialized rope techniques' },
  ];

  const floorOptions = [
    { id: 'ground', name: 'Ground Floor Only' },
    { id: '2-5', name: '2-5 Floors' },
    { id: '6-10', name: '6-10 Floors' },
    { id: '11+', name: '11+ Floors' },
  ];

  const frequencies = [
    { id: 'one-time', name: 'One-Time Service' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly' },
    { id: 'quarterly', name: 'Quarterly' },
  ];

  const windowCounts = [
    { id: 'small', name: '1-20 Windows', description: 'Small office or retail' },
    { id: 'medium', name: '21-50 Windows', description: 'Medium building' },
    { id: 'large', name: '51-100 Windows', description: 'Large facility' },
    { id: 'xl', name: '100+ Windows', description: 'High-rise or complex' },
  ];

  const specialServices = [
    { id: 'water-fed', name: 'Water-Fed Pole System', description: 'Purified water cleaning', price: 75, icon: <Sparkles className="w-5 h-5" /> },
    { id: 'screen-cleaning', name: 'Screen Cleaning', description: 'Remove and clean screens', price: 100, icon: <Shield className="w-5 h-5" /> },
    { id: 'hard-water', name: 'Hard Water Removal', description: 'Mineral deposit removal', price: 150, icon: <Zap className="w-5 h-5" /> },
    { id: 'pressure-wash', name: 'Pressure Washing', description: 'Building exterior cleaning', price: 200, icon: <Factory className="w-5 h-5" /> },
  ];

  const timeSlots = [
    { id: 'early-morning', name: 'Early Morning (6-9 AM)', description: 'Before business hours' },
    { id: 'morning', name: 'Morning (9 AM - 12 PM)', description: 'Regular business hours' },
    { id: 'afternoon', name: 'Afternoon (12-5 PM)', description: 'Regular business hours' },
    { id: 'evening', name: 'Evening (5-8 PM)', description: 'After business hours' },
  ];

  const calculatePrice = () => {
    let basePrice = 0;
    
    // Base price by service package
    if (formData.servicePackage === 'standard') basePrice = 150;
    else if (formData.servicePackage === 'premium') basePrice = 300;
    else if (formData.servicePackage === 'high-rise') basePrice = 500;
    else if (formData.servicePackage === 'storefront') basePrice = 200;
    
    // Window count multiplier
    const windowMultiplier = formData.windowCount === 'small' ? 1 : 
                           formData.windowCount === 'medium' ? 1.5 : 
                           formData.windowCount === 'large' ? 2.5 : 
                           formData.windowCount === 'xl' ? 4 : 1;
    
    // Floor multiplier
    const floorMultiplier = formData.floors === 'ground' ? 1 : 
                           formData.floors === '2-5' ? 1.3 : 
                           formData.floors === '6-10' ? 1.8 : 
                           formData.floors === '11+' ? 2.5 : 1;
    
    const specialServicesPrice = formData.specialServices.length * 50;
    return Math.round(basePrice * windowMultiplier * floorMultiplier + specialServicesPrice);
  };

  const isStepValid = (step: number) => {
    switch (step) {
      case 0: return !!formData.servicePackage && !!formData.buildingType;
      case 1: return !!formData.windowType && !!formData.accessType && !!formData.floors;
      case 2: return !!formData.frequency && !!formData.windowCount;
      case 3: return true; // Special services are optional
      case 4: return !!formData.preferredDate && !!formData.preferredTime;
      case 5: return !!formData.companyName && !!formData.contactName && !!formData.email && !!formData.phone && !!formData.address && !!formData.city && !!formData.zipCode;
      default: return false;
    }
  };

  const handleNext = () => {
    if (isStepValid(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 5));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const handleSubmit = async () => {
    if (!isStepValid(5)) return;
    
    if (!user) {
      navigate('/auth/login', { state: { from: '/commercial/window-cleaning' } });
      return;
    }

    setShowPaymentModal(true);
  };

  const steps = [
    { title: 'Service Type', subtitle: 'Choose your service package', icon: <Sparkles className="w-6 h-6" /> },
    { title: 'Window Details', subtitle: 'Tell us about your windows', icon: <Building className="w-6 h-6" /> },
    { title: 'Scope & Frequency', subtitle: 'Service requirements', icon: <Calendar className="w-6 h-6" /> },
    { title: 'Special Services', subtitle: 'Optional upgrades', icon: <Shield className="w-6 h-6" /> },
    { title: 'Schedule', subtitle: 'Choose date and time', icon: <Clock className="w-6 h-6" /> },
    { title: 'Contact Info', subtitle: 'Finalize your booking', icon: <CheckCircle className="w-6 h-6" /> }
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      <AnimatedBackground />
      
      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Commercial Window Cleaning
          </h1>
          <p className="text-xl text-white/80">
            Professional window cleaning services for your business
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-center mb-4">
            <div className="flex space-x-2">
              {steps.map((step, index) => (
                <div
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index <= currentStep 
                      ? 'bg-green-400 shadow-lg shadow-green-400/50' 
                      : 'bg-white/20'
                  }`}
                />
              ))}
            </div>
          </div>
          <div className="text-center">
            <p className="text-white/80 text-sm mb-1">
              Step {currentStep + 1} of {steps.length}
            </p>
            <p className="text-white font-medium text-lg">
              {steps[currentStep].title}
            </p>
            <p className="text-white/70 text-sm">
              {steps[currentStep].subtitle}
            </p>
          </div>
        </div>

        {/* Form Container */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
            <AnimatePresence mode="wait">
              
              {/* Step 0: Service Type */}
              {currentStep === 0 && (
                <motion.div
                  key="step0"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-white mb-2">Service Package</h2>
                    <p className="text-white/80">Select the service package that best fits your needs</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {servicePackages.map((pkg) => (
                      <motion.button
                        key={pkg.id}
                        onClick={() => setFormData({...formData, servicePackage: pkg.id})}
                        className={`p-6 rounded-2xl border-2 transition-all duration-300 text-left ${
                          formData.servicePackage === pkg.id
                            ? 'border-green-400 bg-green-400/10 shadow-lg shadow-green-400/20'
                            : 'border-white/20 bg-white/5 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="text-green-400">{pkg.icon}</div>
                          <h3 className="text-lg font-semibold text-white">{pkg.name}</h3>
                        </div>
                        <p className="text-white/70 text-sm">{pkg.description}</p>
                      </motion.button>
                    ))}
                  </div>

                  <div className="mt-8">
                    <h3 className="text-lg font-semibold text-white mb-4">Building Type</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {buildingTypes.map((type) => (
                        <motion.button
                          key={type.id}
                          onClick={() => setFormData({...formData, buildingType: type.id})}
                          className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                            formData.buildingType === type.id
                              ? 'border-green-400 bg-green-400/10'
                              : 'border-white/20 bg-white/5 hover:border-white/40'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <h4 className="text-white font-medium">{type.name}</h4>
                          <p className="text-white/70 text-sm">{type.description}</p>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 1: Window Details */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-white mb-2">Window Details</h2>
                    <p className="text-white/80">Tell us about your windows and building</p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Window Type</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {windowTypes.map((type) => (
                          <motion.button
                            key={type.id}
                            onClick={() => setFormData({...formData, windowType: type.id})}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                              formData.windowType === type.id
                                ? 'border-green-400 bg-green-400/10'
                                : 'border-white/20 bg-white/5 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <h4 className="text-white font-medium">{type.name}</h4>
                            <p className="text-white/70 text-sm">{type.description}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Access Type</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {accessTypes.map((type) => (
                          <motion.button
                            key={type.id}
                            onClick={() => setFormData({...formData, accessType: type.id})}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                              formData.accessType === type.id
                                ? 'border-green-400 bg-green-400/10'
                                : 'border-white/20 bg-white/5 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <h4 className="text-white font-medium">{type.name}</h4>
                            <p className="text-white/70 text-sm">{type.description}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Building Floors</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {floorOptions.map((floor) => (
                          <motion.button
                            key={floor.id}
                            onClick={() => setFormData({...formData, floors: floor.id})}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                              formData.floors === floor.id
                                ? 'border-green-400 bg-green-400/10'
                                : 'border-white/20 bg-white/5 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <p className="text-white font-medium">{floor.name}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Scope & Frequency */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-white mb-2">Scope & Frequency</h2>
                    <p className="text-white/80">How often do you need service and how many windows?</p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Service Frequency</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {frequencies.map((freq) => (
                          <motion.button
                            key={freq.id}
                            onClick={() => setFormData({...formData, frequency: freq.id})}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-center ${
                              formData.frequency === freq.id
                                ? 'border-green-400 bg-green-400/10'
                                : 'border-white/20 bg-white/5 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <p className="text-white font-medium">{freq.name}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Window Count</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {windowCounts.map((count) => (
                          <motion.button
                            key={count.id}
                            onClick={() => setFormData({...formData, windowCount: count.id})}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                              formData.windowCount === count.id
                                ? 'border-green-400 bg-green-400/10'
                                : 'border-white/20 bg-white/5 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <h4 className="text-white font-medium">{count.name}</h4>
                            <p className="text-white/70 text-sm">{count.description}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Special Services */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-white mb-2">Special Services</h2>
                    <p className="text-white/80">Optional upgrades and specialized services</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {specialServices.map((service) => (
                      <motion.button
                        key={service.id}
                        onClick={() => {
                          const newServices = formData.specialServices.includes(service.id)
                            ? formData.specialServices.filter(s => s !== service.id)
                            : [...formData.specialServices, service.id];
                          setFormData({...formData, specialServices: newServices});
                        }}
                        className={`p-6 rounded-xl border-2 transition-all duration-300 text-left ${
                          formData.specialServices.includes(service.id)
                            ? 'border-green-400 bg-green-400/10'
                            : 'border-white/20 bg-white/5 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="text-green-400">{service.icon}</div>
                          <h3 className="text-lg font-semibold text-white">{service.name}</h3>
                          <span className="text-green-400 font-medium ml-auto">+${service.price}</span>
                        </div>
                        <p className="text-white/70 text-sm">{service.description}</p>
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Step 4: Schedule */}
              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-white mb-2">Schedule Service</h2>
                    <p className="text-white/80">Choose your preferred date and time</p>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-white font-medium mb-2">
                        Preferred Date
                      </label>
                      <input
                        type="date"
                        value={formData.preferredDate}
                        onChange={(e) => setFormData({...formData, preferredDate: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Preferred Time
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {timeSlots.map((slot) => (
                          <motion.button
                            key={slot.id}
                            onClick={() => setFormData({...formData, preferredTime: slot.id})}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left ${
                              formData.preferredTime === slot.id
                                ? 'border-green-400 bg-green-400/10'
                                : 'border-white/20 bg-white/5 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <h4 className="text-white font-medium">{slot.name}</h4>
                            <p className="text-white/70 text-sm">{slot.description}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        Special Instructions (Optional)
                      </label>
                      <textarea
                        value={formData.specialInstructions}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none resize-none"
                        rows={4}
                        placeholder="Any specific requirements or instructions..."
                      />
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 5: Contact Info */}
              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
                    <p className="text-white/80">Complete your booking with contact details</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-white font-medium mb-2">
                        <User className="inline w-4 h-4 mr-2" />
                        Company Name
                      </label>
                      <input
                        type="text"
                        value={formData.companyName}
                        onChange={(e) => setFormData({...formData, companyName: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        placeholder="Your company name"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        <User className="inline w-4 h-4 mr-2" />
                        Contact Name
                      </label>
                      <input
                        type="text"
                        value={formData.contactName}
                        onChange={(e) => setFormData({...formData, contactName: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        placeholder="Your full name"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        <Mail className="inline w-4 h-4 mr-2" />
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        <Phone className="inline w-4 h-4 mr-2" />
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        placeholder="(*************"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-white font-medium mb-2">
                        <LocationIcon className="inline w-4 h-4 mr-2" />
                        Address
                      </label>
                      <input
                        type="text"
                        value={formData.address}
                        onChange={(e) => setFormData({...formData, address: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        placeholder="Street address"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        City
                      </label>
                      <input
                        type="text"
                        value={formData.city}
                        onChange={(e) => setFormData({...formData, city: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        placeholder="City"
                      />
                    </div>

                    <div>
                      <label className="block text-white font-medium mb-2">
                        ZIP Code
                      </label>
                      <input
                        type="text"
                        value={formData.zipCode}
                        onChange={(e) => setFormData({...formData, zipCode: e.target.value})}
                        className="w-full p-4 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:border-green-400 focus:outline-none"
                        placeholder="ZIP Code"
                      />
                    </div>
                  </div>
                </motion.div>
              )}

            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <motion.button
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  currentStep === 0
                    ? 'bg-white/10 text-white/40 cursor-not-allowed'
                    : 'bg-white/20 text-white hover:bg-white/30'
                }`}
                whileHover={currentStep !== 0 ? { scale: 1.05 } : {}}
                whileTap={currentStep !== 0 ? { scale: 0.95 } : {}}
              >
                <ArrowLeft className="w-5 h-5" />
                <span>Previous</span>
              </motion.button>

              <div className="flex space-x-4">
                {currentStep < 5 ? (
                  <motion.button
                    onClick={handleNext}
                    disabled={!isStepValid(currentStep)}
                    className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                      isStepValid(currentStep)
                        ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25 hover:shadow-green-500/40'
                        : 'bg-white/10 text-white/40 cursor-not-allowed'
                    }`}
                    whileHover={isStepValid(currentStep) ? { scale: 1.05 } : {}}
                    whileTap={isStepValid(currentStep) ? { scale: 0.95 } : {}}
                  >
                    <span>Next</span>
                    <ArrowRight className="w-5 h-5" />
                  </motion.button>
                ) : (
                  <motion.button
                    onClick={handleSubmit}
                    disabled={!isStepValid(5) || isSubmitting}
                    className={`flex items-center space-x-2 px-8 py-3 rounded-xl font-medium transition-all duration-300 ${
                      isStepValid(5) && !isSubmitting
                        ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25 hover:shadow-green-500/40'
                        : 'bg-white/10 text-white/40 cursor-not-allowed'
                    }`}
                    whileHover={isStepValid(5) && !isSubmitting ? { scale: 1.05 } : {}}
                    whileTap={isStepValid(5) && !isSubmitting ? { scale: 0.95 } : {}}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        <span>Processing...</span>
                      </>
                    ) : (
                      <>
                        <span>Get Quote</span>
                        <CheckCircle className="w-5 h-5" />
                      </>
                    )}
                  </motion.button>
                )}
              </div>
            </div>
          </div>

          {/* Quote Summary */}
          <div className="mt-8 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl">
            <div className="text-center">
              <h3 className="text-xl font-bold text-white mb-2">Estimated Quote</h3>
              <div className="text-4xl font-bold text-green-400 mb-2">
                ${calculatePrice()}
              </div>
              <p className="text-white/70 text-sm">
                Final price subject to site inspection
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description="Commercial Window Cleaning Service"
          formData={formData}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedWindowForm; 
