import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { ProgressBar } from '../../../../components/forms/ProgressBar';
import { ServiceTypeSelector } from './components/ServiceTypeSelector';
import { PropertyDetails } from './components/PropertyDetails';
import { CarpetDetails } from './components/CarpetDetails';
import { StainTreatment } from './components/StainTreatment';
import { ServiceScheduling } from './components/ServiceScheduling';
import { ContactInfo } from './components/ContactInfo';
import { SlideTransition } from '../../../../components/animations/SlideTransition';
import { useFormValidation } from '../../../../lib/hooks/useFormValidation';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { submitBookingForm } from '../../../../lib/api/bookingForms';
import { steps, initialFormData } from './types';
import type { FormData } from './types';

interface CarpetCleaningFormProps {
  onBack: () => void;
}

export function CarpetCleaningForm({ onBack }: CarpetCleaningFormProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, loading, saveFormData } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { validateStep } = useFormValidation();

  const handleNext = async () => {
    const validation = validateStep(currentStep, formData);
    
    if (!validation.isValid) {
      setValidationError(validation.errors.join('\n'));
      return;
    }

    setValidationError(null);

    if (currentStep === steps.length - 1) {
      await handleSubmit();
    } else {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
    }
  };

  const handlePrev = () => {
    if (currentStep === 0) {
      onBack();
    } else {
      setCurrentStep((prev) => Math.max(prev - 1, 0));
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setValidationError(null);

      if (!user) {
        // Save form data and redirect to login/signup
        saveFormData(formData);
        navigate('/auth/login', { 
          state: { from: location.pathname }
        });
        return;
      }

      await submitBookingForm(formData, 'residential-carpet', user);
      navigate('/thank-you', { state: { formData } });
    } catch (err) {
      console.error('Error submitting form:', err);
      setValidationError(
        err instanceof Error ? err.message : 'Failed to submit request'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <ServiceTypeSelector
            selected={formData.serviceType}
            onChange={(type) => setFormData({ ...formData, serviceType: type })}
          />
        );
      case 1:
        return (
          <PropertyDetails
            details={formData.propertyDetails}
            onChange={(details) => setFormData({ ...formData, propertyDetails: details })}
          />
        );
      case 2:
        return (
          <CarpetDetails
            details={formData.carpetDetails}
            onChange={(details) => setFormData({ ...formData, carpetDetails: details })}
          />
        );
      case 3:
        return (
          <StainTreatment
            treatment={formData.stainTreatment}
            onChange={(treatment) => setFormData({ ...formData, stainTreatment: treatment })}
          />
        );
      case 4:
        return (
          <ServiceScheduling
            schedule={formData.schedule}
            onChange={(schedule) => setFormData({ ...formData, schedule: schedule })}
          />
        );
      case 5:
        return (
          <ContactInfo
            contact={formData.contact}
            onChange={(contact) => setFormData({ ...formData, contact: contact })}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Progress Bar */}
          <ProgressBar steps={steps} currentStep={currentStep} color="#93C572" />

          {/* Validation Error */}
          {validationError && (
            <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" />
                <div className="text-red-600 whitespace-pre-line">
                  {validationError}
                </div>
              </div>
            </div>
          )}

          {/* Form Content */}
          <div className="mt-12 mb-12">
            <SlideTransition>
              <div className="transition-all duration-300 ease-in-out">
                {renderStep()}
              </div>
            </SlideTransition>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-between pt-8 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={handlePrev}
              disabled={isSubmitting}
              size="lg"
              className="min-w-[120px]"
            >
              {currentStep === 0 ? 'Cancel' : 'Back'}
            </Button>
            
            <Button
              onClick={handleNext}
              disabled={isSubmitting}
              size="lg"
              className="min-w-[120px]"
            >
              {currentStep === steps.length - 1 ? 'Submit Request' : 'Continue'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
