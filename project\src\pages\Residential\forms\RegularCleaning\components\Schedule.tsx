import React from 'react';
import { Calendar, Clock, KeyRound } from 'lucide-react';

interface ScheduleProps {
  schedule: {
    date: string;
    timeSlot: string;
    accessInstructions: string;
  };
  onChange: (schedule: any) => void;
}

export function Schedule({ schedule, onChange }: ScheduleProps) {
  const today = new Date().toISOString().split('T')[0];
  
  const timeSlots = [
    'Morning (8AM - 12PM)',
    'Afternoon (12PM - 4PM)',
    'Evening (4PM - 8PM)'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Calendar className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Schedule Service</h3>
          <p className="text-gray-600">Choose your preferred date and time</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Preferred Date <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="date"
              value={schedule.date}
              onChange={(e) => onChange({ ...schedule, date: e.target.value })}
              min={today}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Preferred Time <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={schedule.timeSlot}
              onChange={(e) => onChange({ ...schedule, timeSlot: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select time slot</option>
              {timeSlots.map((slot) => (
                <option key={slot} value={slot}>{slot}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Access Instructions
        </label>
        <div className="relative">
          <KeyRound className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
          <textarea
            value={schedule.accessInstructions}
            onChange={(e) => onChange({ ...schedule, accessInstructions: e.target.value })}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            rows={3}
            placeholder="Any special instructions for accessing the property?"
          />
        </div>
      </div>
    </div>
  );
}
