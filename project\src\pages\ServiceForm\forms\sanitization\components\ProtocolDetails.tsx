import React from 'react';
import { <PERSON>, <PERSON><PERSON>he<PERSON>, Repeat, AlertTriangle } from 'lucide-react';

interface ProtocolDetailsProps {
  details: {
    sanitizationLevel: string;
    specialRequirements: string[];
    certificationNeeded: boolean;
    frequency: string;
  };
  onChange: (details: any) => void;
}

export function ProtocolDetails({ details, onChange }: ProtocolDetailsProps) {
  const sanitizationLevels = [
    {
      id: 'standard',
      label: 'Standard Sanitization',
      description: 'General-purpose cleaning and sanitization'
    },
    {
      id: 'advanced',
      label: 'Advanced Disinfection',
      description: 'Enhanced protocols with hospital-grade disinfectants'
    },
    {
      id: 'medical',
      label: 'Medical-Grade',
      description: 'Highest level of sanitization for healthcare facilities'
    }
  ];

  const requirements = [
    'EPA-Registered Products',
    'Electrostatic Spraying',
    'UV-C Treatment',
    'HVAC Sanitization',
    'ATP Testing',
    'Documentation/Reporting',
    'Staff Training Records',
    'Safety Data Sheets'
  ];

  const frequencies = [
    'Daily',
    'Weekly',
    'Bi-Weekly',
    'Monthly',
    'Quarterly',
    'Custom Schedule'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Shield className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Protocol Details</h3>
          <p className="text-gray-600">Define sanitization protocols and requirements</p>
        </div>
      </div>

      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Sanitization Level <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {sanitizationLevels.map((level) => (
              <label
                key={level.id}
                className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  details.sanitizationLevel === level.id
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="radio"
                  name="sanitizationLevel"
                  value={level.id}
                  checked={details.sanitizationLevel === level.id}
                  onChange={(e) => onChange({ ...details, sanitizationLevel: e.target.value })}
                  className="sr-only"
                />
                <div className="font-medium text-gray-900 mb-1">{level.label}</div>
                <div className="text-sm text-gray-600">{level.description}</div>
              </label>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            Special Requirements
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {requirements.map((req) => (
              <label
                key={req}
                className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  details.specialRequirements.includes(req)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.specialRequirements.includes(req)}
                  onChange={(e) => {
                    const newReqs = e.target.checked
                      ? [...details.specialRequirements, req]
                      : details.specialRequirements.filter(r => r !== req);
                    onChange({ ...details, specialRequirements: newReqs });
                  }}
                  className="sr-only"
                />
                <FileCheck className={`w-5 h-5 mr-3 ${
                  details.specialRequirements.includes(req) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-gray-700">{req}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Service Frequency <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <Repeat className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={details.frequency}
                onChange={(e) => onChange({ ...details, frequency: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                required
              >
                <option value="">Select frequency</option>
                {frequencies.map((freq) => (
                  <option key={freq} value={freq}>{freq}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="space-y-4">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={details.certificationNeeded}
                onChange={(e) => onChange({ ...details, certificationNeeded: e.target.checked })}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <div className="flex items-center">
                <AlertTriangle className="w-5 h-5 text-brand-600 mr-2" />
                <span className="text-gray-700">Certification Documentation Required</span>
              </div>
            </label>
            {details.certificationNeeded && (
              <p className="text-sm text-gray-500 pl-8">
                We will provide all necessary certification documentation upon completion
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
