import React from 'react';
import { motion } from 'framer-motion';

interface ServiceHeaderProps {
  image: string;
  title: string;
  description: string;
}

export function ServiceHeader({ image, title, description }: ServiceHeaderProps) {
  return (
    <div className="relative h-[300px] rounded-2xl overflow-hidden shadow-xl">
      <img
        src="https://images.unsplash.com/photo-1558317374-067fb5f30001?ixlib=rb-1.2.1&auto=format&fit=crop&w=2850&q=80"
        alt="Professional carpet cleaning"
        className="absolute inset-0 w-full h-full object-cover"
      />
      <div className="absolute inset-0 bg-gradient-to-r from-brand-900/90 to-brand-800/80" />
      
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative h-full flex flex-col justify-center px-8 md:px-12"
      >
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
          {title}
        </h1>
        <p className="text-lg text-brand-50 max-w-xl">
          {description}
        </p>
      </motion.div>
    </div>
  );
}
