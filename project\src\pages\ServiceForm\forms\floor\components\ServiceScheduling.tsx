import React from 'react';
import { Calendar, Clock, AlertTriangle } from 'lucide-react';

interface ServiceSchedulingProps {
  schedule: {
    date: string;
    timeSlot: string;
    frequency: string;
  };
  onChange: (schedule: any) => void;
}

export function ServiceScheduling({ schedule, onChange }: ServiceSchedulingProps) {
  const today = new Date().toISOString().split('T')[0];
  
  const timeSlots = [
    { value: 'early-morning', label: 'Early Morning (5AM - 8AM)' },
    { value: 'morning', label: 'Morning (8AM - 12PM)' },
    { value: 'afternoon', label: 'Afternoon (12PM - 4PM)' },
    { value: 'evening', label: 'Evening (4PM - 8PM)' },
    { value: 'night', label: 'Night (8PM - 12AM)' },
    { value: 'after-hours', label: 'After Hours (12AM - 5AM)' }
  ];

  const frequencies = [
    { value: 'one-time', label: 'One-Time Service' },
    { value: 'monthly', label: 'Monthly Maintenance' },
    { value: 'quarterly', label: 'Quarterly Maintenance' },
    { value: 'semi-annual', label: 'Semi-Annual Maintenance' },
    { value: 'annual', label: 'Annual Maintenance' }
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Calendar className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Schedule Service</h3>
          <p className="text-gray-600">Choose your preferred date and time</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Preferred Date <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="date"
              value={schedule.date}
              onChange={(e) => onChange({ ...schedule, date: e.target.value })}
              min={today}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Preferred Time <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={schedule.timeSlot}
              onChange={(e) => onChange({ ...schedule, timeSlot: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select time slot</option>
              {timeSlots.map((slot) => (
                <option key={slot.value} value={slot.value}>
                  {slot.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Maintenance Frequency
        </label>
        <select
          value={schedule.frequency}
          onChange={(e) => onChange({ ...schedule, frequency: e.target.value })}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
        >
          {frequencies.map((freq) => (
            <option key={freq.value} value={freq.value}>
              {freq.label}
            </option>
          ))}
        </select>
      </div>

      <div className="p-4 bg-brand-50 rounded-lg">
        <div className="flex items-start">
          <AlertTriangle className="w-5 h-5 text-brand-600 mr-2 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-brand-900">Service Duration Notice</h4>
            <p className="mt-1 text-sm text-brand-700">
              Floor restoration may require multiple days depending on the scope of work. Our team will provide a detailed timeline during the assessment.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
