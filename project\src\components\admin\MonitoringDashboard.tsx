/**
 * Production Monitoring Dashboard
 * 
 * Real-time dashboard for monitoring system health, performance,
 * and business metrics in production environment.
 */

import React, { useState, useEffect } from 'react';
import { useProductionMonitor } from '../../lib/monitoring/productionMonitor';

interface DashboardProps {
  refreshInterval?: number;
}

export const MonitoringDashboard: React.FC<DashboardProps> = ({ 
  refreshInterval = 30000 
}) => {
  const { getDashboard, getHealth } = useProductionMonitor();
  const [dashboardData, setDashboardData] = useState(getDashboard());
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  useEffect(() => {
    const interval = setInterval(() => {
      setIsLoading(true);
      const newData = getDashboard();
      setDashboardData(newData);
      setLastUpdated(new Date());
      setIsLoading(false);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval, getDashboard]);

  const { health, recentMetrics, recentLogs, alerts } = dashboardData;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'degraded': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">
              Production Monitoring Dashboard
            </h1>
            <div className="flex items-center space-x-4">
              {isLoading && (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              )}
              <span className="text-sm text-gray-500">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            </div>
          </div>
        </div>

        {/* System Health Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(health.status)}`}>
                {health.status.toUpperCase()}
              </div>
            </div>
            <div className="mt-2">
              <h3 className="text-lg font-medium text-gray-900">System Status</h3>
              <p className="text-sm text-gray-500">
                Uptime: {formatDuration(health.uptime)}
              </p>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl font-bold text-blue-600">
                  {health.responseTime}ms
                </div>
              </div>
            </div>
            <div className="mt-2">
              <h3 className="text-lg font-medium text-gray-900">Response Time</h3>
              <p className="text-sm text-gray-500">Average database response</p>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl font-bold text-green-600">
                  {health.paymentSuccessRate.toFixed(1)}%
                </div>
              </div>
            </div>
            <div className="mt-2">
              <h3 className="text-lg font-medium text-gray-900">Payment Success</h3>
              <p className="text-sm text-gray-500">Last hour success rate</p>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="text-2xl font-bold text-purple-600">
                  {health.activeUsers}
                </div>
              </div>
            </div>
            <div className="mt-2">
              <h3 className="text-lg font-medium text-gray-900">Active Users</h3>
              <p className="text-sm text-gray-500">Last 15 minutes</p>
            </div>
          </div>
        </div>

        {/* Business Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Business Metrics</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">Pending Bookings</span>
                  <span className="text-lg font-semibold text-gray-900">{health.pendingBookings}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">Error Rate</span>
                  <span className={`text-lg font-semibold ${health.errorRate > 5 ? 'text-red-600' : 'text-green-600'}`}>
                    {health.errorRate.toFixed(2)}%
                  </span>
                </div>
                {Object.entries(recentMetrics).slice(0, 5).map(([metricName, metrics]) => (
                  <div key={metricName} className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-500 truncate">
                      {metricName.replace(/\./g, ' ').replace(/_/g, ' ')}
                    </span>
                    <span className="text-lg font-semibold text-gray-900">
                      {formatNumber(metrics.reduce((sum, m) => sum + m.value, 0))}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Active Alerts</h3>
            </div>
            <div className="p-6">
              {alerts.length === 0 ? (
                <p className="text-sm text-gray-500">No active alerts</p>
              ) : (
                <div className="space-y-3">
                  {alerts.map((alert) => (
                    <div key={alert.id} className={`p-3 rounded-md ${
                      alert.severity === 'critical' ? 'bg-red-50 border border-red-200' :
                      alert.severity === 'high' ? 'bg-orange-50 border border-orange-200' :
                      alert.severity === 'medium' ? 'bg-yellow-50 border border-yellow-200' :
                      'bg-blue-50 border border-blue-200'
                    }`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">{alert.name}</h4>
                          <p className="text-xs text-gray-500 mt-1">{alert.condition}</p>
                        </div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          alert.severity === 'critical' ? 'bg-red-100 text-red-800' :
                          alert.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                          alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {alert.severity}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Recent Logs */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
          </div>
          <div className="overflow-hidden">
            <div className="max-h-96 overflow-y-auto">
              {recentLogs.length === 0 ? (
                <p className="p-6 text-sm text-gray-500">No recent activity</p>
              ) : (
                <div className="divide-y divide-gray-200">
                  {recentLogs.slice(0, 20).map((log) => (
                    <div key={log.id} className="p-4 hover:bg-gray-50">
                      <div className="flex items-start space-x-3">
                        <div className={`flex-shrink-0 w-2 h-2 mt-2 rounded-full ${
                          log.level === 'error' || log.level === 'critical' ? 'bg-red-500' :
                          log.level === 'warn' ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`}></div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {log.message}
                            </p>
                            <div className="flex items-center space-x-2">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                log.category === 'business' ? 'bg-blue-100 text-blue-800' :
                                log.category === 'security' ? 'bg-red-100 text-red-800' :
                                log.category === 'performance' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {log.category}
                              </span>
                              <span className="text-xs text-gray-500">
                                {new Date(log.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                          </div>
                          {Object.keys(log.context).length > 0 && (
                            <p className="text-xs text-gray-500 mt-1 truncate">
                              {JSON.stringify(log.context)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MonitoringDashboard;

