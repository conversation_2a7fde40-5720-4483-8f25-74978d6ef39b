import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Trash2, Recycle, Building, Factory, Phone, Mail, User,
  ArrowRight, CheckCircle, AlertCircle, Package
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';

interface WasteFormData {
  servicePackage: string;
  facilityType: string;
  wasteTypes: string[];
  wasteVolume: string;
  containerSizes: string[];
  pickupFrequency: string;
  specialHandling: string[];
  propertyAddress: string;
  accessInstructions: string;
  hazardousMaterials: boolean;
  recyclingRequirements: string[];
  complianceNeeds: string[];
  additionalServices: string[];
  specialInstructions: string;
  // Contact information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName: string;
  jobTitle: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  propertyAddress?: string;
  facilityType?: string;
  wasteVolume?: string;
  pickupFrequency?: string;
}

const steps = [
  { id: 1, name: 'Service Type' },
  { id: 2, name: 'Facility Info' },
  { id: 3, name: 'Waste Details' },
  { id: 4, name: 'Schedule' },
  { id: 5, name: 'Requirements' },
  { id: 6, name: 'Contact' },
];

const servicePackages = [
  { id: 'standard', name: 'Standard Waste Collection', description: 'Regular waste pickup and disposal', icon: <Trash2 /> },
  { id: 'recycling', name: 'Recycling Program', description: 'Comprehensive recycling and waste diversion', icon: <Recycle /> },
  { id: 'medical', name: 'Medical Waste Management', description: 'Specialized medical waste disposal', icon: <Package /> },
  { id: 'construction', name: 'Construction Debris', description: 'Construction and demolition waste removal', icon: <Building /> },
  { id: 'industrial', name: 'Industrial Waste', description: 'Heavy-duty industrial waste management', icon: <Factory /> },
];

const facilityTypes = [
  { id: 'office', name: 'Office Building' },
  { id: 'retail', name: 'Retail/Shopping Center' },
  { id: 'restaurant', name: 'Restaurant/Food Service' },
  { id: 'medical', name: 'Medical Facility' },
  { id: 'manufacturing', name: 'Manufacturing Plant' },
  { id: 'warehouse', name: 'Warehouse/Distribution' },
  { id: 'construction', name: 'Construction Site' },
  { id: 'educational', name: 'School/University' },
  { id: 'government', name: 'Government Facility' },
];

const wasteTypes = [
  { id: 'general', name: 'General Waste' },
  { id: 'recyclables', name: 'Recyclables (Paper, Plastic, Metal)' },
  { id: 'organic', name: 'Organic/Food Waste' },
  { id: 'cardboard', name: 'Cardboard/Packaging' },
  { id: 'electronic', name: 'Electronic Waste' },
  { id: 'medical', name: 'Medical Waste' },
  { id: 'hazardous', name: 'Hazardous Materials' },
  { id: 'construction', name: 'Construction Debris' },
  { id: 'confidential', name: 'Confidential Documents' },
];

/*
const containerSizes = [
  { id: '2-yard', name: '2 Yard Dumpster' },
  { id: '4-yard', name: '4 Yard Dumpster' },
  { id: '6-yard', name: '6 Yard Dumpster' },
  { id: '8-yard', name: '8 Yard Dumpster' },
  { id: '10-yard', name: '10 Yard Dumpster' },
  { id: '20-yard', name: '20 Yard Roll-off' },
  { id: '30-yard', name: '30 Yard Roll-off' },
  { id: '40-yard', name: '40 Yard Roll-off' },
];
*/

const pickupFrequencies = [
  { id: 'daily', name: 'Daily Pickup' },
  { id: 'twice-weekly', name: 'Twice Weekly' },
  { id: 'weekly', name: 'Weekly Pickup' },
  { id: 'bi-weekly', name: 'Bi-Weekly' },
  { id: 'monthly', name: 'Monthly' },
  { id: 'on-call', name: 'On-Call/As Needed' },
];

const wasteVolumes = [
  { id: 'low', name: 'Low (1-5 bags/week)' },
  { id: 'medium', name: 'Medium (6-15 bags/week)' },
  { id: 'high', name: 'High (16-30 bags/week)' },
  { id: 'very-high', name: 'Very High (30+ bags/week)' },
];

/*
const specialHandlingOptions = [
  { id: 'locked-container', name: 'Locked/Secure Containers' },
  { id: 'scheduled-access', name: 'Scheduled Access Times' },
  { id: 'witnessed-disposal', name: 'Witnessed Disposal' },
  { id: 'documentation', name: 'Chain of Custody Documentation' },
  { id: 'sorting', name: 'On-site Sorting' },
];

const recyclingRequirements = [
  { id: 'single-stream', name: 'Single-Stream Recycling' },
  { id: 'separated', name: 'Separated by Material' },
  { id: 'composting', name: 'Organic Waste Composting' },
  { id: 'shredding', name: 'Document Shredding' },
  { id: 'electronics', name: 'Electronics Recycling' },
];

const complianceNeeds = [
  { id: 'hipaa', name: 'HIPAA Compliance' },
  { id: 'epa', name: 'EPA Regulations' },
  { id: 'dot', name: 'DOT Requirements' },
  { id: 'local', name: 'Local Ordinances' },
  { id: 'corporate', name: 'Corporate Sustainability Goals' },
];

const additionalServices = [
  { id: 'container-cleaning', name: 'Container Cleaning & Sanitization' },
  { id: 'compactor-service', name: 'Compactor Service & Maintenance' },
  { id: 'reporting', name: 'Waste Audit & Reporting' },
  { id: 'consultation', name: 'Waste Reduction Consultation' },
  { id: 'emergency', name: 'Emergency Cleanup Services' },
];
*/

// Modern custom select component
const ModernSelect = ({ label, value, onChange, options, placeholder }: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void, 
  options: { id: string, name: string }[], 
  placeholder: string 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-semibold text-white block">{label}</label>
      <div className="relative">
        <motion.button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-left text-white focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300 flex items-center justify-between"
          whileTap={{ scale: 0.98 }}
        >
          <span className={value ? 'text-white' : 'text-gray-400'}>
            {value ? options.find(opt => opt.id === value)?.name : placeholder}
          </span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRight className="w-5 h-5 rotate-90" />
          </motion.div>
        </motion.button>
        
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 bg-slate-800/95 backdrop-blur-xl border border-white/30 rounded-xl overflow-hidden z-50 shadow-2xl"
            >
              {options.map((option) => (
                <motion.button
                  key={option.id}
                  type="button"
                  onClick={() => {
                    onChange(option.id);
                    setIsOpen(false);
                  }}
                  className="w-full p-4 text-left text-white hover:bg-slate-700/80 transition-colors duration-200 border-b border-white/10 last:border-b-0"
                  whileHover={{ backgroundColor: 'rgba(51, 65, 85, 0.8)' }}
                >
                  {option.name}
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Email Validation
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Phone Number Formatting
const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

// Phone Number Validation
const validatePhoneNumber = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length === 10 || (cleaned.length === 11 && cleaned[0] === '1');
};

// Address Validation
const validateAddress = (address: string): boolean => {
  return address.trim().length >= 10 && /\d/.test(address) && /[a-zA-Z]/.test(address);
};

const BrandAlignedWasteForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<Partial<WasteFormData>>({
    servicePackage: 'standard',
    wasteTypes: [],
    containerSizes: [],
    specialHandling: [],
    recyclingRequirements: [],
    complianceNeeds: [],
    additionalServices: [],
    hazardousMaterials: false,
  });

  // Save form data to localStorage on changes
  useEffect(() => {
    localStorage.setItem('wasteFormData', JSON.stringify(formData));
  }, [formData]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('wasteFormData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Enhanced form field validation
  const validateField = (fieldName: keyof FormErrors, value: string): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value) return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
        if (!value || value.trim().length < 2) return 'First name must be at least 2 characters';
        break;
      case 'lastName':
        if (!value || value.trim().length < 2) return 'Last name must be at least 2 characters';
        break;
      case 'companyName':
        if (!value || value.trim().length < 2) return 'Company name is required';
        break;
      case 'propertyAddress':
        if (!value) return 'Property address is required';
        if (!validateAddress(value)) return 'Please enter a complete address with street number and name';
        break;
      case 'facilityType':
        if (!value) return 'Please select a facility type';
        break;
      case 'wasteVolume':
        if (!value) return 'Please select waste volume';
        break;
      case 'pickupFrequency':
        if (!value) return 'Please select pickup frequency';
        break;
    }
    return undefined;
  };

  // Enhanced handleNext with validation
  const handleNext = () => {
    if (currentStep < steps.length) {
      setIsLoading(true);
      
      // Validate current step
      const errors: FormErrors = {};
      
      if (currentStep === 2) {
        errors.facilityType = validateField('facilityType', formData.facilityType || '');
        errors.propertyAddress = validateField('propertyAddress', formData.propertyAddress || '');
      } else if (currentStep === 3) {
        errors.wasteVolume = validateField('wasteVolume', formData.wasteVolume || '');
      } else if (currentStep === 4) {
        errors.pickupFrequency = validateField('pickupFrequency', formData.pickupFrequency || '');
      } else if (currentStep === 6) {
        errors.firstName = validateField('firstName', formData.firstName || '');
        errors.lastName = validateField('lastName', formData.lastName || '');
        errors.email = validateField('email', formData.email || '');
        errors.phone = validateField('phone', formData.phone || '');
        errors.companyName = validateField('companyName', formData.companyName || '');
      }
      
      // Filter out undefined errors
      const filteredErrors = Object.fromEntries(
        Object.entries(errors).filter((entry) => entry[1] !== undefined)
      );
      
      setFormErrors(filteredErrors);
      
      if (Object.keys(filteredErrors).length === 0) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1);
          setIsLoading(false);
        }, 500);
      } else {
        setIsLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.servicePackage;
      case 2:
        return formData.facilityType && 
               formData.propertyAddress &&
               !formErrors.facilityType &&
               !formErrors.propertyAddress;
      case 3:
        return formData.wasteVolume &&
               !formErrors.wasteVolume;
      case 4:
        return formData.pickupFrequency &&
               !formErrors.pickupFrequency;
      case 6:
        return formData.firstName && 
               formData.lastName && 
               formData.email && 
               formData.phone && 
               formData.companyName &&
               !formErrors.firstName &&
               !formErrors.lastName &&
               !formErrors.email &&
               !formErrors.phone &&
               !formErrors.companyName;
      default:
        return true;
    }
  };

  // Enhanced input handlers with validation
  const handleEmailChange = (value: string) => {
    setFormData({...formData, email: value});
    const error = validateField('email', value);
    setFormErrors(prev => ({ ...prev, email: error }));
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    setFormData({...formData, phone: formatted});
    const error = validateField('phone', value);
    setFormErrors(prev => ({ ...prev, phone: error }));
  };

  const handleAddressChange = (value: string) => {
    setFormData({...formData, propertyAddress: value});
    const error = validateField('propertyAddress', value);
    setFormErrors(prev => ({ ...prev, propertyAddress: error }));
  };

  const handleNameChange = (field: 'firstName' | 'lastName', value: string) => {
    setFormData({...formData, [field]: value});
    const error = validateField(field, value);
    setFormErrors(prev => ({ ...prev, [field]: error }));
  };

  const handleCompanyNameChange = (value: string) => {
    setFormData({...formData, companyName: value});
    const error = validateField('companyName', value);
    setFormErrors(prev => ({ ...prev, companyName: error }));
  };

  const handleWasteTypeToggle = (typeId: string) => {
    const currentTypes = formData.wasteTypes || [];
    if (currentTypes.includes(typeId)) {
      setFormData({
        ...formData,
        wasteTypes: currentTypes.filter(id => id !== typeId)
      });
    } else {
      setFormData({
        ...formData,
        wasteTypes: [...currentTypes, typeId]
      });
    }
  };

  /* 
  const handleContainerSizeToggle = (sizeId: string) => {
    const currentSizes = formData.containerSizes || [];
    if (currentSizes.includes(sizeId)) {
      setFormData({
        ...formData,
        containerSizes: currentSizes.filter(id => id !== sizeId)
      });
    } else {
      setFormData({
        ...formData,
        containerSizes: [...currentSizes, sizeId]
      });
    }
  };

  const handleSpecialHandlingToggle = (handlingId: string) => {
    const currentHandling = formData.specialHandling || [];
    if (currentHandling.includes(handlingId)) {
      setFormData({
        ...formData,
        specialHandling: currentHandling.filter(id => id !== handlingId)
      });
    } else {
      setFormData({
        ...formData,
        specialHandling: [...currentHandling, handlingId]
      });
    }
  };

  const handleRecyclingRequirementToggle = (reqId: string) => {
    const currentReqs = formData.recyclingRequirements || [];
    if (currentReqs.includes(reqId)) {
      setFormData({
        ...formData,
        recyclingRequirements: currentReqs.filter(id => id !== reqId)
      });
    } else {
      setFormData({
        ...formData,
        recyclingRequirements: [...currentReqs, reqId]
      });
    }
  };

  const handleComplianceNeedToggle = (needId: string) => {
    const currentNeeds = formData.complianceNeeds || [];
    if (currentNeeds.includes(needId)) {
      setFormData({
        ...formData,
        complianceNeeds: currentNeeds.filter(id => id !== needId)
      });
    } else {
      setFormData({
        ...formData,
        complianceNeeds: [...currentNeeds, needId]
      });
    }
  };

  const handleAdditionalServiceToggle = (serviceId: string) => {
    const currentServices = formData.additionalServices || [];
    if (currentServices.includes(serviceId)) {
      setFormData({
        ...formData,
        additionalServices: currentServices.filter(id => id !== serviceId)
      });
    } else {
      setFormData({
        ...formData,
        additionalServices: [...currentServices, serviceId]
      });
    }
  };
  */

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate all contact fields before submission
      const contactErrors: FormErrors = {};
      contactErrors.firstName = validateField('firstName', formData.firstName || '');
      contactErrors.lastName = validateField('lastName', formData.lastName || '');
      contactErrors.email = validateField('email', formData.email || '');
      contactErrors.phone = validateField('phone', formData.phone || '');
      contactErrors.companyName = validateField('companyName', formData.companyName || '');
      
      const filteredContactErrors = Object.fromEntries(
        Object.entries(contactErrors).filter((entry) => entry[1] !== undefined)
      );
      
      if (Object.keys(filteredContactErrors).length > 0) {
        setFormErrors(filteredContactErrors);
        setIsLoading(false);
        return;
      }

      // Submit form data to Supabase database
      const { supabase } = await import('../../../../lib/supabase/client');
      
      const bookingData = {
        user_id: user?.id || null,
        service_type: 'waste-management',
        property_details: {
          facilityType: formData.facilityType,
          address: formData.propertyAddress,
          wasteVolume: formData.wasteVolume,
          wasteTypes: formData.wasteTypes,
          hazardousMaterials: formData.hazardousMaterials,
        },
        service_details: {
          servicePackage: formData.servicePackage,
          pickupFrequency: formData.pickupFrequency,
          specialHandling: formData.specialHandling,
          recyclingRequirements: formData.recyclingRequirements,
          complianceNeeds: formData.complianceNeeds,
          additionalServices: formData.additionalServices,
        },
        schedule: {
          pickupFrequency: formData.pickupFrequency,
        },
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle,
        },
        status: 'pending',
        special_instructions: formData.specialInstructions,
        metadata: {
          submittedAt: new Date().toISOString(),
          requestType: 'estimate',
        }
      };

      const { data, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Error saving booking:', error);
        throw new Error('Failed to save booking');
      }

      console.log('Booking saved successfully:', data);
      
      // Show success popup
      setShowSuccessPopup(true);
      
      // Clear form data from localStorage
      localStorage.removeItem('wasteFormData');
      
    } catch (error) {
      console.error('Error submitting waste management form:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClosePopup = () => {
    setShowSuccessPopup(false);
    
    // Clear form data from localStorage
    localStorage.removeItem('wasteFormData');
    
    if (user) {
      navigate('/accountdashboard');
    } else {
      navigate('/');
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-start py-8 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress Bar */}
          <div className="mb-12">
            {/* Step Counter */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">
                  Step {currentStep} of {steps.length}
                </span>
              </div>
            </div>
            
            {/* Progress Line */}
            <div className="relative bg-white/10 h-2 rounded-full mb-6 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"
                initial={{ width: '16.67%' }}
                animate={{ width: `${(currentStep / steps.length) * 100}%` }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              />
            </div>
            
            {/* Step Indicators */}
            <div className="flex justify-between">
              {steps.map((step) => (
                <div key={step.id} className="flex flex-col items-center">
                  <motion.div
                    className={`w-4 h-4 rounded-full border-2 ${
                      currentStep >= step.id 
                        ? 'bg-emerald-400 border-emerald-400' 
                        : 'bg-white/10 border-white/30'
                    }`}
                    animate={{
                      scale: currentStep === step.id ? 1.2 : 1,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                  <span className={`text-xs mt-2 font-medium ${
                    currentStep >= step.id ? 'text-emerald-400' : 'text-white/60'
                  }`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Form Card */}
          <motion.div
            className="backdrop-blur-[30px] bg-white/[0.08] border border-white/20 rounded-3xl p-8 shadow-2xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              {/* Step 1: Service Type */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Choose Your Service Package</h2>
                  <p className="text-gray-300 mb-6">Select the waste management service that best fits your needs</p>

                  <div className="grid gap-4">
                    {servicePackages.map((pkg) => (
                      <motion.button
                        key={pkg.id}
                        type="button"
                        onClick={() => setFormData({...formData, servicePackage: pkg.id})}
                        className={`p-6 rounded-xl border-2 transition-all duration-300 text-left ${
                          formData.servicePackage === pkg.id
                            ? 'bg-emerald-500/20 border-emerald-400 shadow-lg shadow-emerald-400/20'
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center mb-3">
                          <div className="text-emerald-400 mr-4">
                            {pkg.icon}
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-white">{pkg.name}</h3>
                          </div>
                        </div>
                        <p className="text-gray-300">{pkg.description}</p>
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Step 2: Facility Information */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Facility Information</h2>
                  <p className="text-gray-300 mb-6">Tell us about your facility</p>

                  <div className="space-y-6">
                    <ModernSelect
                      label="Facility Type *"
                      value={formData.facilityType || ''}
                      onChange={(value) => setFormData({...formData, facilityType: value})}
                      options={facilityTypes}
                      placeholder="Select facility type"
                    />
                    {formErrors.facilityType && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center text-red-400 text-sm"
                      >
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {formErrors.facilityType}
                      </motion.div>
                    )}

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">
                        Property Address *
                      </label>
                      <input
                        type="text"
                        value={formData.propertyAddress || ''}
                        onChange={(e) => handleAddressChange(e.target.value)}
                        className={`w-full bg-white/10 p-4 rounded-xl border ${
                          formErrors.propertyAddress ? 'border-red-400' : 'border-white/20'
                        } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                        placeholder="Enter facility address"
                      />
                      {formErrors.propertyAddress && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.propertyAddress}
                        </motion.div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Waste Details */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Waste Details</h2>
                  <p className="text-gray-300 mb-6">Specify your waste management needs</p>

                  <div className="space-y-6">
                    <ModernSelect
                      label="Estimated Waste Volume *"
                      value={formData.wasteVolume || ''}
                      onChange={(value) => setFormData({...formData, wasteVolume: value})}
                      options={wasteVolumes}
                      placeholder="Select waste volume"
                    />
                    {formErrors.wasteVolume && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center text-red-400 text-sm"
                      >
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {formErrors.wasteVolume}
                      </motion.div>
                    )}

                    <div>
                      <label className="block text-white font-medium mb-3">
                        Types of Waste (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-3">
                        {wasteTypes.map((type) => (
                          <motion.button
                            key={type.id}
                            type="button"
                            onClick={() => handleWasteTypeToggle(type.id)}
                            className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                              formData.wasteTypes?.includes(type.id)
                                ? 'border-emerald-400 bg-emerald-500/20 text-white'
                                : 'border-white/20 bg-white/5 text-gray-300 hover:border-white/40'
                            }`}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-center justify-between">
                              <span>{type.name}</span>
                              {formData.wasteTypes?.includes(type.id) && (
                                <CheckCircle className="w-5 h-5 text-emerald-400" />
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Schedule */}
              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Pickup Schedule</h2>
                  <p className="text-gray-300 mb-6">When do you need pickup service?</p>

                  <div className="space-y-6">
                    <ModernSelect
                      label="Pickup Frequency *"
                      value={formData.pickupFrequency || ''}
                      onChange={(value) => setFormData({...formData, pickupFrequency: value})}
                      options={pickupFrequencies}
                      placeholder="Select pickup frequency"
                    />
                    {formErrors.pickupFrequency && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center text-red-400 text-sm"
                      >
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {formErrors.pickupFrequency}
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Step 5: Requirements */}
              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Additional Requirements</h2>
                  <p className="text-gray-300 mb-6">Any special requirements or services needed?</p>

                  <div className="space-y-6">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="hazardous"
                        checked={formData.hazardousMaterials || false}
                        onChange={(e) => setFormData({...formData, hazardousMaterials: e.target.checked})}
                        className="w-5 h-5 text-emerald-400 bg-white/10 border-white/20 rounded focus:ring-emerald-400"
                      />
                      <label htmlFor="hazardous" className="text-white">
                        Facility handles hazardous materials
                      </label>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">Special Instructions</label>
                      <textarea
                        value={formData.specialInstructions || ''}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                        className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300 h-24 resize-none"
                        placeholder="Any special instructions or access requirements..."
                      />
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Step 6: Contact Information */}
              {currentStep === 6 && (
                <motion.div
                  key="step6"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
                  <p className="text-gray-300 mb-6">We'll use this information to coordinate your service</p>

                  <div className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="text-sm font-semibold text-white block">
                          First Name *
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={formData.firstName || ''}
                            onChange={(e) => handleNameChange('firstName', e.target.value)}
                            className={`w-full bg-white/10 p-4 pl-12 rounded-xl border ${
                              formErrors.firstName ? 'border-red-400' : 'border-white/20'
                            } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                            placeholder="Enter your first name"
                          />
                          <User className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        </div>
                        {formErrors.firstName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.firstName}
                          </motion.div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-semibold text-white block">
                          Last Name *
                        </label>
                        <input
                          type="text"
                          value={formData.lastName || ''}
                          onChange={(e) => handleNameChange('lastName', e.target.value)}
                          className={`w-full bg-white/10 p-4 rounded-xl border ${
                            formErrors.lastName ? 'border-red-400' : 'border-white/20'
                          } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                          placeholder="Enter your last name"
                        />
                        {formErrors.lastName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.lastName}
                          </motion.div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">
                        Email Address *
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          value={formData.email || ''}
                          onChange={(e) => handleEmailChange(e.target.value)}
                          className={`w-full bg-white/10 p-4 pl-12 rounded-xl border ${
                            formErrors.email ? 'border-red-400' : 'border-white/20'
                          } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                          placeholder="Enter your email address"
                        />
                        <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      </div>
                      {formErrors.email && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.email}
                        </motion.div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">
                        Phone Number *
                      </label>
                      <div className="relative">
                        <input
                          type="tel"
                          value={formData.phone || ''}
                          onChange={(e) => handlePhoneChange(e.target.value)}
                          className={`w-full bg-white/10 p-4 pl-12 rounded-xl border ${
                            formErrors.phone ? 'border-red-400' : 'border-white/20'
                          } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                          placeholder="Enter your phone number"
                        />
                        <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      </div>
                      {formErrors.phone && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.phone}
                        </motion.div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">
                        Company Name *
                      </label>
                      <input
                        type="text"
                        value={formData.companyName || ''}
                        onChange={(e) => handleCompanyNameChange(e.target.value)}
                        className={`w-full bg-white/10 p-4 rounded-xl border ${
                          formErrors.companyName ? 'border-red-400' : 'border-white/20'
                        } text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300`}
                        placeholder="Enter your company name"
                      />
                      {formErrors.companyName && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.companyName}
                        </motion.div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-semibold text-white block">Job Title</label>
                      <input
                        type="text"
                        value={formData.jobTitle || ''}
                        onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                        className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-emerald-400 focus:bg-white/15 transition-all duration-300"
                        placeholder="Enter your job title"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center mt-8">
              <motion.button
                type="button"
                onClick={handleBack}
                disabled={currentStep === 1}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  currentStep === 1
                    ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                    : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                }`}
                whileHover={currentStep !== 1 ? { scale: 1.05 } : {}}
                whileTap={currentStep !== 1 ? { scale: 0.95 } : {}}
              >
                Back
              </motion.button>

              {currentStep < steps.length ? (
                <Button
                  onClick={handleNext}
                  disabled={!isStepValid() || isLoading}
                  className="px-8 py-3 bg-emerald-500 hover:bg-emerald-600 text-white rounded-xl font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Loading...' : 'Next Step'}
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={!isStepValid() || isLoading}
                  className="px-8 py-3 bg-emerald-500 hover:bg-emerald-600 text-white rounded-xl font-semibold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Submitting...' : 'Request Estimate'}
                </Button>
              )}
            </div>
          </motion.div>

          {/* Success Popup */}
          <AnimatePresence>
            {showSuccessPopup && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
              >
                <motion.div
                  initial={{ scale: 0.7, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.7, opacity: 0 }}
                  className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 max-w-md w-full border border-white/20"
                >
                  <div className="text-center">
                    <div className="w-16 h-16 bg-emerald-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                      <CheckCircle className="w-8 h-8 text-emerald-400" />
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-3">Estimate Request Submitted!</h3>
                    <p className="text-gray-300 mb-6">
                      Thank you for your waste management estimate request. Our team will review your requirements and contact you within 24 hours.
                    </p>
                    <Button
                      onClick={handleClosePopup}
                      className="w-full bg-emerald-500 hover:bg-emerald-600 text-white py-3 rounded-xl font-semibold"
                    >
                      Continue
                    </Button>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedWasteForm; 
