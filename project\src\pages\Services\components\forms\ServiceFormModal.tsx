import React from 'react';
import { X } from 'lucide-react';
import { WindowCleaningForm } from './WindowCleaningForm';
import { CarpetCleaningForm } from './CarpetCleaningForm';
import { PostConstructionForm } from './PostConstructionForm';
import { DeepCleaningForm } from './DeepCleaningForm';
import { SanitizationForm } from './SanitizationForm';
import { TileGroutForm } from './TileGroutForm';
import { PressureWashingForm } from './PressureWashingForm';
import { FloorRestorationForm } from './FloorRestorationForm';
import type { ServiceType } from '../../types';

interface ServiceFormModalProps {
  service: ServiceType;
  onClose: () => void;
  onSubmit: (formData: any) => void;
}

export function ServiceFormModal({ service, onClose, onSubmit }: ServiceFormModalProps) {
  const renderForm = () => {
    switch (service.id) {
      case 'window':
        return <WindowCleaningForm onSubmit={onSubmit} />;
      case 'carpet':
        return <CarpetCleaningForm onSubmit={onSubmit} />;
      case 'construction':
        return <PostConstructionForm onSubmit={onSubmit} />;
      case 'deep':
        return <DeepCleaningForm onSubmit={onSubmit} />;
      case 'sanitization':
        return <SanitizationForm onSubmit={onSubmit} />;
      case 'tile':
        return <TileGroutForm onSubmit={onSubmit} />;
      case 'pressure':
        return <PressureWashingForm onSubmit={onSubmit} />;
      case 'floor':
        return <FloorRestorationForm onSubmit={onSubmit} />;
      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50">
      <div className="min-h-screen px-4 text-center flex items-center justify-center">
        <div className="relative w-full max-w-2xl p-6 my-8 bg-white rounded-2xl shadow-xl">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </button>
          
          <div className="mt-2">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              {service.title}
            </h2>
            
            {renderForm()}
          </div>
        </div>
      </div>
    </div>
  );
}
