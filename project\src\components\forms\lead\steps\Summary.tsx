import React from 'react';
import { Check, Calendar, Users, MapPin } from 'lucide-react';
import type { FormData } from '../types';

interface SummaryProps {
  formData: FormData;
  onSubmit: () => void;
}

export function Summary({ formData, onSubmit }: SummaryProps) {
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-medium mb-6">Review Your Selections</h2>
      
      <div className="space-y-6">
        <SummarySection
          title="Selected Services"
          icon={Check}
          items={formData.services}
        />

        {formData.frequency && (
          <SummarySection
            title="Service Frequency"
            icon={Calendar}
            items={[formData.frequency]}
          />
        )}

        {formData.additionalServices.length > 0 && (
          <SummarySection
            title="Additional Services"
            icon={Check}
            items={formData.additionalServices}
          />
        )}

        <SummarySection
          title="Office Details"
          icon={Users}
          items={[
            `${formData.officeDetails.squareFootage} sq ft`,
            `${formData.officeDetails.employeeCount} employees`
          ]}
        />

        <SummarySection
          title="Schedule"
          icon={Calendar}
          items={[
            formData.schedule.startDate?.toLocaleDateString() || '',
            formData.schedule.timePreference || ''
          ]}
        />

        <SummarySection
          title="Contact"
          icon={MapPin}
          items={[
            formData.contact.fullName,
            formData.contact.email,
            formData.contact.phone
          ]}
        />
      </div>

      <button
        onClick={onSubmit}
        className="w-full py-3 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
      >
        Submit Request
      </button>
    </div>
  );
}

interface SummarySectionProps {
  title: string;
  icon: React.ComponentType<any>;
  items: string[];
}

function SummarySection({ title, icon: Icon, items }: SummarySectionProps) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center mb-3">
        <Icon className="w-5 h-5 text-green-600 mr-2" />
        <h3 className="font-medium text-gray-900">{title}</h3>
      </div>
      <ul className="space-y-1">
        {items.map((item, index) => (
          <li key={index} className="text-gray-600">
            {item}
          </li>
        ))}
      </ul>
    </div>
  );
}
