/**
 * Payment Test Page
 * Test the Square Web SDK integration in React
 */

import React, { useState } from 'react';
import { SquareWebSDKPayment } from '../components/SquareWebSDKPayment';
import type { PaymentResult } from '../lib/square';

export const PaymentTest: React.FC = () => {
  const [paymentResult, setPaymentResult] = useState<PaymentResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handlePaymentSuccess = (result: PaymentResult) => {
    console.log('Payment successful:', result);
    setPaymentResult(result);
    setError(null);
  };

  const handlePaymentError = (errorMessage: string) => {
    console.error('Payment failed:', errorMessage);
    setError(errorMessage);
    setPaymentResult(null);
  };

  const resetTest = () => {
    setPaymentResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Square Web SDK Payment Test
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Test your Square payment integration with sandbox credentials
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Payment Form */}
          <div>
            <SquareWebSDKPayment
              amount={2500} // $25.00
              description="Test Payment - Cleaning Service"
              customerEmail="<EMAIL>"
              orderId={`test-order-${Date.now()}`}
              onPaymentSuccess={handlePaymentSuccess}
              onPaymentError={handlePaymentError}
              className="mb-6"
            />

            {/* Reset Button */}
            {(paymentResult || error) && (
              <div className="text-center">
                <button
                  onClick={resetTest}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                >
                  Reset Test
                </button>
              </div>
            )}
          </div>

          {/* Results Panel */}
          <div className="space-y-6">
            {/* Test Card Numbers */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                💳 Test Card Numbers
              </h3>
              <div className="space-y-3 text-sm">
                <div>
                  <strong>Visa:</strong> 4111 1111 1111 1111
                </div>
                <div>
                  <strong>Mastercard:</strong> 5555 5555 5555 4444
                </div>
                <div>
                  <strong>CVV:</strong> Any 3 digits
                </div>
                <div>
                  <strong>Expiry:</strong> Any future date
                </div>
              </div>
            </div>

            {/* Success Result */}
            {paymentResult && (
              <div className="bg-green-50 border border-green-200 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-green-800 mb-4">
                  ✅ Payment Successful!
                </h3>
                <div className="space-y-2 text-sm text-green-700">
                  <div>
                    <strong>Payment ID:</strong> {paymentResult.paymentId}
                  </div>
                  {paymentResult.receiptUrl && (
                    <div>
                      <strong>Receipt:</strong>{' '}
                      <a 
                        href={paymentResult.receiptUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="underline hover:no-underline"
                      >
                        View Receipt
                      </a>
                    </div>
                  )}
                  <div className="mt-4 p-3 bg-green-100 rounded text-xs">
                    <strong>Note:</strong> This is a test payment in sandbox mode. 
                    No real money was charged.
                  </div>
                </div>
              </div>
            )}

            {/* Error Result */}
            {error && (
              <div className="bg-red-50 border border-red-200 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-red-800 mb-4">
                  ❌ Payment Failed
                </h3>
                <div className="text-sm text-red-700">
                  <strong>Error:</strong> {error}
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-800 mb-4">
                📋 Test Instructions
              </h3>
              <ol className="list-decimal list-inside space-y-2 text-sm text-blue-700">
                <li>Use one of the test card numbers above</li>
                <li>Enter any valid expiry date (future)</li>
                <li>Enter any 3-digit CVV</li>
                <li>Click "Pay $25.00" to process</li>
                <li>Check the results panel for success/error</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
