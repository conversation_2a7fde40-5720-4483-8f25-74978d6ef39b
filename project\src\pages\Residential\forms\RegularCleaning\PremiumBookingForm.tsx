import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Users, Calendar, Clock, CheckCircle, 
  Shield, Award, Star, ArrowRight, Building2,
  Sparkles, DollarSign, PhoneCall, Info, 
  TrendingUp, ChevronRight, MapPin, Heart
} from 'lucide-react';

interface BookingData {
  propertyDetails: {
    type: string;
    size: string;
    bedrooms: number;
    bathrooms: number;
  };
  serviceDetails: {
    frequency: string;
    preferredDay: string;
    preferredTime: string;
    specialRequests: string;
  };
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    zipCode: string;
  };
}

interface TrustIndicator {
  icon: React.ReactNode;
  text: string;
  highlight?: string;
}

const PremiumBookingForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentSection, setCurrentSection] = useState<'property' | 'service' | 'personal'>('property');
  const [isCalculating, setIsCalculating] = useState(false);
  const [showPriceBreakdown, setShowPriceBreakdown] = useState(false);
  const [bookingData, setBookingData] = useState<BookingData>({
    propertyDetails: {
      type: '',
      size: '',
      bedrooms: 2,
      bathrooms: 1
    },
    serviceDetails: {
      frequency: '',
      preferredDay: '',
      preferredTime: '',
      specialRequests: ''
    },
    personalInfo: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      zipCode: ''
    }
  });

  // Calculate price based on selections
  const calculatePrice = () => {
    const sizeMultiplier = {
      'small': 1,
      'medium': 1.3,
      'large': 1.6,
      'xlarge': 2
    };

    const frequencyDiscount = {
      'weekly': 0.8,
      'biweekly': 0.85,
      'monthly': 0.95,
      'onetime': 1
    };

    const basePrice = 100;
    const size = sizeMultiplier[bookingData.propertyDetails.size] || 1;
    const frequency = frequencyDiscount[bookingData.serviceDetails.frequency] || 1;
    const roomsMultiplier = (bookingData.propertyDetails.bedrooms * 0.15) + (bookingData.propertyDetails.bathrooms * 0.2);
    
    return Math.round(basePrice * size * frequency * (1 + roomsMultiplier));
  };

  const calculateSavings = () => {
    const oneTimePrice = Math.round(calculatePrice() / (frequencyDiscount[bookingData.serviceDetails.frequency] || 1));
    return oneTimePrice - calculatePrice();
  };

  const frequencyDiscount = {
    'weekly': 0.8,
    'biweekly': 0.85,
    'monthly': 0.95,
    'onetime': 1
  };

  // Property types with professional icons
  const propertyTypes = [
    { id: 'apartment', name: 'Apartment', icon: <Building2 className="w-6 h-6" /> },
    { id: 'house', name: 'Single Family', icon: <Home className="w-6 h-6" /> },
    { id: 'condo', name: 'Condominium', icon: <Building2 className="w-6 h-6" /> },
    { id: 'townhouse', name: 'Townhouse', icon: <Home className="w-6 h-6" /> }
  ];

  // Property sizes with descriptions
  const propertySizes = [
    { id: 'small', name: 'Compact', sqft: 'Under 1,000 sq ft', recommended: '1-2 hours' },
    { id: 'medium', name: 'Standard', sqft: '1,000-2,000 sq ft', recommended: '2-3 hours', popular: true },
    { id: 'large', name: 'Spacious', sqft: '2,000-3,500 sq ft', recommended: '3-4 hours' },
    { id: 'xlarge', name: 'Estate', sqft: 'Over 3,500 sq ft', recommended: '4+ hours' }
  ];

  // Service frequencies with benefits
  const frequencies = [
    { 
      id: 'weekly', 
      name: 'Weekly Service', 
      description: 'Pristine home, always guest-ready',
      savings: '20% savings',
      benefits: ['Consistent cleanliness', 'Priority scheduling', 'Dedicated team']
    },
    { 
      id: 'biweekly', 
      name: 'Bi-Weekly Service', 
      description: 'Perfect balance of clean and value',
      savings: '15% savings',
      benefits: ['Regular maintenance', 'Flexible scheduling', 'Cost-effective'],
      recommended: true
    },
    { 
      id: 'monthly', 
      name: 'Monthly Service', 
      description: 'Essential maintenance cleaning',
      savings: '5% savings',
      benefits: ['Budget-friendly', 'Deep cleaning focus', 'Seasonal flexibility']
    },
    { 
      id: 'onetime', 
      name: 'One-Time Service', 
      description: 'Deep clean or special occasion',
      savings: 'Standard rate',
      benefits: ['No commitment', 'Try our service', 'Special events']
    }
  ];

  // Time slots with availability
  const timeSlots = [
    { id: 'early', name: '8:00 AM - 10:00 AM', period: 'Early Morning', available: 3 },
    { id: 'mid', name: '10:00 AM - 12:00 PM', period: 'Mid Morning', available: 2, popular: true },
    { id: 'afternoon', name: '12:00 PM - 2:00 PM', period: 'Early Afternoon', available: 5 },
    { id: 'late', name: '2:00 PM - 4:00 PM', period: 'Late Afternoon', available: 4 }
  ];

  // Trust indicators
  const trustIndicators: TrustIndicator[] = [
    { icon: <Shield className="w-5 h-5" />, text: 'Licensed & Insured', highlight: 'Fully Protected' },
    { icon: <Award className="w-5 h-5" />, text: '10+ Years Experience', highlight: 'Industry Leaders' },
    { icon: <Users className="w-5 h-5" />, text: '50,000+ Happy Homes', highlight: 'Trusted by Many' },
    { icon: <Star className="w-5 h-5" />, text: '4.9/5 Average Rating', highlight: '2,500+ Reviews' }
  ];

  // Form validation
  const isPropertyComplete = () => {
    return bookingData.propertyDetails.type && 
           bookingData.propertyDetails.size;
  };

  const isServiceComplete = () => {
    return bookingData.serviceDetails.frequency && 
           bookingData.serviceDetails.preferredDay && 
           bookingData.serviceDetails.preferredTime;
  };

  const isPersonalComplete = () => {
    return bookingData.personalInfo.firstName && 
           bookingData.personalInfo.lastName && 
           bookingData.personalInfo.email && 
           bookingData.personalInfo.phone && 
           bookingData.personalInfo.address;
  };

  // Navigation functions
  const handleNext = () => {
    if (currentSection === 'property' && isPropertyComplete()) {
      setIsCalculating(true);
      setTimeout(() => {
        setIsCalculating(false);
        setCurrentSection('service');
      }, 800);
    } else if (currentSection === 'service' && isServiceComplete()) {
      setCurrentSection('personal');
    }
  };

  const handleBack = () => {
    if (currentSection === 'service') {
      setCurrentSection('property');
    } else if (currentSection === 'personal') {
      setCurrentSection('service');
    }
  };

  const handleSubmit = async () => {
    if (isPersonalComplete()) {
      // Submit logic here
      navigate('/thank-you');
    }
  };

  // Get next available date
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Premium Header */}
      <motion.header 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white shadow-sm sticky top-0 z-40"
      >
        <div className="max-w-6xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Sparkles className="w-8 h-8 text-blue-600" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">CleanPro Premium</h1>
                <p className="text-sm text-gray-600">Professional Home Cleaning Services</p>
              </div>
            </div>
            <div className="hidden md:flex items-center gap-2">
              <PhoneCall className="w-5 h-5 text-gray-600" />
              <span className="font-semibold">1-800-CLEAN-PRO</span>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Progress Indicator */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {['Property Details', 'Service Selection', 'Contact Information'].map((step, index) => {
              const isActive = (index === 0 && currentSection === 'property') ||
                             (index === 1 && currentSection === 'service') ||
                             (index === 2 && currentSection === 'personal');
              const isCompleted = (index === 0 && (currentSection === 'service' || currentSection === 'personal')) ||
                                (index === 1 && currentSection === 'personal');
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`flex items-center gap-3 ${index !== 2 ? 'mr-8' : ''}`}>
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all ${
                      isActive ? 'bg-blue-600 text-white' : 
                      isCompleted ? 'bg-green-500 text-white' : 
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {isCompleted ? <CheckCircle className="w-5 h-5" /> : index + 1}
                    </div>
                    <span className={`hidden md:block font-medium ${
                      isActive ? 'text-blue-600' : 
                      isCompleted ? 'text-green-600' : 
                      'text-gray-500'
                    }`}>
                      {step}
                    </span>
                  </div>
                  {index !== 2 && (
                    <ChevronRight className={`w-5 h-5 mx-4 ${
                      isCompleted ? 'text-green-500' : 'text-gray-300'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form Section */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              {/* Property Details Section */}
              {currentSection === 'property' && (
                <motion.div
                  key="property"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-lg p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Tell us about your property</h2>
                    <p className="text-gray-600">This helps us provide accurate pricing and assign the right team</p>
                  </div>

                  {/* Property Type */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-4">Property Type</label>
                    <div className="grid grid-cols-2 gap-4">
                      {propertyTypes.map((type) => (
                        <motion.button
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setBookingData({
                            ...bookingData,
                            propertyDetails: { ...bookingData.propertyDetails, type: type.id }
                          })}
                          className={`p-6 rounded-xl border-2 transition-all ${
                            bookingData.propertyDetails.type === type.id
                              ? 'border-blue-500 bg-blue-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          <div className={`mb-3 ${
                            bookingData.propertyDetails.type === type.id
                              ? 'text-blue-600'
                              : 'text-gray-600'
                          }`}>
                            {type.icon}
                          </div>
                          <span className="font-medium text-gray-900">{type.name}</span>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Property Size */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-4">Property Size</label>
                    <div className="grid grid-cols-2 gap-4">
                      {propertySizes.map((size) => (
                        <motion.button
                          key={size.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setBookingData({
                            ...bookingData,
                            propertyDetails: { ...bookingData.propertyDetails, size: size.id }
                          })}
                          className={`relative p-5 rounded-xl border-2 transition-all text-left ${
                            bookingData.propertyDetails.size === size.id
                              ? 'border-blue-500 bg-blue-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          {size.popular && (
                            <span className="absolute -top-3 right-4 bg-orange-500 text-white text-xs px-3 py-1 rounded-full">
                              Most Popular
                            </span>
                          )}
                          <div className="font-semibold text-gray-900 mb-1">{size.name}</div>
                          <div className="text-sm text-gray-600 mb-2">{size.sqft}</div>
                          <div className="text-xs text-gray-500">Est. {size.recommended}</div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Rooms Counter */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-4">Number of Rooms</label>
                    <div className="grid grid-cols-2 gap-6">
                      <div className="bg-gray-50 p-6 rounded-xl">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-700">Bedrooms</span>
                          <div className="flex items-center gap-3">
                            <button
                              onClick={() => setBookingData({
                                ...bookingData,
                                propertyDetails: {
                                  ...bookingData.propertyDetails,
                                  bedrooms: Math.max(0, bookingData.propertyDetails.bedrooms - 1)
                                }
                              })}
                              className="w-8 h-8 rounded-full bg-white border hover:bg-gray-100 transition-colors flex items-center justify-center"
                            >
                              -
                            </button>
                            <span className="text-2xl font-semibold w-8 text-center">
                              {bookingData.propertyDetails.bedrooms}
                            </span>
                            <button
                              onClick={() => setBookingData({
                                ...bookingData,
                                propertyDetails: {
                                  ...bookingData.propertyDetails,
                                  bedrooms: bookingData.propertyDetails.bedrooms + 1
                                }
                              })}
                              className="w-8 h-8 rounded-full bg-white border hover:bg-gray-100 transition-colors flex items-center justify-center"
                            >
                              +
                            </button>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 p-6 rounded-xl">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-700">Bathrooms</span>
                          <div className="flex items-center gap-3">
                            <button
                              onClick={() => setBookingData({
                                ...bookingData,
                                propertyDetails: {
                                  ...bookingData.propertyDetails,
                                  bathrooms: Math.max(0, bookingData.propertyDetails.bathrooms - 1)
                                }
                              })}
                              className="w-8 h-8 rounded-full bg-white border hover:bg-gray-100 transition-colors flex items-center justify-center"
                            >
                              -
                            </button>
                            <span className="text-2xl font-semibold w-8 text-center">
                              {bookingData.propertyDetails.bathrooms}
                            </span>
                            <button
                              onClick={() => setBookingData({
                                ...bookingData,
                                propertyDetails: {
                                  ...bookingData.propertyDetails,
                                  bathrooms: bookingData.propertyDetails.bathrooms + 1
                                }
                              })}
                              className="w-8 h-8 rounded-full bg-white border hover:bg-gray-100 transition-colors flex items-center justify-center"
                            >
                              +
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Continue Button */}
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleNext}
                    disabled={!isPropertyComplete()}
                    className={`w-full py-4 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 ${
                      isPropertyComplete()
                        ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg'
                        : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {isCalculating ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Calculating Best Options...
                      </>
                    ) : (
                      <>
                        Continue to Service Selection
                        <ArrowRight className="w-5 h-5" />
                      </>
                    )}
                  </motion.button>
                </motion.div>
              )}

              {/* Service Selection Section */}
              {currentSection === 'service' && (
                <motion.div
                  key="service"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-lg p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Choose your service plan</h2>
                    <p className="text-gray-600">Select frequency and schedule that works best for you</p>
                  </div>

                  {/* Service Frequency */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-4">Cleaning Frequency</label>
                    <div className="space-y-4">
                      {frequencies.map((freq) => (
                        <motion.button
                          key={freq.id}
                          whileHover={{ scale: 1.01 }}
                          whileTap={{ scale: 0.99 }}
                          onClick={() => setBookingData({
                            ...bookingData,
                            serviceDetails: { ...bookingData.serviceDetails, frequency: freq.id }
                          })}
                          className={`relative w-full p-6 rounded-xl border-2 transition-all text-left ${
                            bookingData.serviceDetails.frequency === freq.id
                              ? 'border-blue-500 bg-blue-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          {freq.recommended && (
                            <span className="absolute -top-3 right-6 bg-blue-600 text-white text-xs px-3 py-1 rounded-full">
                              Recommended
                            </span>
                          )}
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h3 className="font-semibold text-lg text-gray-900">{freq.name}</h3>
                              <p className="text-gray-600 text-sm">{freq.description}</p>
                            </div>
                            <span className={`font-bold ${
                              freq.id === 'weekly' ? 'text-green-600' :
                              freq.id === 'biweekly' ? 'text-blue-600' :
                              'text-gray-600'
                            }`}>
                              {freq.savings}
                            </span>
                          </div>
                          <div className="flex gap-3">
                            {freq.benefits.map((benefit, index) => (
                              <span key={index} className="text-xs bg-gray-100 px-3 py-1 rounded-full">
                                {benefit}
                              </span>
                            ))}
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Date Selection */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-4">First Cleaning Date</label>
                    <div className="grid grid-cols-7 gap-2">
                      {getAvailableDates().map((date, index) => {
                        const dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                        const dayStr = date.toLocaleDateString('en-US', { weekday: 'short' });
                        const isWeekend = date.getDay() === 0 || date.getDay() === 6;
                        
                        return (
                          <motion.button
                            key={index}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setBookingData({
                              ...bookingData,
                              serviceDetails: { ...bookingData.serviceDetails, preferredDay: dateStr }
                            })}
                            className={`p-3 rounded-lg text-center transition-all ${
                              bookingData.serviceDetails.preferredDay === dateStr
                                ? 'bg-blue-600 text-white shadow-lg'
                                : isWeekend
                                  ? 'bg-gray-100 hover:bg-gray-200'
                                  : 'bg-white border hover:border-blue-300'
                            }`}
                          >
                            <div className="text-xs font-medium">{dayStr}</div>
                            <div className="text-sm font-semibold">{date.getDate()}</div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Time Selection */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-4">Preferred Time</label>
                    <div className="grid grid-cols-2 gap-4">
                      {timeSlots.map((slot) => (
                        <motion.button
                          key={slot.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setBookingData({
                            ...bookingData,
                            serviceDetails: { ...bookingData.serviceDetails, preferredTime: slot.id }
                          })}
                          className={`relative p-5 rounded-xl border-2 transition-all text-left ${
                            bookingData.serviceDetails.preferredTime === slot.id
                              ? 'border-blue-500 bg-blue-50 shadow-md'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                        >
                          {slot.popular && (
                            <span className="absolute -top-2 right-4 bg-orange-500 text-white text-xs px-2 py-0.5 rounded-full">
                              Popular
                            </span>
                          )}
                          <div className="font-medium text-gray-900">{slot.name}</div>
                          <div className="text-sm text-gray-600">{slot.period}</div>
                          <div className="text-xs text-green-600 mt-1">{slot.available} slots available</div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Special Requests */}
                  <div className="mb-8">
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Special Requests (Optional)
                    </label>
                    <textarea
                      placeholder="Any specific areas of focus, allergies, or special instructions..."
                      value={bookingData.serviceDetails.specialRequests}
                      onChange={(e) => setBookingData({
                        ...bookingData,
                        serviceDetails: { ...bookingData.serviceDetails, specialRequests: e.target.value }
                      })}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors resize-none"
                      rows={3}
                    />
                  </div>

                  {/* Navigation Buttons */}
                  <div className="flex gap-4">
                    <button
                      onClick={handleBack}
                      className="px-6 py-3 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors font-medium"
                    >
                      Back
                    </button>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleNext}
                      disabled={!isServiceComplete()}
                      className={`flex-1 py-3 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 ${
                        isServiceComplete()
                          ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-lg'
                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      Continue to Contact Details
                      <ArrowRight className="w-5 h-5" />
                    </motion.button>
                  </div>
                </motion.div>
              )}

              {/* Personal Information Section */}
              {currentSection === 'personal' && (
                <motion.div
                  key="personal"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white rounded-2xl shadow-lg p-8"
                >
                  <div className="mb-8">
                    <h2 className="text-3xl font-bold text-gray-900 mb-2">Almost there!</h2>
                    <p className="text-gray-600">We just need your contact details to confirm your booking</p>
                  </div>

                  {/* Contact Form */}
                  <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input
                          type="text"
                          value={bookingData.personalInfo.firstName}
                          onChange={(e) => setBookingData({
                            ...bookingData,
                            personalInfo: { ...bookingData.personalInfo, firstName: e.target.value }
                          })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                          placeholder="John"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input
                          type="text"
                          value={bookingData.personalInfo.lastName}
                          onChange={(e) => setBookingData({
                            ...bookingData,
                            personalInfo: { ...bookingData.personalInfo, lastName: e.target.value }
                          })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                          placeholder="Doe"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                      <input
                        type="email"
                        value={bookingData.personalInfo.email}
                        onChange={(e) => setBookingData({
                          ...bookingData,
                          personalInfo: { ...bookingData.personalInfo, email: e.target.value }
                        })}
                        className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                      <input
                        type="tel"
                        value={bookingData.personalInfo.phone}
                        onChange={(e) => setBookingData({
                          ...bookingData,
                          personalInfo: { ...bookingData.personalInfo, phone: e.target.value }
                        })}
                        className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                        placeholder="(*************"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Service Address</label>
                      <input
                        type="text"
                        value={bookingData.personalInfo.address}
                        onChange={(e) => setBookingData({
                          ...bookingData,
                          personalInfo: { ...bookingData.personalInfo, address: e.target.value }
                        })}
                        className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                        placeholder="123 Main Street"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">City</label>
                        <input
                          type="text"
                          value={bookingData.personalInfo.city}
                          onChange={(e) => setBookingData({
                            ...bookingData,
                            personalInfo: { ...bookingData.personalInfo, city: e.target.value }
                          })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                          placeholder="New York"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">ZIP Code</label>
                        <input
                          type="text"
                          value={bookingData.personalInfo.zipCode}
                          onChange={(e) => setBookingData({
                            ...bookingData,
                            personalInfo: { ...bookingData.personalInfo, zipCode: e.target.value }
                          })}
                          className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-blue-500 focus:outline-none transition-colors"
                          placeholder="10001"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Terms and Submit */}
                  <div className="mt-8">
                    <div className="mb-6">
                      <label className="flex items-start gap-3">
                        <input type="checkbox" className="mt-1" />
                        <span className="text-sm text-gray-600">
                          I agree to the Terms of Service and Privacy Policy. I understand that CleanPro will contact me to confirm my booking.
                        </span>
                      </label>
                    </div>

                    {/* Navigation Buttons */}
                    <div className="flex gap-4">
                      <button
                        onClick={handleBack}
                        className="px-6 py-3 rounded-xl border border-gray-300 hover:bg-gray-50 transition-colors font-medium"
                      >
                        Back
                      </button>
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={handleSubmit}
                        disabled={!isPersonalComplete()}
                        className={`flex-1 py-3 rounded-xl font-semibold transition-all flex items-center justify-center gap-2 ${
                          isPersonalComplete()
                            ? 'bg-green-600 text-white hover:bg-green-700 shadow-lg'
                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        <CheckCircle className="w-5 h-5" />
                        Complete Booking
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Sidebar */}
          <div className="lg:sticky lg:top-32 h-fit space-y-6">
            {/* Price Summary */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-2xl shadow-lg p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Price Estimate</h3>
                <button
                  onClick={() => setShowPriceBreakdown(!showPriceBreakdown)}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  {showPriceBreakdown ? 'Hide' : 'Show'} details
                </button>
              </div>

              {bookingData.serviceDetails.frequency ? (
                <>
                  <div className="mb-4">
                    <div className="flex items-baseline justify-between">
                      <span className="text-3xl font-bold text-gray-900">${calculatePrice()}</span>
                      <span className="text-gray-600">/cleaning</span>
                    </div>
                    {calculateSavings() > 0 && (
                      <p className="text-green-600 text-sm mt-1">
                        Save ${calculateSavings()} per cleaning
                      </p>
                    )}
                  </div>

                  <AnimatePresence>
                    {showPriceBreakdown && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="border-t pt-4 space-y-2 text-sm"
                      >
                        <div className="flex justify-between text-gray-600">
                          <span>Base cleaning</span>
                          <span>$100</span>
                        </div>
                        <div className="flex justify-between text-gray-600">
                          <span>Property size</span>
                          <span>+${Math.round((calculatePrice() / frequencyDiscount[bookingData.serviceDetails.frequency]) - 100)}</span>
                        </div>
                        {bookingData.serviceDetails.frequency !== 'onetime' && (
                          <div className="flex justify-between text-green-600">
                            <span>Frequency discount</span>
                            <span>-${calculateSavings()}</span>
                          </div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <DollarSign className="w-12 h-12 mx-auto mb-2 opacity-20" />
                  <p>Price will be calculated based on your selections</p>
                </div>
              )}
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-50 rounded-2xl p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Why Choose CleanPro?</h3>
              <div className="space-y-4">
                {trustIndicators.map((indicator, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="text-blue-600 mt-0.5">{indicator.icon}</div>
                    <div>
                      <p className="font-medium text-gray-900">{indicator.text}</p>
                      {indicator.highlight && (
                        <p className="text-sm text-gray-600">{indicator.highlight}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Customer Love */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6"
            >
              <div className="flex items-center gap-2 mb-3">
                <Heart className="w-5 h-5 text-red-500" />
                <h3 className="font-semibold text-gray-900">Customers Love Us</h3>
              </div>
              <div className="space-y-3">
                <div className="bg-white/80 rounded-lg p-3">
                  <div className="flex gap-1 mb-1">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <p className="text-sm text-gray-700">"Best cleaning service I've ever used!"</p>
                  <p className="text-xs text-gray-500 mt-1">- Sarah M.</p>
                </div>
                <div className="text-center">
                  <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                    Read 2,500+ Reviews
                  </button>
                </div>
              </div>
            </motion.div>

            {/* Help Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-center"
            >
              <p className="text-gray-600 mb-2">Need help?</p>
              <div className="space-y-2">
                <button className="flex items-center justify-center gap-2 w-full py-2 text-blue-600 hover:text-blue-700 font-medium">
                  <PhoneCall className="w-4 h-4" />
                  Call 1-800-CLEAN-PRO
                </button>
                <button className="flex items-center justify-center gap-2 w-full py-2 text-gray-600 hover:text-gray-700">
                  <Info className="w-4 h-4" />
                  Live Chat Support
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PremiumBookingForm; 
