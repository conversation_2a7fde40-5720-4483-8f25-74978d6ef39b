import React from 'react';
import { motion } from 'framer-motion';
import { MapPin } from 'lucide-react';

const serviceStates = [
  { name: 'Pennsylvania', abbr: 'PA' },
  { name: 'New York', abbr: 'NY' },
  { name: 'New Jersey', abbr: 'NJ' },
  { name: 'North Carolina', abbr: 'NC' },
  { name: 'Virginia', abbr: 'VA' },
  { name: 'Florida', abbr: 'FL' },
  { name: 'Texas', abbr: 'TX' },
  { name: 'California', abbr: 'CA' }
];

export function ServiceAreas() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-6"
          >
            <MapPin className="w-6 h-6 text-brand-600" />
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-3xl font-bold text-gray-900 mb-6"
          >
            Service Areas
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Serving multiple counties with trusted partners across the United States
          </motion.p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {serviceStates.map((state, index) => (
            <motion.div
              key={state.abbr}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="bg-white p-6 rounded-xl shadow-lg text-center hover:shadow-xl transition-shadow"
            >
              <h3 className="text-2xl font-bold text-brand-600 mb-2">{state.abbr}</h3>
              <p className="text-gray-600">{state.name}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
