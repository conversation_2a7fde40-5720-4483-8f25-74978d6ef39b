import React from 'react';
import { X, ArrowRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { services } from './data/services';

interface ServiceSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onServiceSelect: (serviceId: string) => void;
}

export function ServiceSelectionModal({ isOpen, onClose, onServiceSelect }: ServiceSelectionModalProps) {
  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-hidden">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, y: '100%' }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: '100%' }}
          transition={{ type: 'spring', damping: 25, stiffness: 300 }}
          className="absolute bottom-0 left-0 right-0 max-h-[90vh] overflow-y-auto 
                   bg-white rounded-t-3xl shadow-2xl"
        >
          {/* Header */}
          <div className="sticky top-0 bg-white/80 backdrop-blur-sm border-b border-gray-100 
                        px-4 py-4 flex items-center justify-between z-10">
            <h2 className="text-xl font-semibold text-gray-900">
              Our Services
            </h2>
            <button
              onClick={onClose}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors touch-manipulation"
            >
              <X className="w-6 h-6 text-gray-500" />
            </button>
          </div>

          {/* Services Grid */}
          <div className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {services.map((service, index) => {
                const Icon = service.icon;
                return (
                  <motion.button
                    key={service.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    onClick={() => {
                      onServiceSelect(service.id);
                      onClose();
                    }}
                    className="group relative w-full p-6 rounded-xl text-left transition-all 
                             bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md
                             active:scale-[0.98] touch-manipulation"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="p-3 rounded-lg bg-gray-50 group-hover:bg-brand-50 transition-colors">
                        <Icon className="w-6 h-6 text-gray-600 group-hover:text-brand-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-gray-900 mb-1">
                          {service.title}
                        </h3>
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {service.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-brand-600">
                            {service.price}
                          </span>
                          <ArrowRight className="w-5 h-5 text-brand-600 transform translate-x-0 
                                              group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </div>
                  </motion.button>
                );
              })}
            </div>

            {/* Bottom Padding for Mobile */}
            <div className="h-safe-area-inset-bottom" />
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
