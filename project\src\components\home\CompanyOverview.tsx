import React from 'react';
import { motion } from 'framer-motion';
import { Building2, Shield, Users, Award, MapPin, Star, TrendingUp } from 'lucide-react';

const stats = [
  {
    icon: Shield,
    value: '8+',
    label: 'Years Experience',
    description: 'Trusted industry leader'
  },
  {
    icon: Users,
    value: '250+',
    label: 'Happy Clients',
    description: 'Across multiple states'
  },
  {
    icon: Award,
    value: '98%',
    label: 'Satisfaction Rate',
    description: 'Client happiness score'
  },
  {
    icon: TrendingUp,
    value: '1,400+',
    label: 'Buildings Serviced',
    description: 'Spaces transformed'
  }
];

export function CompanyOverview() {
  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-14 lg:mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-6"
          >
            <Building2 className="w-6 h-6 text-brand-600" />
          </motion.div>
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-2xl sm:text-3xl font-bold text-gray-900 mb-4 sm:mb-6"
          >
            Empire Pro Cleaning Excellence
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Since 2015, we've been transforming commercial spaces with our professional cleaning solutions. Our commitment to excellence has made us a trusted partner for businesses across the nation.
          </motion.p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-brand-50 to-brand-100/50 rounded-2xl transform -rotate-2 group-hover:rotate-0 transition-transform" />
                <div className="relative bg-white p-4 sm:p-6 lg:p-8 rounded-2xl shadow-lg group-hover:shadow-xl transition-all h-full flex flex-col">
                  <div className="p-2 sm:p-3 bg-brand-50 rounded-xl w-fit mb-3 sm:mb-4 group-hover:bg-brand-100 transition-colors">
                    <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-brand-600" />
                  </div>
                  <div className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-1 sm:mb-2">{stat.value}</div>
                  <div className="font-medium text-gray-900 mb-1 text-sm sm:text-base">{stat.label}</div>
                  <div className="text-xs sm:text-sm text-gray-600 flex-1">{stat.description}</div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
