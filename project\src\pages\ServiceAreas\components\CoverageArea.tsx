import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Check, Building2, Users } from 'lucide-react';

const serviceAreas = [
  {
    state: 'New York',
    abbr: 'NY',
    cities: ['Manhattan', 'Brooklyn', 'Queens', 'Bronx', 'Staten Island', 'Buffalo', 'Rochester', 'Syracuse', 'Albany', 'White Plains'],
    stats: {
      clients: '45+',
      buildings: '250+'
    },
    active: true
  },
  {
    state: 'New Jersey',
    abbr: 'NJ',
    cities: ['Jersey City', 'Newark', 'Hoboken', 'Elizabeth', 'Paterson', 'Trenton', 'Princeton', 'Atlantic City', 'Camden', 'Morristown'],
    stats: {
      clients: '35+',
      buildings: '200+'
    },
    active: true
  },
  {
    state: 'Pennsylvania',
    abbr: 'PA',
    cities: ['Philadelphia', 'Pittsburgh', 'Allentown', 'Erie', 'Reading', 'Scranton', 'Harrisburg', 'Lancaster', 'Bethlehem', 'York'],
    stats: {
      clients: '30+',
      buildings: '175+'
    },
    active: true
  },
  {
    state: 'North Carolina',
    abbr: 'NC',
    cities: ['Charlotte', 'Raleigh', 'Durham', 'Greensboro', 'Winston-Salem', 'Wilmington', 'Asheville', 'Chapel Hill', 'Cary', 'High Point'],
    stats: {
      clients: '25+',
      buildings: '150+'
    },
    active: true
  },
  {
    state: 'Virginia',
    abbr: 'VA',
    cities: ['Richmond', 'Virginia Beach', 'Norfolk', 'Alexandria', 'Arlington', 'Roanoke', 'Newport News', 'Chesapeake', 'Fairfax', 'Hampton'],
    stats: {
      clients: '20+',
      buildings: '125+'
    },
    active: true
  },
  {
    state: 'Florida',
    abbr: 'FL',
    cities: ['Miami', 'Orlando', 'Tampa', 'Jacksonville', 'Fort Lauderdale', 'West Palm Beach', 'Boca Raton', 'Naples', 'Sarasota', 'St. Petersburg'],
    stats: {
      clients: '25+',
      buildings: '175+'
    },
    active: true
  },
  {
    state: 'Texas',
    abbr: 'TX',
    cities: ['Dallas', 'Houston', 'Austin', 'San Antonio', 'Fort Worth', 'Plano', 'Irving', 'Arlington', 'Frisco', 'Richardson'],
    stats: {
      clients: '20+',
      buildings: '150+'
    },
    active: true
  },
  {
    state: 'California',
    abbr: 'CA',
    cities: ['Los Angeles', 'San Francisco', 'San Diego', 'San Jose', 'Sacramento', 'Oakland', 'Irvine', 'Santa Monica', 'Beverly Hills', 'Pasadena'],
    stats: {
      clients: '30+',
      buildings: '175+'
    },
    active: true
  }
];

export function CoverageArea() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Our Service Areas
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Providing professional cleaning services across multiple states
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {serviceAreas.map((area, index) => (
            <motion.div
              key={area.state}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-2xl shadow-lg overflow-hidden"
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <MapPin className="w-5 h-5 text-brand-600 mr-2" />
                    <h3 className="text-lg font-semibold text-gray-900">
                      {area.state}
                    </h3>
                  </div>
                  {area.active && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <Check className="w-3 h-3 mr-1" />
                      Active
                    </span>
                  )}
                </div>

                <div className="space-y-2">
                  {area.cities.map((city) => (
                    <div
                      key={city}
                      className="flex items-center text-gray-600 text-sm"
                    >
                      <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                      {city}
                    </div>
                  ))}
                </div>
              </div>

              <div className="px-6 py-4 bg-brand-50">
                <div className="flex justify-between items-center">
                  <div className="flex items-center text-brand-600">
                    <Building2 className="w-4 h-4 mr-1" />
                    <span className="text-sm">{area.stats.buildings} Buildings</span>
                  </div>
                  <div className="flex items-center text-brand-600">
                    <Users className="w-4 h-4 mr-1" />
                    <span className="text-sm">{area.stats.clients} Clients</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
