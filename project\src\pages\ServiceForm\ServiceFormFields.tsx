import React from 'react';
import { WindowCleaningForm } from './forms/WindowCleaningForm';
import { CarpetCleaningForm } from './forms/carpet/CarpetCleaningForm';
import { PostConstructionForm } from './forms/PostConstructionForm';
import { DeepCleaningForm } from './forms/DeepCleaningForm';
import { SanitizationForm } from './forms/SanitizationForm';
import { TileGroutForm } from './forms/TileGroutForm';
import { PressureWashingForm } from './forms/PressureWashingForm';
import { FloorRestorationForm } from './forms/FloorRestorationForm';
import { OfficeCleaningForm } from './forms/OfficeCleaningForm';

interface ServiceFormFieldsProps {
  serviceId: string;
}

export function ServiceFormFields({ serviceId }: ServiceFormFieldsProps) {
  const renderForm = () => {
    switch (serviceId) {
      case 'window':
        return <WindowCleaningForm />;
      case 'carpet':
        return <CarpetCleaningForm />;
      case 'construction':
        return <PostConstructionForm />;
      case 'deep':
        return <DeepCleaningForm />;
      case 'sanitization':
        return <SanitizationForm />;
      case 'tile':
        return <TileGroutForm />;
      case 'pressure':
        return <PressureWashingForm />;
      case 'floor':
        return <FloorRestorationForm />;
      case 'office':
        return <OfficeCleaningForm />;
      default:
        return null;
    }
  };

  return renderForm();
}
