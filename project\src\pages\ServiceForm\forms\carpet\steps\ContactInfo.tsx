import React from 'react';
import { User, Mail, Phone } from 'lucide-react';

interface ContactInfoProps {
  contact: {
    fullName: string;
    email: string;
    phone: string;
  };
  onChange: (contact: any) => void;
}

export function ContactInfo({ contact, onChange }: ContactInfoProps) {
  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-6">
        <div className="p-3 rounded-full bg-brand-100">
          <User className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Contact Information</h3>
          <p className="text-gray-600">How can we reach you?</p>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Full Name
        </label>
        <div className="relative">
          <User className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={contact.fullName}
            onChange={(e) => onChange({ ...contact, fullName: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
            placeholder="Enter your full name"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Email Address
        </label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="email"
            value={contact.email}
            onChange={(e) => onChange({ ...contact, email: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Phone Number
        </label>
        <div className="relative">
          <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="tel"
            value={contact.phone}
            onChange={(e) => onChange({ ...contact, phone: e.target.value })}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
            placeholder="(*************"
          />
        </div>
      </div>
    </div>
  );
}
