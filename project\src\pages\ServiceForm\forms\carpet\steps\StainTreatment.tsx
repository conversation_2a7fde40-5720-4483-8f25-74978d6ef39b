import React from 'react';
import { Upload, AlertCircle } from 'lucide-react';

interface StainTreatmentData {
  types: string[];
  photos: File[];
}

interface StainTreatmentProps {
  treatment: StainTreatmentData;
  onChange: (treatment: StainTreatmentData) => void;
}

export function StainTreatment({ treatment, onChange }: StainTreatmentProps) {
  const handleStainTypeChange = (type: string) => {
    const newTypes = treatment.types.includes(type)
      ? treatment.types.filter(t => t !== type)
      : [...treatment.types, type];
    onChange({ ...treatment, types: newTypes });
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      onChange({ ...treatment, photos: [...treatment.photos, ...Array.from(e.target.files)] });
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-6">
        <div className="p-3 rounded-full bg-brand-100">
          <AlertCircle className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Stain Treatment</h3>
          <p className="text-gray-600">Tell us about any stains that need special attention</p>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-4">
          Select Stain Types
        </label>
        <div className="grid grid-cols-2 gap-4">
          {[
            'Coffee/Tea',
            'Wine',
            'Pet Stains',
            'Food',
            'Ink',
            'Oil/Grease',
            'Unknown',
            'Other'
          ].map((stain) => (
            <label
              key={stain}
              className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                treatment.types.includes(stain.toLowerCase())
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-200'
              }`}
            >
              <input
                type="checkbox"
                className="sr-only"
                checked={treatment.types.includes(stain.toLowerCase())}
                onChange={() => handleStainTypeChange(stain.toLowerCase())}
              />
              <span className="text-gray-700">{stain}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-4">
          Upload Photos (Optional)
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
          <input
            type="file"
            id="stain-photos"
            multiple
            accept="image/*"
            className="hidden"
            onChange={handlePhotoUpload}
          />
          <label
            htmlFor="stain-photos"
            className="flex flex-col items-center cursor-pointer"
          >
            <Upload className="w-8 h-8 text-gray-400 mb-2" />
            <span className="text-sm text-gray-600">
              Click to upload photos of stained areas
            </span>
            <span className="text-xs text-gray-500 mt-1">
              Max 5 photos, 5MB each
            </span>
          </label>
        </div>
        {treatment.photos.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-2">
            {treatment.photos.map((photo, index) => (
              <div
                key={index}
                className="relative bg-gray-100 rounded-lg p-2 text-sm text-gray-600"
              >
                {photo.name}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
