import React from 'react';
import { Building2, Warehouse, ShoppingBag, Building, Droplets, Waves } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceTypeSelectorProps {
  selected: string;
  onChange: (type: string) => void;
}

const serviceTypes = [
  {
    id: 'commercial',
    icon: Building2,
    label: 'Commercial Buildings',
    description: 'Office buildings & business centers'
  },
  {
    id: 'industrial',
    icon: Warehouse,
    label: 'Industrial Facilities',
    description: 'Warehouses & manufacturing plants'
  },
  {
    id: 'retail',
    icon: ShoppingBag,
    label: 'Retail & Restaurant',
    description: 'Storefronts & dining establishments'
  },
  {
    id: 'parking',
    icon: Building,
    label: 'Parking Structures',
    description: 'Parking lots & garages'
  },
  {
    id: 'fleet',
    icon: Droplets,
    label: 'Fleet Washing',
    description: 'Commercial vehicle fleets'
  }
];

export function ServiceTypeSelector({ selected, onChange }: ServiceTypeSelectorProps) {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <div className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-4">
          <Waves className="w-6 h-6 text-brand-600" />
        </div>
        <h3 className="text-2xl font-semibold text-gray-900">
          Select Your Service Type
        </h3>
        <p className="text-gray-600 mt-2">
          Choose the type of pressure washing service you need
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {serviceTypes.map((service, index) => {
          const Icon = service.icon;
          const isSelected = selected === service.id;
          
          return (
            <motion.button
              key={service.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => onChange(service.id)}
              className={`group relative w-full p-6 rounded-xl text-left transition-all duration-200 ${
                isSelected
                  ? 'bg-brand-50 border-2 border-brand-500 shadow-lg'
                  : 'bg-white border border-gray-200 hover:border-brand-300 hover:shadow-md'
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className={`p-3 rounded-lg transition-colors ${
                  isSelected ? 'bg-brand-100' : 'bg-gray-50 group-hover:bg-brand-50'
                }`}>
                  <Icon className={`w-6 h-6 ${
                    isSelected ? 'text-brand-600' : 'text-gray-600'
                  }`} />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">{service.label}</h3>
                  <p className="text-sm text-gray-600">{service.description}</p>
                </div>
              </div>
            </motion.button>
          );
        })}
      </div>
    </div>
  );
}
