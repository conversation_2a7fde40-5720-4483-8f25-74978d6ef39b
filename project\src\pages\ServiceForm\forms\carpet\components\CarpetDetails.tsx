import React from 'react';
import { Brush } from 'lucide-react';

interface CarpetDetailsData {
  material: string;
  condition: string;
  age: string;
  color: string;
  installation: string;
  traffic: string;
}

interface CarpetDetailsProps {
  details: CarpetDetailsData;
  onChange: (details: CarpetDetailsData) => void;
}

export function CarpetDetails({ details, onChange }: CarpetDetailsProps) {
  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Brush className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Carpet Details</h3>
          <p className="text-gray-600">Tell us about your carpet</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Carpet Material <span className="text-red-500">*</span>
          </label>
          <select
            value={details.material}
            onChange={(e) => onChange({ ...details, material: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select material type</option>
            <option value="nylon">Nylon</option>
            <option value="polyester">Polyester</option>
            <option value="wool">Wool</option>
            <option value="olefin">Olefin</option>
            <option value="blend">Blend</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Current Condition <span className="text-red-500">*</span>
          </label>
          <select
            value={details.condition}
            onChange={(e) => onChange({ ...details, condition: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select current condition</option>
            <option value="excellent">Excellent</option>
            <option value="good">Good</option>
            <option value="fair">Fair</option>
            <option value="poor">Poor</option>
            <option value="severe">Severely Soiled</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Carpet Age <span className="text-red-500">*</span>
          </label>
          <select
            value={details.age}
            onChange={(e) => onChange({ ...details, age: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select carpet age</option>
            <option value="0-2">0-2 years</option>
            <option value="3-5">3-5 years</option>
            <option value="6-10">6-10 years</option>
            <option value="10+">10+ years</option>
            <option value="unknown">Unknown</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Carpet Color
          </label>
          <select
            value={details.color}
            onChange={(e) => onChange({ ...details, color: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
            <option value="">Select carpet color</option>
            <option value="light">Light</option>
            <option value="medium">Medium</option>
            <option value="dark">Dark</option>
            <option value="patterned">Patterned</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Installation Type
          </label>
          <select
            value={details.installation}
            onChange={(e) => onChange({ ...details, installation: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
            <option value="">Select installation type</option>
            <option value="glue-down">Glue-Down</option>
            <option value="stretch-in">Stretch-In</option>
            <option value="modular">Modular/Carpet Tiles</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Traffic Level <span className="text-red-500">*</span>
          </label>
          <select
            value={details.traffic}
            onChange={(e) => onChange({ ...details, traffic: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select traffic level</option>
            <option value="light">Light</option>
            <option value="moderate">Moderate</option>
            <option value="heavy">Heavy</option>
            <option value="extreme">Extreme</option>
          </select>
        </div>
      </div>
    </div>
  );
}
