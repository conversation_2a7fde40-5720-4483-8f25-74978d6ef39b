import React from 'react';
import { Check } from 'lucide-react';

interface ProgressBarProps {
  steps: string[];
  currentStep: number;
  color: string;
}

export function ProgressBar({ steps, currentStep, color }: ProgressBarProps) {
  const progress = (currentStep / (steps.length - 1)) * 100;

  return (
    <div className="relative">
      <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
        <div 
          className="h-full rounded-full transition-all duration-500 ease-out"
          style={{ 
            width: `${progress}%`,
            backgroundColor: color
          }}
        />
      </div>
      
      <div className="mt-4 flex justify-between">
        {steps.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          
          return (
            <div 
              key={step}
              className="flex flex-col items-center"
            >
              <div 
                className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
                  isCompleted ? 'bg-green-600' :
                  isCurrent ? 'bg-green-100 border-2 border-green-600' :
                  'bg-gray-100'
                }`}
              >
                {isCompleted ? (
                  <Check className="w-5 h-5 text-white" />
                ) : (
                  <span className={`text-sm ${
                    isCurrent ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    {index + 1}
                  </span>
                )}
              </div>
              <span className={`mt-2 text-xs ${
                isCompleted ? 'text-green-600' :
                isCurrent ? 'text-gray-900 font-medium' :
                'text-gray-400'
              }`}>
                {step}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}
