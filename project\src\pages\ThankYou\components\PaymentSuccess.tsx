import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, CreditCard, Calendar, Clock, Star, Sparkles } from 'lucide-react';

export function PaymentSuccess() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white/40 backdrop-blur-xl border border-white/20 rounded-3xl p-8 mb-8 shadow-2xl relative overflow-hidden"
    >
      {/* Background decorative elements */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-emerald-100/20 rounded-full blur-2xl" />
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-brand-100/20 rounded-full blur-2xl" />
      
      <div className="relative z-10">
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-emerald-100/80 backdrop-blur-xl border border-emerald-200/50 mb-6 relative"
          >
            <CheckCircle className="w-10 h-10 text-emerald-600" />
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="absolute inset-0 rounded-full border-2 border-emerald-400/30"
            />
          </motion.div>
          
          <motion.h3
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="text-3xl font-bold text-gray-900 mb-3"
          >
            Payment Successful
          </motion.h3>
          
          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="text-gray-600 text-lg"
          >
            Your payment has been processed successfully
          </motion.p>
        </div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        >
          <div className="bg-white/60 backdrop-blur-xl p-6 rounded-2xl border border-white/30 hover:bg-white/70 transition-all duration-300">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-brand-100/80 rounded-xl flex items-center justify-center mr-3">
                <CreditCard className="w-5 h-5 text-brand-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Payment Method</h4>
            </div>
            <p className="text-gray-600 font-medium">Credit Card</p>
            <p className="text-sm text-gray-500 mt-1">Secure & Encrypted</p>
          </div>

          <div className="bg-white/60 backdrop-blur-xl p-6 rounded-2xl border border-white/30 hover:bg-white/70 transition-all duration-300">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-emerald-100/80 rounded-xl flex items-center justify-center mr-3">
                <Calendar className="w-5 h-5 text-emerald-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Payment Date</h4>
            </div>
            <p className="text-gray-600 font-medium">{new Date().toLocaleDateString('en-US', {
              weekday: 'short',
              year: 'numeric',
              month: 'short',
              day: 'numeric'
            })}</p>
            <p className="text-sm text-gray-500 mt-1">Transaction Date</p>
          </div>

          <div className="bg-white/60 backdrop-blur-xl p-6 rounded-2xl border border-white/30 hover:bg-white/70 transition-all duration-300">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 bg-amber-100/80 rounded-xl flex items-center justify-center mr-3">
                <Clock className="w-5 h-5 text-amber-600" />
              </div>
              <h4 className="font-semibold text-gray-900">Payment Time</h4>
            </div>
            <p className="text-gray-600 font-medium">{new Date().toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            })}</p>
            <p className="text-sm text-gray-500 mt-1">Processing Time</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1.0 }}
          className="bg-gradient-to-r from-emerald-50/80 to-brand-50/80 backdrop-blur-xl border border-emerald-200/50 rounded-2xl p-6"
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-emerald-100/80 rounded-xl flex items-center justify-center">
                <Star className="w-5 h-5 text-emerald-600" />
              </div>
            </div>
            <div className="ml-4">
              <h4 className="font-semibold text-emerald-900 mb-2">Receipt & Confirmation</h4>
              <p className="text-emerald-700 mb-3">
                A detailed receipt and booking confirmation has been sent to your email address. 
                Please keep it for your records and reference.
              </p>
              <div className="flex items-center space-x-2">
                <Sparkles className="w-4 h-4 text-emerald-600" />
                <span className="text-sm font-medium text-emerald-800">Confirmation ID: #{Math.random().toString(36).substr(2, 9).toUpperCase()}</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
}
