import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, Settings, X, Bot, User, Shield, CreditCard, Calendar, ArrowRight, CheckCircle, Search, Loader, FileText, Paperclip, Mic, Send, Camera } from 'lucide-react';

const VoiceChatWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [flowStep, setFlowStep] = useState(0);
  const [messages, setMessages] = useState([
    { sender: 'ai', text: "Hello! How can I help you today?" }
  ]);
  const [inputValue, setInputValue] = useState('');

  const scriptedMessages = [
    { sender: 'user', text: "I need a deep cleaning for a 3-bedroom house." },
    { sender: 'ai', text: "Of course. I'm analyzing your request and preparing a quote..." },
    { sender: 'ai', text: "Your quote is ready. The total for a deep clean is $249." },
  ];

  const handleOpen = () => {
    setIsOpen(true);
    setFlowStep(1);
    setMessages([{ sender: 'ai', text: "Hello! How can I help you today?" }]);
  };

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(() => {
        setFlowStep(0);
        setMessages([]);
        setInputValue('');
    }, 500);
  };

  const handleSendMessage = () => {
    if (inputValue.trim() === '') return;

    const userMessage = { sender: 'user', text: inputValue };
    const currentMessages = [...messages, userMessage];
    
    // Use the next scripted AI response or a default one
    const aiResponse = scriptedMessages[messages.length -1] || { sender: 'ai', text: "I'm processing that. One moment." };
    
    setMessages([...currentMessages, aiResponse]);
    setInputValue('');
    
    // Advance the flow
    if(flowStep < 4) {
        setFlowStep(prev => prev + 1);
    }
  };

  // Simulate the agent's progress
  useEffect(() => {
    if (isOpen && flowStep > 0 && flowStep < 2) { // Only for first step now
      const timer = setTimeout(() => {
        setMessages(prev => [...prev, scriptedMessages[0]]);
        setFlowStep(prev => prev + 1);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [isOpen, flowStep]);
  
  const timelineSteps = [
    { name: "Requesting", icon: Search },
    { name: "Analyzing", icon: Loader },
    { name: "Quote", icon: FileText },
    { name: "Payment", icon: CreditCard }
  ];

  return (
    <>
              <style>{`
         .voice-widget-metallic-orb {
           background: conic-gradient(from 180deg at 50% 50%, #E5E7EB 0deg, #FFFFFF 72.17deg, #D1D5DB 118.17deg, #F3F4F6 185.62deg, #9CA3AF 253.17deg, #FFFFFF 303.75deg, #E5E7EB 360deg);
           box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.12), inset 0px 2px 8px rgba(255, 255, 255, 0.8);
           animation: spin 10s linear infinite;
         }
         @keyframes spin {
           from { transform: rotate(0deg); }
           to { transform: rotate(360deg); }
         }
         @keyframes pulse-glow {
             0%, 100% { box-shadow: 0 0 20px rgba(16, 185, 129, 0.4); }
             50% { box-shadow: 0 0 40px rgba(16, 185, 129, 0.6); }
         }
         .glass-widget {
           background: rgba(255, 255, 255, 0.08);
           backdrop-filter: blur(20px);
           -webkit-backdrop-filter: blur(20px);
           border: 1px solid rgba(255, 255, 255, 0.15);
           box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
         }
         .glass-modal {
           background: rgba(15, 23, 42, 0.85);
           backdrop-filter: blur(24px);
           -webkit-backdrop-filter: blur(24px);
           border: 1px solid rgba(255, 255, 255, 0.1);
           box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
         }
         .glass-input {
           background: rgba(255, 255, 255, 0.05);
           backdrop-filter: blur(8px);
           -webkit-backdrop-filter: blur(8px);
           border: 1px solid rgba(255, 255, 255, 0.1);
         }
         .glass-input:focus {
             border-color: rgba(16, 185, 129, 0.5);
             box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
         }
         .glass-button {
           background: rgba(255, 255, 255, 0.1);
           backdrop-filter: blur(8px);
           -webkit-backdrop-filter: blur(8px);
           border: 1px solid rgba(255, 255, 255, 0.2);
         }
         .glass-button:hover {
           background: rgba(255, 255, 255, 0.15);
         }
         .user-bubble {
             background: linear-gradient(135deg, rgba(16, 185, 129, 0.8), rgba(5, 150, 105, 0.9));
             backdrop-filter: blur(8px);
             -webkit-backdrop-filter: blur(8px);
             border: 1px solid rgba(255, 255, 255, 0.1);
         }
         .ai-bubble {
             background: rgba(30, 41, 59, 0.6);
             backdrop-filter: blur(8px);
             -webkit-backdrop-filter: blur(8px);
             border: 1px solid rgba(255, 255, 255, 0.1);
         }
         .glass-timeline-active {
             background: linear-gradient(135deg, #10b981, #059669);
             box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
         }
         .glass-timeline-inactive {
             background: rgba(71, 85, 105, 0.4);
             backdrop-filter: blur(8px);
             -webkit-backdrop-filter: blur(8px);
             border: 1px solid rgba(255, 255, 255, 0.1);
         }
         .call-indicator {
             background: rgba(15, 23, 42, 0.9);
             backdrop-filter: blur(16px);
             -webkit-backdrop-filter: blur(16px);
             border: 1px solid rgba(255, 255, 255, 0.1);
         }
         .call-indicator-pulse {
             animation: call-pulse 2s infinite;
         }
         @keyframes call-pulse {
             0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
             70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
             100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
         }
         .glass-payment {
           background: rgba(30, 41, 59, 0.7);
           backdrop-filter: blur(16px);
           -webkit-backdrop-filter: blur(16px);
           border: 1px solid rgba(255, 255, 255, 0.1);
         }
       `}</style>
      
      {/* The Widget */}
      <motion.div onClick={handleOpen} className="cursor-pointer">
        <div className="relative flex justify-center items-center">
                     <motion.div
             className="glass-widget rounded-full p-2 flex items-center justify-between w-full max-w-xl"
           >
             <div className="w-12 h-12 rounded-full voice-widget-metallic-orb ml-2"></div>
             <button className="text-white rounded-full px-12 py-3 flex items-center gap-3 hover:bg-white/5 transition-all duration-300">
               <Phone size={18} />
               <span className="font-semibold text-sm tracking-wider">VOICE CHAT</span>
             </button>
             <button className="glass-button rounded-full p-3 flex items-center justify-center mr-2 w-12 h-12 transition-all duration-300">
               <Settings size={22} className="text-white/80" />
             </button>
          </motion.div>
        </div>
      </motion.div>

      {/* In-Call Indicator */}
      <AnimatePresence>
          {isOpen && (
                             <motion.div 
                 className="fixed top-6 right-6 z-50 flex items-center gap-3 call-indicator rounded-full py-2 px-4"
                 initial={{ opacity: 0, y: -20 }}
                 animate={{ opacity: 1, y: 0 }}
                 exit={{ opacity: 0, y: -20 }}
               >
                   <div className="w-3 h-3 rounded-full bg-emerald-500 call-indicator-pulse"></div>
                   <span className="text-sm text-white/90 font-medium">AI Call Active</span>
               </motion.div>
          )}
      </AnimatePresence>

      {/* The Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm"
            onClick={handleClose}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
                         <motion.div
               onClick={(e) => e.stopPropagation()}
               className="glass-modal w-[95vw] h-auto max-w-md rounded-2xl flex flex-col p-6 relative overflow-hidden"
               initial={{ scale: 0.9, opacity: 0 }}
               animate={{ scale: 1, opacity: 1 }}
               exit={{ scale: 0.9, opacity: 0 }}
               transition={{ type: 'spring', stiffness: 300, damping: 30 }}
             >
              <button onClick={handleClose} className="absolute top-4 right-4 text-gray-400 hover:text-white z-20">
                <X size={20} />
              </button>

              <div className="text-center mb-6">
                <h2 className="text-xl font-bold text-white">Voice Request</h2>
              </div>
              
              {/* Timeline */}
              <div className="flex justify-between items-center mb-6">
                {timelineSteps.map((step, index) => {
                  const isActive = flowStep > index;
                  const isCurrent = flowStep === index + 1;
                  return (
                    <React.Fragment key={step.name}>
                      <div className="flex flex-col items-center gap-2">
                                                 <motion.div
                           className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300
                             ${isActive ? 'glass-timeline-active' : 'glass-timeline-inactive'}`}
                           animate={isCurrent ? { animation: 'pulse-glow 1.5s infinite' } : {}}
                         >
                          {isActive ? <CheckCircle size={20} className="text-white"/> : <step.icon size={20} className={`transition-colors ${isCurrent ? 'text-emerald-400' : 'text-gray-500'}`}/>}
                        </motion.div>
                        <span className={`text-xs font-medium transition-colors ${isActive || isCurrent ? 'text-white' : 'text-gray-500'}`}>{step.name}</span>
                      </div>
                      {index < timelineSteps.length - 1 && (
                        <div className="flex-1 h-1 mx-2 bg-white/10 rounded-full overflow-hidden">
                           <motion.div className="h-full bg-gradient-to-r from-emerald-400 to-emerald-600"
                              initial={{width: "0%"}}
                              animate={{width: flowStep > index + 1 ? "100%" : "0%"}}
                              transition={{duration: 2, ease: "circOut"}}/>
                        </div>
                      )}
                    </React.Fragment>
                  );
                })}
              </div>

              {/* Chat Area */}
              <div className="w-full space-y-4 my-4 max-h-[25vh] overflow-y-auto pr-2">
                {messages.map((msg, index) => (
                  <motion.div
                    key={index}
                    className={`flex items-start gap-3 w-full ${msg.sender === 'user' ? 'justify-end' : ''}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                                         {msg.sender === 'ai' && <div className="w-8 h-8 rounded-full ai-bubble flex-shrink-0 flex items-center justify-center"><Bot size={16} className="text-white/80"/></div>}
                     <div className={`max-w-xs p-3 rounded-lg text-sm ${msg.sender === 'ai' ? 'ai-bubble text-white/90' : 'user-bubble text-white'}`}>
                       {msg.text}
                     </div>
                    {msg.sender === 'user' && <div className="w-8 h-8 rounded-full bg-gray-700 flex-shrink-0 flex items-center justify-center border border-gray-600"><User size={16} className="text-gray-300"/></div>}
                  </motion.div>
                ))}
              </div>

              {/* Informational Notes */}
                                 <div className="my-4 space-y-2 text-xs text-white/60 border-t border-b border-white/10 py-3">
                     <div className="flex items-center gap-2">
                         <Camera size={16} className="text-emerald-400 flex-shrink-0"/>
                         <span>Send photos and videos for the most accurate pricing.</span>
                     </div>
                     <div className="flex items-center gap-2">
                         <Mic size={16} className="text-emerald-400 flex-shrink-0"/>
                         <span>Texting is optional—the AI can understand your voice.</span>
                     </div>
                 </div>

              {/* Chat Input */}
              <div className="mt-auto pt-2">
                                <div className="flex items-center gap-2">
                     <button className="glass-button p-2 rounded-lg hover:bg-white/10 transition-all duration-300">
                         <Paperclip size={20} className="text-white/70"/>
                     </button>
                     <button className="glass-button p-2 rounded-lg hover:bg-white/10 transition-all duration-300">
                         <Mic size={20} className="text-white/70"/>
                     </button>
                     <input
                         type="text"
                         value={inputValue}
                         onChange={(e) => setInputValue(e.target.value)}
                         onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                         placeholder="Type a message..."
                         className="w-full glass-input rounded-lg py-2 px-4 text-sm text-white placeholder-white/40 focus:outline-none"
                     />
                     <button 
                         onClick={handleSendMessage}
                         className="glass-button p-2 rounded-lg hover:bg-emerald-500/20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                         disabled={!inputValue.trim()}
                     >
                         <Send size={20} className="text-white/70"/>
                     </button>
                 </div>
              </div>

              {/* Payment Section */}
              <AnimatePresence>
              {flowStep >= 4 && (
                                 <motion.div 
                     className="absolute bottom-0 left-0 right-0 p-6 glass-payment border-t border-white/10"
                     initial={{ opacity: 0, y: 50 }}
                     animate={{ opacity: 1, y: 0 }}
                     exit={{ opacity: 0, y: 50 }}
                     transition={{ duration: 0.3 }}
                 >
                   <h3 className="text-lg font-semibold text-center mb-4 text-white">Complete Your Booking</h3>
                   <div className="glass-payment p-4 rounded-lg border border-white/10">
                    <div className="flex justify-between items-center text-white mb-4">
                      <span className="text-gray-300">Deep Cleaning (3-bed)</span>
                      <span className="font-bold text-xl text-emerald-400">$249.00</span>
                    </div>
                                         <div className="space-y-3">
                         <div className="relative">
                             <CreditCard className="absolute left-3.5 top-1/2 -translate-y-1/2 text-white/50" size={18}/>
                             <input placeholder="Card Number" className="w-full glass-input rounded-md py-2 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none"/>
                         </div>
                         <div className="grid grid-cols-2 gap-3">
                             <div className="relative">
                                 <Calendar className="absolute left-3.5 top-1/2 -translate-y-1/2 text-white/50" size={18}/>
                                 <input placeholder="MM / YY" className="w-full glass-input rounded-md py-2 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none"/>
                             </div>
                             <div className="relative">
                                 <Shield className="absolute left-3.5 top-1/2 -translate-y-1/2 text-white/50" size={18}/>
                                 <input placeholder="CVC" className="w-full glass-input rounded-md py-2 pl-10 pr-4 text-white placeholder-white/40 focus:outline-none"/>
                             </div>

                        </div>

                    </div>
                  </div>

                  {/* Notes Section */}
                                     <div className="mt-4 space-y-2 text-xs text-white/60">
                     <div className="flex items-start gap-2">
                       <ArrowRight size={14} className="mt-0.5 flex-shrink-0 text-emerald-400"/>
                       <span>Be transparent with the AI for the most accurate quotes.</span>
                     </div>
                     <div className="flex items-start gap-2">
                       <ArrowRight size={14} className="mt-0.5 flex-shrink-0 text-emerald-400"/>
                       <span>Additional charges may apply based on the final scope of work.</span>
                     </div>
                   </div>

                   <button className="w-full mt-4 bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 rounded-lg flex items-center justify-center gap-2 transition-colors shadow-lg shadow-emerald-600/20 hover:shadow-emerald-600/40">
                        <span>Pay & Confirm Booking</span>
                        <ArrowRight size={20}/>
                    </button>
                </motion.div>
              )}
              </AnimatePresence>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default VoiceChatWidget;

