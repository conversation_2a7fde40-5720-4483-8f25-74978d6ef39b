import React from 'react';
import { normalizeServiceType, getServiceDisplayName } from '../lib/services/serviceTypeRegistry';

export function ServiceTypeTest() {
  const testCases = [
    'regular',
    'residential_regular', 
    'deep',
    'residential_deep',
    'office',
    'Regular House Cleaning',
    'Deep House Cleaning',
    'Move-in/Move-out Cleaning',
    'house',
    'home',
    'commercial',
    'business'
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Service Type Normalization Test</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4">Test Results</h2>
        
        <div className="space-y-3">
          {testCases.map((testCase, index) => {
            const normalized = normalizeServiceType(testCase);
            const displayName = getServiceDisplayName(normalized);
            
            return (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div className="flex-1">
                  <span className="font-mono text-sm text-blue-600">"{testCase}"</span>
                </div>
                <div className="flex-1 text-center">
                  <span className="text-gray-600">→</span>
                </div>
                <div className="flex-1">
                  <span className="font-mono text-sm text-green-600">"{normalized}"</span>
                </div>
                <div className="flex-1 text-center">
                  <span className="text-gray-600">→</span>
                </div>
                <div className="flex-1">
                  <span className="font-semibold text-purple-600">"{displayName}"</span>
                </div>
              </div>
            );
          })}
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 rounded">
          <h3 className="font-semibold text-blue-800 mb-2">Expected Results:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• "regular" should normalize to "residential_regular" and display "Regular House Cleaning"</li>
            <li>• "deep" should normalize to "residential_deep" and display "Deep House Cleaning"</li>
            <li>• "office" should stay "office" and display "Office Cleaning"</li>
            <li>• Legacy names should be converted to new format</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

