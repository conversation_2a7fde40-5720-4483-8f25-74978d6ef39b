import React from 'react';
import { motion } from 'framer-motion';
import { Building2, GraduationCap, Stethoscope, ShoppingBag, Building, Factory } from 'lucide-react';

const industries = [
  {
    icon: Building2,
    name: 'Corporate Offices',
    description: 'Professional cleaning for modern office environments',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: Stethoscope,
    name: 'Healthcare Facilities',
    description: 'Specialized cleaning for medical environments',
    color: 'from-green-500 to-green-600'
  },
  {
    icon: GraduationCap,
    name: 'Educational Institutions',
    description: 'Safe and thorough cleaning for schools',
    color: 'from-purple-500 to-purple-600'
  },
  {
    icon: ShoppingBag,
    name: 'Retail & Commercial',
    description: 'Cleaning solutions for retail spaces',
    color: 'from-orange-500 to-orange-600'
  },
  {
    icon: Building,
    name: 'Government Facilities',
    description: 'Secure cleaning for government buildings',
    color: 'from-red-500 to-red-600'
  },
  {
    icon: Factory,
    name: 'Industrial & Manufacturing',
    description: 'Heavy-duty cleaning for industrial spaces',
    color: 'from-teal-500 to-teal-600'
  }
];

export function IndustriesServed() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl font-bold text-gray-900 mb-4"
          >
            Industries We Serve
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-2xl mx-auto"
          >
            Specialized cleaning solutions for every industry
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {industries.map((industry, index) => {
            const Icon = industry.icon;
            
            return (
              <motion.div
                key={industry.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5, scale: 1.02 }}
                className="group relative bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-brand-50 to-brand-100/50 rounded-xl 
                              transform rotate-2 group-hover:rotate-0 transition-transform" />
                <div className="relative">
                  <div className="inline-flex items-center justify-center p-3 bg-brand-100 rounded-xl mb-4 
                                group-hover:scale-110 transition-transform">
                    <Icon className="w-6 h-6 text-brand-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {industry.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {industry.description}
                  </p>
                  
                  {/* Add subtle decoration */}
                  <div className="absolute top-2 right-2 w-12 h-12 bg-brand-50 rounded-full opacity-0 
                                group-hover:opacity-100 transition-opacity" />
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
