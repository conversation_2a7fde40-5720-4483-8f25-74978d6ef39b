import React from 'react';
import { Check } from 'lucide-react';
import { Button } from '../../../components/ui/Button';

const plans = [
  {
    name: 'Basic',
    price: '209',
    description: 'Perfect for small offices',
    features: [
      'Weekly cleaning service',
      'Basic sanitization',
      'Trash removal',
      'Vacuum and mopping',
      'Window sill cleaning'
    ]
  },
  {
    name: 'Professional',
    price: '419',
    description: 'Most popular for medium offices',
    features: [
      'Everything in Basic',
      'Deep carpet cleaning',
      'Restroom sanitization',
      'Kitchen cleaning',
      'Window cleaning',
      'Air vent cleaning'
    ],
    highlighted: true
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    description: 'For large offices and buildings',
    features: [
      'Everything in Professional',
      'Custom cleaning schedule',
      'Dedicated cleaning team',
      'Emergency cleaning service',
      '24/7 support',
      'Monthly deep cleaning'
    ]
  }
];

export function PricingPlans() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900">
            Choose Your Plan
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            Flexible plans to meet your needs
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan) => (
            <div 
              key={plan.name}
              className={`relative rounded-2xl p-8 ${
                plan.highlighted 
                  ? 'bg-brand-50 border-2 border-brand-200' 
                  : 'bg-white border border-gray-200'
              }`}
            >
              {plan.highlighted && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                  <span className="bg-brand-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <h3 className="text-xl font-semibold text-gray-900">{plan.name}</h3>
              <p className="mt-2 text-gray-600">{plan.description}</p>
              
              <div className="mt-6">
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold text-gray-900">
                    {plan.price === 'Custom' ? plan.price : `$${plan.price}`}
                  </span>
                  {plan.price !== 'Custom' && (
                    <span className="ml-2 text-gray-600">/month</span>
                  )}
                </div>
              </div>

              <ul className="mt-8 space-y-4">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-start">
                    <Check className="h-5 w-5 text-brand-600 shrink-0 mr-2" />
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button 
                className="w-full mt-8"
                variant={plan.highlighted ? 'primary' : 'outline'}
              >
                Get Started
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
