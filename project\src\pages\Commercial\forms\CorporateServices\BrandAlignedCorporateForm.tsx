import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

const BrandAlignedCorporateForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isSubmitting, ] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, ] = useState({
    serviceType: '',
    officeSize: '',
    preferredDate: '',
  });

  const calculatePrice = () => 500; 

  const handleSubmit = async () => {
    if (!user) {
      navigate('/auth/login', { state: { from: '/commercial/corporate' } });
      return;
    }
    setShowPaymentModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <h1 className="text-2xl font-bold text-center p-4">Corporate Services Booking</h1>
      {/* A simplified form for demonstration. Will be built out like the office form. */}
      <div className="p-8">
        <p>This is the form for Corporate Services.</p>
        <Button onClick={handleSubmit} disabled={isSubmitting} className="mt-4">
          {isSubmitting ? 'Submitting...' : 'Book Now'}
        </Button>
      </div>

      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description="Corporate Cleaning Service"
          formData={formData}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedCorporateForm; 
