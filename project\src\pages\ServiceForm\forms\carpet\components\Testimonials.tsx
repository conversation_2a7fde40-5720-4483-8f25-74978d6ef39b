import React from 'react';
import { Star, Quote } from 'lucide-react';
import { motion } from 'framer-motion';

const testimonials = [
  {
    author: "<PERSON>",
    role: "Office Manager at TechCorp",
    content: "The carpet cleaning service was exceptional. Our office looks brand new and the team was highly professional.",
    rating: 5
  },
  {
    author: "<PERSON>",
    role: "Facility Manager at MedCenter",
    content: "Outstanding results! They handled our medical facility with the utmost care and attention to sanitization protocols.",
    rating: 5
  },
  {
    author: "<PERSON>",
    role: "Property Manager",
    content: "Reliable, efficient, and thorough. They've been our go-to commercial cleaning service for over 2 years.",
    rating: 5
  }
];

export function Testimonials() {
  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        What Our Clients Say
      </h3>

      <div className="space-y-6">
        {testimonials.map((testimonial, index) => (
          <motion.div
            key={testimonial.author}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.2 }}
            className="relative"
          >
            <div className="absolute -left-2 top-0">
              <Quote className="w-8 h-8 text-brand-100" />
            </div>
            
            <div className="pl-8">
              <div className="flex mb-2">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-4 h-4 text-yellow-400 fill-current"
                  />
                ))}
              </div>
              
              <p className="text-gray-600 text-sm italic mb-3">
                "{testimonial.content}"
              </p>
              
              <div>
                <p className="font-medium text-gray-900">{testimonial.author}</p>
                <p className="text-sm text-gray-500">{testimonial.role}</p>
              </div>
            </div>

            {index < testimonials.length - 1 && (
              <div className="mt-4 border-b border-gray-100" />
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );
}
