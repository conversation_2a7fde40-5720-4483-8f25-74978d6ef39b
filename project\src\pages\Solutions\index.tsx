import React from 'react';
import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { <PERSON> } from './components/Hero';
import { SolutionsGrid } from './components/SolutionsGrid';
import { Industries } from './components/Industries';
import { Features } from './components/Features';
import { Process } from './components/Process';
import { CallToAction } from './components/CallToAction';

export function Solutions() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      <Header />
      <main>
        <Hero />
        <SolutionsGrid />
        <Industries />
        <Features />
        <Process />
        <CallToAction />
      </main>
      <Footer />
    </div>
  );
}
