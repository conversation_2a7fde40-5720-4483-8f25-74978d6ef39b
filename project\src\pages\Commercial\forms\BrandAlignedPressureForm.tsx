import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, 
  ArrowRight, Briefcase, Mail, Phone, Home, User,
  Zap, Droplets, Truck
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { AnimatedBackground } from '../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../components/PaymentOptionsModal';
import GlassmorphismSelect from '../../../components/ui/GlassmorphismSelect';

interface PressureWashingFormData {
  servicePackage: string;
  propertyType: string;
  surfaceType: string;
  areaSize: string;
  cleaningAreas: string[];
  pressureLevel: string;
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedPressureForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState<Partial<PressureWashingFormData>>({
    servicePackage: 'standard',
    propertyType: 'office',
    surfaceType: 'concrete',
    areaSize: 'medium',
    cleaningAreas: [],
    pressureLevel: 'medium',
    serviceFrequency: 'quarterly',
    addOns: [],
  });
  
  useEffect(() => {
    const savedData = localStorage.getItem('pressureWashingFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('pressureWashingFormData', JSON.stringify(formData));
  }, [formData]);

  const calculatePrice = () => {
    let basePrice = 0;
    
    // Base price by service package
    if (formData.servicePackage === 'standard') basePrice = 200;
    else if (formData.servicePackage === 'deep-clean') basePrice = 350;
    else if (formData.servicePackage === 'commercial-contract') basePrice = 500;
    else if (formData.servicePackage === 'emergency') basePrice = 400;
    
    // Area size multiplier
    const areaMultiplier = formData.areaSize === 'small' ? 0.7 : 
                          formData.areaSize === 'medium' ? 1 : 
                          formData.areaSize === 'large' ? 1.5 : 
                          formData.areaSize === 'xl' ? 2.2 : 1;
    
    // Cleaning areas multiplier
    const areasMultiplier = 1 + ((formData.cleaningAreas?.length || 0) * 0.2);
    
    const addOnPrice = (formData.addOns?.length || 0) * 100;
    return Math.round(basePrice * areaMultiplier * areasMultiplier + addOnPrice);
  };

  const steps = [
    { id: 1, name: 'Service Type' },
    { id: 2, name: 'Property & Scope' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact' },
  ];

  const servicePackages = [
    { 
      id: 'standard', 
      name: 'Standard Pressure Wash', 
      icon: <Droplets className="w-8 h-8" />, 
      description: 'Basic exterior cleaning for routine maintenance' 
    },
    { 
      id: 'deep-clean', 
      name: 'Deep Clean Service', 
      icon: <Zap className="w-8 h-8" />, 
      description: 'Intensive cleaning with hot water and chemicals' 
    },
    { 
      id: 'commercial-contract', 
      name: 'Commercial Contract', 
      icon: <Briefcase className="w-8 h-8" />, 
      description: 'Regular scheduled service with discounted rates' 
    },
    { 
      id: 'emergency', 
      name: 'Emergency Response', 
      icon: <Truck className="w-8 h-8" />, 
      description: 'Urgent cleaning for accidents or spills' 
    },
  ];

  const propertyTypes = [
    { id: 'office', name: 'Office Building' },
    { id: 'retail', name: 'Retail Store' },
    { id: 'restaurant', name: 'Restaurant/Food Service' },
    { id: 'warehouse', name: 'Warehouse/Industrial' },
    { id: 'medical', name: 'Medical Facility' },
    { id: 'parking-garage', name: 'Parking Garage' },
  ];

  const surfaceTypes = [
    { id: 'concrete', name: 'Concrete' },
    { id: 'brick', name: 'Brick' },
    { id: 'vinyl-siding', name: 'Vinyl Siding' },
    { id: 'metal', name: 'Metal/Steel' },
    { id: 'stone', name: 'Stone/Masonry' },
    { id: 'composite', name: 'Composite Materials' },
  ];

  const areaSizes = [
    { id: 'small', name: 'Under 1,000 sq ft' },
    { id: 'medium', name: '1,000 - 5,000 sq ft' },
    { id: 'large', name: '5,000 - 10,000 sq ft' },
    { id: 'xl', name: '10,000+ sq ft' },
  ];

  const cleaningAreaOptions = [
    { id: 'building-exterior', name: 'Building Exterior' },
    { id: 'sidewalks', name: 'Sidewalks & Walkways' },
    { id: 'parking-lots', name: 'Parking Lots' },
    { id: 'loading-docks', name: 'Loading Docks' },
    { id: 'dumpster-areas', name: 'Dumpster Areas' },
    { id: 'drive-thru', name: 'Drive-Thru Areas' },
  ];

  const pressureLevels = [
    { id: 'low', name: 'Low Pressure (Soft Wash)' },
    { id: 'medium', name: 'Medium Pressure (Standard)' },
    { id: 'high', name: 'High Pressure (Heavy Duty)' },
    { id: 'variable', name: 'Variable (Mixed Surfaces)' },
  ];
  
  const serviceFrequencies = [
    { id: 'one-time', name: 'One-Time' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly' },
    { id: 'quarterly', name: 'Quarterly' },
    { id: 'seasonal', name: 'Seasonal' },
  ];

  const addOnServices = [
    { id: 'hot-water', name: 'Hot Water Cleaning', description: 'Grease and oil removal', price: 150 },
    { id: 'chemical-treatment', name: 'Chemical Treatment', description: 'Mold and mildew removal', price: 100 },
    { id: 'sealing', name: 'Sealing/Protection', description: 'Surface protection coating', price: 200 },
    { id: 'graffiti-removal', name: 'Graffiti Removal', description: 'Specialized graffiti cleaning', price: 175 },
    { id: 'gum-removal', name: 'Gum Removal', description: 'Sidewalk gum removal', price: 75 },
    { id: 'oil-stain-treatment', name: 'Oil Stain Treatment', description: 'Parking lot oil stains', price: 125 },
  ];

  const timeSlots = [
    { id: 'early-morning', name: 'Early Morning (6AM - 9AM)' },
    { id: 'morning', name: 'Morning (9AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (12PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 8PM)' },
  ];
  
  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  const handleCleaningAreaToggle = (areaId: string) => {
    const currentAreas = formData.cleaningAreas || [];
    if (currentAreas.includes(areaId)) {
      setFormData({
        ...formData,
        cleaningAreas: currentAreas.filter(id => id !== areaId)
      });
    } else {
      setFormData({
        ...formData,
        cleaningAreas: [...currentAreas, areaId]
      });
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    if (!user) {
      navigate('/auth/login', { state: { from: '/commercial/pressure-washing' } });
      return;
    }

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2">
              Commercial Pressure Washing
            </h1>
            <p className="text-gray-200">Professional pressure washing for commercial properties.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= step.id ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-green-500 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sm:p-8" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-white mb-6">Select Service Package</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {servicePackages.map(pkg => (
                      <motion.button 
                        key={pkg.id} 
                        onClick={() => setFormData({...formData, servicePackage: pkg.id})} 
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-300 ${
                          formData.servicePackage === pkg.id 
                            ? 'bg-green-500/20 border-green-400 shadow-lg shadow-green-400/20' 
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center gap-4">
                          <div className="text-green-300">{pkg.icon}</div>
                          <div>
                            <h3 className="font-semibold text-white">{pkg.name}</h3>
                            <p className="text-sm text-gray-300">{pkg.description}</p>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      onClick={() => setCurrentStep(2)} 
                      disabled={!formData.servicePackage}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-white mb-6">Property & Scope Details</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <GlassmorphismSelect
                      options={propertyTypes}
                      value={formData.propertyType}
                      onChange={value => setFormData({...formData, propertyType: value})}
                      placeholder="Property Type"
                    />
                    <GlassmorphismSelect
                      options={surfaceTypes}
                      value={formData.surfaceType}
                      onChange={value => setFormData({...formData, surfaceType: value})}
                      placeholder="Primary Surface"
                    />
                    <GlassmorphismSelect
                      options={areaSizes}
                      value={formData.areaSize}
                      onChange={value => setFormData({...formData, areaSize: value})}
                      placeholder="Area Size"
                    />
                    <GlassmorphismSelect
                      options={pressureLevels}
                      value={formData.pressureLevel}
                      onChange={value => setFormData({...formData, pressureLevel: value})}
                      placeholder="Pressure Level"
                    />
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-white mb-3">Cleaning Areas (Select all that apply)</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                      {cleaningAreaOptions.map(area => (
                        <motion.button
                          key={area.id}
                          onClick={() => handleCleaningAreaToggle(area.id)}
                          className={`p-3 rounded-lg border-2 text-sm transition-all duration-300 ${
                            (formData.cleaningAreas || []).includes(area.id)
                              ? 'bg-green-500/20 border-green-400 text-white'
                              : 'bg-white/5 border-white/20 text-gray-300 hover:border-white/40'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          {area.name}
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <GlassmorphismSelect
                      options={serviceFrequencies}
                      value={formData.serviceFrequency}
                      onChange={value => setFormData({...formData, serviceFrequency: value})}
                      placeholder="Service Frequency"
                    />
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={e => setFormData({...formData, preferredDate: e.target.value})} 
                      className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      min={new Date().toISOString().split('T')[0]}
                    />
                  </div>

                  <div className="mb-6">
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={value => setFormData({...formData, preferredTime: value})}
                      placeholder="Preferred Time"
                    />
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)} 
                      disabled={!formData.propertyType || !formData.surfaceType || !formData.areaSize || !formData.serviceFrequency || !formData.preferredDate || !formData.preferredTime}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-white mb-6">Add-on Services</h2>
                  <p className="text-gray-300 mb-6">Select any additional services you need:</p>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map(addon => (
                      <motion.button 
                        key={addon.id} 
                        onClick={() => handleAddOnToggle(addon.id)} 
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-300 ${
                          (formData.addOns || []).includes(addon.id) 
                            ? 'bg-green-500/20 border-green-400 shadow-lg shadow-green-400/20' 
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-white">{addon.name}</h3>
                          <span className="text-green-400 font-medium">+${addon.price}</span>
                        </div>
                        <p className="text-sm text-gray-300">{addon.description}</p>
                      </motion.button>
                    ))}
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-white mb-6">Contact Information</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Company Name" 
                        value={formData.companyName || ''} 
                        onChange={e => setFormData({...formData, companyName: e.target.value})} 
                        className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      />
                    </div>
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Contact Name" 
                        value={formData.contactName || ''} 
                        onChange={e => setFormData({...formData, contactName: e.target.value})} 
                        className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      />
                    </div>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={e => setFormData({...formData, email: e.target.value})} 
                        className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      />
                    </div>
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={e => setFormData({...formData, phone: e.target.value})} 
                        className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      />
                    </div>
                    <div className="relative sm:col-span-2">
                      <Home className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Property Address" 
                        value={formData.address || ''} 
                        onChange={e => setFormData({...formData, address: e.target.value})} 
                        className="w-full bg-white/10 p-3 pl-12 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      />
                    </div>
                    <input 
                      type="text" 
                      placeholder="City" 
                      value={formData.city || ''} 
                      onChange={e => setFormData({...formData, city: e.target.value})} 
                      className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                    />
                    <input 
                      type="text" 
                      placeholder="ZIP Code" 
                      value={formData.zipCode || ''} 
                      onChange={e => setFormData({...formData, zipCode: e.target.value})} 
                      className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                    />
                  </div>

                  <textarea 
                    placeholder="Special Instructions (water source, access requirements, etc.)" 
                    value={formData.specialInstructions || ''} 
                    onChange={e => setFormData({...formData, specialInstructions: e.target.value})} 
                    className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all resize-none" 
                    rows={4} 
                  />

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!formData.companyName || !formData.contactName || !formData.email || !formData.phone || !formData.address || !formData.city || !formData.zipCode || isSubmitting}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Get Quote <CheckCircle className="ml-2 h-5 w-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Quote Summary */}
          <motion.div 
            className="mt-6 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="text-center">
              <h3 className="text-xl font-bold text-white mb-2">Estimated Quote</h3>
              <div className="text-4xl font-bold text-green-400 mb-2">
                ${calculatePrice()}
              </div>
              <p className="text-white/70 text-sm">
                Final price subject to site inspection
              </p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description="Commercial Pressure Washing Service"
          formData={formData}
          user={user}
        />
      )}
    </AnimatedBackground>
  );
};

export default BrandAlignedPressureForm; 
