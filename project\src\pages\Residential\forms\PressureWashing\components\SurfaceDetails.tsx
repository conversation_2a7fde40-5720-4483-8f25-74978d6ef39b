import React from 'react';
import { Waves, Droplets, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';

interface SurfaceDetailsData {
  surfaceType: string[];
  areaSize: number;
  surfaceMaterial: string;
  stains: boolean;
  mold: boolean;
  lastCleaned: string;
}

interface SurfaceDetailsProps {
  details: SurfaceDetailsData;
  onChange: (details: SurfaceDetailsData) => void;
}

export function SurfaceDetails({ details, onChange }: SurfaceDetailsProps) {
  const surfaceTypes = [
    'Driveway',
    'Patio',
    'Deck',
    'Siding',
    'Fence',
    'Walkway',
    'Pool Deck',
    'Garage Floor'
  ];

  const surfaceMaterials = [
    'Concrete',
    'Brick',
    'Wood',
    'Vinyl',
    'Stone',
    'Pavers',
    'Composite',
    'Other'
  ];

  const lastCleanedOptions = [
    'Never',
    'Within last 6 months',
    'Within last year',
    '1-2 years ago',
    '3+ years ago',
    'Unknown'
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Waves className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Surface Details</h3>
          <p className="text-gray-600">Tell us about the area that needs pressure washing</p>
        </div>
      </motion.div>

      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium text-gray-700">
            What area needs to be pressure washed? <span className="text-red-500">*</span>
          </label>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {surfaceTypes.map((type) => (
              <label
                key={type}
                className={`flex items-center p-3 rounded-lg border-2 transition-all cursor-pointer ${
                  details.surfaceType.includes(type)
                    ? 'border-brand-500 bg-brand-50'
                    : 'border-gray-200 hover:border-brand-300'
                }`}
              >
                <input
                  type="checkbox"
                  checked={details.surfaceType.includes(type)}
                  onChange={(e) => {
                    const newTypes = e.target.checked
                      ? [...details.surfaceType, type]
                      : details.surfaceType.filter(t => t !== type);
                    onChange({ ...details, surfaceType: newTypes });
                  }}
                  className="sr-only"
                />
                <Waves className={`w-5 h-5 mr-2 ${
                  details.surfaceType.includes(type) ? 'text-brand-600' : 'text-gray-400'
                }`} />
                <span className="text-sm">{type}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Approximate square footage or size of the area <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              value={details.areaSize || ''}
              onChange={(e) => onChange({ ...details, areaSize: Number(e.target.value) })}
              min="1"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              What type of surface is it? <span className="text-red-500">*</span>
            </label>
            <select
              value={details.surfaceMaterial}
              onChange={(e) => onChange({ ...details, surfaceMaterial: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select surface material</option>
              {surfaceMaterials.map((material) => (
                <option key={material} value={material}>{material}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <label
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              details.stains
                ? 'border-brand-500 bg-brand-50'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={details.stains}
              onChange={(e) => onChange({ ...details, stains: e.target.checked })}
              className="sr-only"
            />
            <Droplets className={`w-5 h-5 mr-3 ${
              details.stains ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Stubborn stains present</span>
          </label>

          <label
            className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
              details.mold
                ? 'border-brand-500 bg-brand-50'
                : 'border-gray-200 hover:border-brand-300'
            }`}
          >
            <input
              type="checkbox"
              checked={details.mold}
              onChange={(e) => onChange({ ...details, mold: e.target.checked })}
              className="sr-only"
            />
            <AlertCircle className={`w-5 h-5 mr-3 ${
              details.mold ? 'text-brand-600' : 'text-gray-400'
            }`} />
            <span className="text-gray-700">Mold/mildew present</span>
          </label>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              When was the area last cleaned?
            </label>
            <select
              value={details.lastCleaned}
              onChange={(e) => onChange({ ...details, lastCleaned: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            >
              <option value="">Select option</option>
              {lastCleanedOptions.map((option) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        </div>

        {details.mold && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Mold Treatment Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Mold and mildew may require special treatment solutions. Our technicians will assess the severity during service.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
