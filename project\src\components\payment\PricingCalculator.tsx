import React, { useState, useEffect } from 'react';
import { Calculator, DollarSign, Home, Building2 } from 'lucide-react';
import { Button } from '../ui/Button';
import { calculatePrice, formatPrice, type PricingInput } from '../../lib/services/pricingService';
import { normalizeServiceType } from '../../lib/services/serviceTypeRegistry';

interface PricingCalculatorProps {
  initialData?: {
    serviceType: string;
    squareFootage?: number;
    bedrooms?: number;
    bathrooms?: number;
    frequency?: string;
  };
  onGetQuote?: (price: number) => void;
}

export function PricingCalculator({ initialData, onGetQuote }: PricingCalculatorProps) {
  const [serviceType, setServiceType] = useState(initialData?.serviceType || 'regular');
  const [propertyType, setPropertyType] = useState('residential');
  const [squareFootage, setSquareFootage] = useState(initialData?.squareFootage || 1500);
  const [bedrooms, setBedrooms] = useState(initialData?.bedrooms || 3);
  const [bathrooms, setBathrooms] = useState(initialData?.bathrooms || 2);
  const [frequency, setFrequency] = useState(initialData?.frequency || 'one-time');
  const [estimatedPrice, setEstimatedPrice] = useState(0);

  // Calculate price whenever inputs change using centralized pricing service
  useEffect(() => {
    try {
      // Normalize service type to match our registry
      const normalizedServiceType = normalizeServiceType(serviceType);

      // Prepare pricing input
      const pricingInput: PricingInput = {
        serviceType: normalizedServiceType,
        propertySize: squareFootage,
        frequency: frequency === 'one-time' ? 'onetime' : frequency,
        customOptions: {
          bedrooms,
          bathrooms,
          propertyType
        }
      };

      // Calculate price using centralized service
      const pricingResult = calculatePrice(pricingInput);
      setEstimatedPrice(Math.round(pricingResult.total));

    } catch (error) {
      console.error('Error calculating price:', error);
      // Fallback to a reasonable default
      setEstimatedPrice(150);
    }
  }, [serviceType, propertyType, squareFootage, bedrooms, bathrooms, frequency]);

  const handleGetQuote = () => {
    if (onGetQuote) {
      onGetQuote(estimatedPrice);
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-3 rounded-full bg-brand-100">
          <Calculator className="w-6 h-6 text-brand-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900">Price Calculator</h3>
      </div>

      <div className="space-y-6">
        {/* Property Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Property Type
          </label>
          <div className="grid grid-cols-2 gap-4">
            <button
              type="button"
              onClick={() => setPropertyType('residential')}
              className={`flex items-center p-3 rounded-lg border-2 transition-colors ${
                propertyType === 'residential'
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <Home className={`w-5 h-5 mr-2 ${
                propertyType === 'residential' ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span>Residential</span>
            </button>
            
            <button
              type="button"
              onClick={() => setPropertyType('commercial')}
              className={`flex items-center p-3 rounded-lg border-2 transition-colors ${
                propertyType === 'commercial'
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <Building2 className={`w-5 h-5 mr-2 ${
                propertyType === 'commercial' ? 'text-brand-600' : 'text-gray-400'
              }`} />
              <span>Commercial</span>
            </button>
          </div>
        </div>

        {/* Service Type */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Service Type
          </label>
          <select
            value={serviceType}
            onChange={(e) => setServiceType(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
            <option value="regular">Regular Cleaning</option>
            <option value="deep">Deep Cleaning</option>
            <option value="move">Move In/Out Cleaning</option>
                                    <option value="construction">Post-Construction Cleaning</option>
            <option value="carpet">Carpet Cleaning</option>
          </select>
        </div>

        {/* Square Footage */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Square Footage
          </label>
          <input
            type="number"
            value={squareFootage}
            onChange={(e) => setSquareFootage(Number(e.target.value))}
            min={100}
            step={100}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          />
        </div>

        {propertyType === 'residential' && (
          <div className="grid grid-cols-2 gap-4">
            {/* Bedrooms */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Bedrooms
              </label>
              <input
                type="number"
                value={bedrooms}
                onChange={(e) => setBedrooms(Number(e.target.value))}
                min={0}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>

            {/* Bathrooms */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Bathrooms
              </label>
              <input
                type="number"
                value={bathrooms}
                onChange={(e) => setBathrooms(Number(e.target.value))}
                min={0}
                step={0.5}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* Frequency */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Service Frequency
          </label>
          <select
            value={frequency}
            onChange={(e) => setFrequency(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          >
            <option value="one-time">One-Time Service</option>
            <option value="weekly">Weekly</option>
            <option value="bi-weekly">Bi-Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>

        {/* Price Estimate */}
        <div className="p-4 bg-brand-50 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="text-brand-800 font-medium">Estimated Price:</span>
            <div className="flex items-center">
              <DollarSign className="w-5 h-5 text-brand-600" />
              <span className="text-2xl font-bold text-brand-800">{estimatedPrice}</span>
            </div>
          </div>
          <p className="text-sm text-brand-600 mt-2">
            Final price may vary based on inspection and additional services
          </p>
        </div>

        {/* Get Quote Button */}
        {onGetQuote && (
          <Button
            onClick={handleGetQuote}
            className="w-full"
          >
            Get Personalized Quote
          </Button>
        )}
      </div>
    </div>
  );
}
