import React from 'react';
import { Calendar, Clock, KeyRound, DollarSign } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceSchedulingProps {
  schedule: {
    date: string;
    timeSlot: string;
    access: string;
    specialInstructions: string;
    promoCode: string;
  };
  onChange: (schedule: any) => void;
}

export function ServiceScheduling({ schedule, onChange }: ServiceSchedulingProps) {
  const today = new Date().toISOString().split('T')[0];
  
  const timeSlots = [
    { value: 'morning', label: 'Morning (8AM - 12PM)' },
    { value: 'afternoon', label: 'Afternoon (12PM - 4PM)' },
    { value: 'evening', label: 'Evening (4PM - 8PM)' }
  ];



  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Calendar className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Schedule Service</h3>
          <p className="text-gray-600">Choose your service date and time</p>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div 
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Service Date <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="date"
              value={schedule.date}
              onChange={(e) => onChange({ ...schedule, date: e.target.value })}
              min={today}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="space-y-2"
        >
          <label className="block text-sm font-medium text-gray-700">
            Preferred Time <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={schedule.timeSlot}
              onChange={(e) => onChange({ ...schedule, timeSlot: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select time slot</option>
              {timeSlots.map((slot) => (
                <option key={slot.value} value={slot.value}>
                  {slot.label}
                </option>
              ))}
            </select>
          </div>
        </motion.div>
      </div>



      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="space-y-2"
      >
        <label className="block text-sm font-medium text-gray-700">
          Access Instructions
        </label>
        <div className="relative">
          <KeyRound className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
          <textarea
            value={schedule.access}
            onChange={(e) => onChange({ ...schedule, access: e.target.value })}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            rows={3}
            placeholder="Any special instructions for accessing your home? (garage code, lockbox, etc.)"
          />
        </div>
      </motion.div>

      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-2"
      >
        <label className="block text-sm font-medium text-gray-700">
          Special Instructions
        </label>
        <textarea
          value={schedule.specialInstructions}
          onChange={(e) => onChange({ ...schedule, specialInstructions: e.target.value })}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
          rows={3}
          placeholder="Any additional instructions or requests for our technicians?"
        />
      </motion.div>

      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="space-y-2"
      >
        <label className="block text-sm font-medium text-gray-700">
          Promo Code
        </label>
        <div className="relative">
          <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={schedule.promoCode}
            onChange={(e) => onChange({ ...schedule, promoCode: e.target.value })}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            placeholder="Enter promo code if you have one"
          />
        </div>
      </motion.div>
    </div>
  );
}
