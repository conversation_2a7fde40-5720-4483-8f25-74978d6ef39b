import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { Mail, Lock, AlertCircle, X } from 'lucide-react';
import { useAuth } from '../../lib/auth/AuthProvider';
import { Button } from '../../components/ui/Button';
import { checkSupabaseConnection } from '../../lib/supabase/client';

export function Login() {
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, pendingFormData } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Check for intended redirect destination
  const redirectAfterLogin = localStorage.getItem('redirectAfterLogin');
  const from = location.state?.from?.pathname || redirectAfterLogin || '/accountdashboard';

  // Clear error when inputs change
  useEffect(() => {
    if (error) setError('');
  }, [email, password]);

  // Pre-fill email if coming from form submission
  useEffect(() => {
    if (pendingFormData?.contact?.email) {
      setEmail(pendingFormData.contact.email);
    }
  }, [pendingFormData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError('');

      // Check Supabase connection before attempting sign in
      await checkSupabaseConnection();
      
      await signIn(email, password);
      
      // Clear the redirect after login flag if it was used
      if (redirectAfterLogin) {
        localStorage.removeItem('redirectAfterLogin');
      }
      
      // If we have pending form data, redirect to thank you page
      if (pendingFormData) {
        navigate('/thank-you', { 
          state: { formData: pendingFormData },
          replace: true 
        });
      } else {
        // Otherwise go to dashboard or original destination
        navigate(from, { replace: true });
      }
    } catch (err) {
      if (err instanceof Error) {
        // Provide more user-friendly error messages
        if (err.message.includes('Failed to connect')) {
          setError('Unable to connect to the authentication service. Please check your internet connection and try again.');
        } else if (err.message.includes('not configured')) {
          setError('Authentication service is not properly configured. Please ensure you are connected to Supabase.');
        } else if (err.message.includes('Invalid login')) {
          setError('Invalid email or password. Please try again.');
        } else if (err.message.includes('user_already_exists')) {
          setError('An account with this email already exists. Please sign in instead.');
        } else {
          setError(err.message);
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    // If we have pending form data, go back to form
    if (location.state?.from === '/thank-you' && pendingFormData) {
      navigate('/thank-you', { 
        state: { formData: pendingFormData },
        replace: true 
      });
    } else {
      navigate(-1);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md relative">
        <button
          onClick={handleClose}
          className="absolute -top-4 -right-4 p-2 rounded-full bg-white shadow-lg hover:bg-gray-50 transition-colors"
        >
          <X className="w-5 h-5 text-gray-500" />
        </button>

        <h2 className="text-center text-3xl font-bold text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Or{' '}
          <Link to="/auth/register" className="font-medium text-brand-600 hover:text-brand-500">
            create a new account
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
          {error && (
            <div className="mb-4 p-4 bg-red-50 rounded-md flex items-center text-red-700">
              <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="email"
                  type="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                  placeholder="<EMAIL>"
                  disabled={loading}
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  id="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 block w-full border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-brand-500 focus:border-brand-500"
                  placeholder="Enter your password"
                  disabled={loading}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-brand-600 focus:ring-brand-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link to="/auth/forgot-password" className="font-medium text-brand-600 hover:text-brand-500">
                  Forgot your password?
                </Link>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full flex justify-center py-3"
              disabled={loading}
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </Button>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  Need help?
                </span>
              </div>
            </div>

            <div className="mt-6 text-center text-sm">
              <p className="text-gray-600">
                Contact support at{' '}
                <a href="tel:+17187171502" className="font-medium text-brand-600 hover:text-brand-500">
                  (*************
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
