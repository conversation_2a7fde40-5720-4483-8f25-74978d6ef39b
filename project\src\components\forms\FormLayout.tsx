import React from 'react';
import { motion } from 'framer-motion';

interface FormLayoutProps {
  children: React.ReactNode;
  title: string;
  description?: string;
  icon?: React.ReactNode;
}

export function FormLayout({ children, title, description, icon }: FormLayoutProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-lg p-8 mb-8"
    >
      <div className="flex items-center space-x-4 mb-8">
        {icon && (
          <div className="p-3 rounded-full bg-brand-100">
            {icon}
          </div>
        )}
        <div>
          <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
          {description && (
            <p className="text-gray-600 mt-1">{description}</p>
          )}
        </div>
      </div>

      <div className="space-y-8">
        {children}
      </div>
    </motion.div>
  );
}
