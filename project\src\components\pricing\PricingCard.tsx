import React from 'react';
import { Check } from 'lucide-react';
import { Button } from '../ui/Button';

interface PricingCardProps {
  plan: {
    name: string;
    price: string;
    description: string;
    features: string[];
    highlighted?: boolean;
  };
}

export function PricingCard({ plan }: PricingCardProps) {
  return (
    <div 
      className={`relative rounded-2xl p-8 ${
        plan.highlighted 
          ? 'bg-brand-50 border-2 border-brand-200' 
          : 'bg-white border border-gray-200'
      }`}
    >
      {plan.highlighted && (
        <div className="absolute -top-4 left-1/2 -translate-x-1/2">
          <span className="bg-brand-600 text-white px-4 py-1 rounded-full text-sm font-medium">
            Most Popular
          </span>
        </div>
      )}

      <h3 className="text-xl font-semibold text-gray-900">{plan.name}</h3>
      <p className="mt-2 text-gray-600">{plan.description}</p>
      
      <div className="mt-6">
        <div className="flex items-baseline">
          <span className="text-4xl font-bold text-gray-900">
            {plan.price === 'Custom' ? plan.price : `$${plan.price}`}
          </span>
          {plan.price !== 'Custom' && (
            <span className="ml-2 text-gray-600">/month</span>
          )}
        </div>
      </div>

      <ul className="mt-8 space-y-4">
        {plan.features.map((feature) => (
          <li key={feature} className="flex items-start">
            <Check className="h-5 w-5 text-brand-600 shrink-0 mr-2" />
            <span className="text-gray-600">{feature}</span>
          </li>
        ))}
      </ul>

      <Button 
        className="w-full mt-8"
        variant={plan.highlighted ? 'primary' : 'outline'}
      >
        Get Started
      </Button>
    </div>
  );
}
