import React from 'react';
import { Slider } from '../ui/Slider';
import { Counter } from '../ui/Counter';
import { OfficeVisualization } from '../ui/OfficeVisualization';
import type { OfficeDetailsType } from '../types';

interface OfficeDetailsProps {
  details: OfficeDetailsType;
  onChange: (details: OfficeDetailsType) => void;
}

export function OfficeDetails({ details, onChange }: OfficeDetailsProps) {
  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-medium mb-6">Office Details</h2>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Square Footage: {details.squareFootage} sq ft
          </label>
          <Slider
            min={500}
            max={10000}
            step={100}
            value={details.squareFootage}
            onChange={(value) => onChange({ ...details, squareFootage: value })}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Number of Employees
          </label>
          <Counter
            value={details.employeeCount}
            onChange={(value) => onChange({ ...details, employeeCount: value })}
            min={1}
            max={100}
          />
        </div>
      </div>

      <OfficeVisualization
        squareFootage={details.squareFootage}
        employeeCount={details.employeeCount}
      />
    </div>
  );
}
