import React from 'react';
import { Calendar, Clock, AlertTriangle } from 'lucide-react';
import { motion } from 'framer-motion';

interface ServiceSchedulingProps {
  schedule: {
    date: string;
    timeSlot: string;
    projectDeadline: string;
    urgency: string;
  };
  onChange: (schedule: any) => void;
}

export function ServiceScheduling({ schedule, onChange }: ServiceSchedulingProps) {
  const today = new Date().toISOString().split('T')[0];
  
  const timeSlots = [
    { value: 'early-morning', label: 'Early Morning (5AM - 8AM)' },
    { value: 'morning', label: 'Morning (8AM - 12PM)' },
    { value: 'afternoon', label: 'Afternoon (12PM - 4PM)' },
    { value: 'evening', label: 'Evening (4PM - 8PM)' },
    { value: 'night', label: 'Night (8PM - 12AM)' },
    { value: 'after-hours', label: 'After Hours (12AM - 5AM)' }
  ];

  const urgencyLevels = [
    { value: 'normal', label: 'Normal', description: 'Standard scheduling' },
    { value: 'priority', label: 'Priority', description: 'Within 48 hours' },
    { value: 'urgent', label: 'Urgent', description: 'Next day service' }
  ];

  return (
    <div className="space-y-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-4 mb-8"
      >
        <div className="p-3 rounded-full bg-brand-100">
          <Calendar className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Schedule Service</h3>
          <p className="text-gray-600">Choose your preferred date and time</p>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Service Date <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="date"
              value={schedule.date}
              onChange={(e) => onChange({ ...schedule, date: e.target.value })}
              min={today}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Time Slot <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <select
              value={schedule.timeSlot}
              onChange={(e) => onChange({ ...schedule, timeSlot: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              required
            >
              <option value="">Select time slot</option>
              {timeSlots.map((slot) => (
                <option key={slot.value} value={slot.value}>
                  {slot.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Project Deadline <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="date"
            value={schedule.projectDeadline}
            onChange={(e) => onChange({ ...schedule, projectDeadline: e.target.value })}
            min={today}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          />
        </div>
      </div>

      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Service Urgency
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {urgencyLevels.map((level) => (
            <label
              key={level.value}
              className={`flex flex-col p-4 rounded-lg border-2 transition-all cursor-pointer ${
                schedule.urgency === level.value
                  ? 'border-brand-500 bg-brand-50'
                  : 'border-gray-200 hover:border-brand-300'
              }`}
            >
              <input
                type="radio"
                name="urgency"
                value={level.value}
                checked={schedule.urgency === level.value}
                onChange={(e) => onChange({ ...schedule, urgency: e.target.value })}
                className="sr-only"
              />
              <div className="font-medium text-gray-900">{level.label}</div>
              <p className="text-sm text-gray-600 mt-1">{level.description}</p>
            </label>
          ))}
        </div>

        {schedule.urgency !== 'normal' && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-yellow-800">Priority Service Notice</h4>
                <p className="mt-1 text-sm text-yellow-700">
                  Additional fees may apply for expedited service. Our team will contact you to confirm availability.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
