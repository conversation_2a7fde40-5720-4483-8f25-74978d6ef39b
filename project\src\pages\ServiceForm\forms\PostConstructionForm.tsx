import React from 'react';
import { Form, FormField } from '../components/FormFields';

export function PostConstructionForm() {
  return (
    <Form onSubmit={(data) => console.log(data)}>
      <FormField label="Project Type" required>
        <select
          name="projectType"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        >
          <option value="renovation">Renovation</option>
          <option value="new-construction">New Construction</option>
          <option value="remodel">Remodel</option>
          <option value="addition">Addition</option>
        </select>
      </FormField>

      <FormField label="Square Footage" required>
        <input
          type="number"
          name="squareFootage"
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          required
        />
      </FormField>

      <FormField label="Required Services">
        <div className="space-y-2">
          {[
            'Dust Removal',
            'Paint Overspray Cleanup',
            'Window Cleaning',
            'Floor Cleaning',
            'Debris Removal',
            'HVAC Cleaning'
          ].map((service) => (
            <label key={service} className="flex items-center">
              <input
                type="checkbox"
                name="services"
                value={service.toLowerCase().replace(/\s+/g, '-')}
                className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
              />
              <span className="ml-2 text-gray-700">{service}</span>
            </label>
          ))}
        </div>
      </FormField>

      <FormField label="Special Instructions">
        <textarea
          name="instructions"
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg"
          placeholder="Any specific requirements or access restrictions?"
        />
      </FormField>
    </Form>
  );
}
