import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, 
  ArrowRight, Briefcase, Mail, Phone, Home, User,
  ChefHat, Flame, Truck, Utensils
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';

interface KitchenFormData {
  servicePackage: string;
  kitchenType: string;
  kitchenSize: string;
  cleaningLevel: string;
  cleaningAreas: string[];
  equipmentTypes: string[];
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedKitchenForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [formData, setFormData] = useState<Partial<KitchenFormData>>({
    servicePackage: 'standard',
    kitchenType: 'restaurant',
    kitchenSize: 'medium',
    cleaningLevel: 'standard',
    cleaningAreas: [],
    equipmentTypes: [],
    serviceFrequency: 'weekly',
    addOns: [],
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    specialInstructions: '',
  });
  
  useEffect(() => {
    const savedData = localStorage.getItem('kitchenFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('kitchenFormData', JSON.stringify(formData));
  }, [formData]);

  const calculatePrice = () => {
    let basePrice = 0;
    
    // Base price by service package
    if (formData.servicePackage === 'standard') basePrice = 400;
    else if (formData.servicePackage === 'deep-clean') basePrice = 650;
    else if (formData.servicePackage === 'hood-exhaust') basePrice = 800;
    else if (formData.servicePackage === 'emergency') basePrice = 750;
    
    // Kitchen size multiplier
    const sizeMultiplier = formData.kitchenSize === 'small' ? 0.7 : 
                          formData.kitchenSize === 'medium' ? 1 : 
                          formData.kitchenSize === 'large' ? 1.8 : 
                          formData.kitchenSize === 'xl' ? 2.8 : 1;
    
    // Cleaning areas multiplier
    const areasMultiplier = 1 + ((formData.cleaningAreas?.length || 0) * 0.12);
    
    // Equipment types multiplier
    const equipmentMultiplier = 1 + ((formData.equipmentTypes?.length || 0) * 0.15);
    
    // Cleaning level multiplier
    const levelMultiplier = formData.cleaningLevel === 'deep-clean' ? 1.5 : 1;
    
    const addOnPrice = (formData.addOns?.length || 0) * 150;
    return Math.round(basePrice * sizeMultiplier * areasMultiplier * equipmentMultiplier * levelMultiplier + addOnPrice);
  };

  const steps = [
    { id: 1, name: 'Service Type' },
    { id: 2, name: 'Kitchen & Scope' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact' },
  ];

  const servicePackages = [
    { 
      id: 'standard', 
      name: 'Standard Kitchen Clean', 
      icon: <ChefHat className="w-8 h-8" />, 
      description: 'Regular kitchen maintenance and sanitation' 
    },
    { 
      id: 'deep-clean', 
      name: 'Deep Kitchen Clean', 
      icon: <Flame className="w-8 h-8" />, 
      description: 'Intensive degreasing and equipment cleaning' 
    },
    { 
      id: 'hood-exhaust', 
      name: 'Hood & Exhaust System', 
      icon: <Briefcase className="w-8 h-8" />, 
      description: 'Complete hood, duct, and exhaust system cleaning' 
    },
    { 
      id: 'emergency', 
      name: 'Emergency Response', 
      icon: <Truck className="w-8 h-8" />, 
      description: 'Urgent cleaning for health inspections or incidents' 
    },
  ];

  const kitchenTypes = [
    { id: 'restaurant', name: 'Restaurant' },
    { id: 'fast-food', name: 'Fast Food' },
    { id: 'cafeteria', name: 'Cafeteria' },
    { id: 'bakery', name: 'Bakery' },
    { id: 'catering', name: 'Catering Kitchen' },
    { id: 'food-truck', name: 'Food Truck' },
    { id: 'hotel', name: 'Hotel Kitchen' },
    { id: 'hospital', name: 'Hospital Kitchen' },
  ];

  const kitchenSizes = [
    { id: 'small', name: 'Small (Under 500 sq ft)' },
    { id: 'medium', name: 'Medium (500 - 1,500 sq ft)' },
    { id: 'large', name: 'Large (1,500 - 3,000 sq ft)' },
    { id: 'xl', name: 'Extra Large (3,000+ sq ft)' },
  ];

  const cleaningLevels = [
    { id: 'standard', name: 'Standard Cleaning' },
    { id: 'deep-clean', name: 'Deep Clean (+50%)' },
  ];

  const cleaningAreaOptions = [
    { id: 'cooking-line', name: 'Cooking Line' },
    { id: 'prep-areas', name: 'Prep Areas' },
    { id: 'dishwashing', name: 'Dishwashing Area' },
    { id: 'storage-areas', name: 'Storage Areas' },
    { id: 'walk-in-coolers', name: 'Walk-in Coolers/Freezers' },
    { id: 'dining-area', name: 'Dining Area' },
    { id: 'restrooms', name: 'Restrooms' },
    { id: 'office-areas', name: 'Office Areas' },
  ];

  const equipmentTypeOptions = [
    { id: 'fryers', name: 'Fryers' },
    { id: 'grills', name: 'Grills' },
    { id: 'ovens', name: 'Ovens' },
    { id: 'hood-systems', name: 'Hood Systems' },
    { id: 'dishwashers', name: 'Dishwashers' },
    { id: 'ice-machines', name: 'Ice Machines' },
    { id: 'refrigeration', name: 'Refrigeration Units' },
    { id: 'exhaust-fans', name: 'Exhaust Fans' },
  ];
  
  const serviceFrequencies = [
    { id: 'daily', name: 'Daily' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly' },
    { id: 'quarterly', name: 'Quarterly' },
    { id: 'one-time', name: 'One-Time' },
  ];

  const addOnServices = [
    { id: 'grease-trap', name: 'Grease Trap Cleaning', description: 'Deep cleaning and maintenance of grease traps', price: 200 },
    { id: 'floor-degreasing', name: 'Floor Degreasing', description: 'Specialized floor cleaning and degreasing', price: 150 },
    { id: 'ceiling-cleaning', name: 'Ceiling & Light Cleaning', description: 'Complete ceiling and fixture cleaning', price: 180 },
    { id: 'equipment-calibration', name: 'Equipment Calibration', description: 'Temperature and equipment calibration check', price: 250 },
    { id: 'pest-control', name: 'Pest Control Treatment', description: 'Professional pest prevention treatment', price: 175 },
    { id: 'compliance-documentation', name: 'Compliance Documentation', description: 'Health department compliance paperwork', price: 100 },
  ];

  const timeSlots = [
    { id: 'early-morning', name: 'Early Morning (4AM - 7AM)' },
    { id: 'morning', name: 'Morning (7AM - 11AM)' },
    { id: 'afternoon', name: 'Afternoon (11AM - 3PM)' },
    { id: 'evening', name: 'Evening (3PM - 7PM)' },
    { id: 'overnight', name: 'Overnight (10PM - 4AM)' },
  ];

  const validateField = (fieldName: string, value: string) => {
    const errors = { ...validationErrors };
    
    switch (fieldName) {
      case 'email': {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (value && !emailRegex.test(value)) {
          errors.email = 'Please enter a valid email address';
        } else {
          delete errors.email;
        }
        break;
      }
      case 'phone': {
        const phoneRegex = /\d{10,}/;
        if (value && !phoneRegex.test(value.replace(/\D/g, ''))) {
          errors.phone = 'Please enter a valid phone number (at least 10 digits)';
        } else {
          delete errors.phone;
        }
        break;
      }
      case 'companyName':
        if (!value || value.trim().length < 2) {
          errors.companyName = 'Company name must be at least 2 characters';
        } else {
          delete errors.companyName;
        }
        break;
      case 'contactName':
        if (!value || value.trim().length < 2) {
          errors.contactName = 'Contact name must be at least 2 characters';
        } else {
          delete errors.contactName;
        }
        break;
      case 'address':
        if (!value || value.trim().length < 5) {
          errors.address = 'Please enter a valid address';
        } else {
          delete errors.address;
        }
        break;
      case 'city':
        if (!value || value.trim().length < 2) {
          errors.city = 'Please enter a valid city';
        } else {
          delete errors.city;
        }
        break;
      case 'zipCode': {
        const zipRegex = /^\d{5}(-\d{4})?$/;
        if (!value || !zipRegex.test(value)) {
          errors.zipCode = 'Please enter a valid ZIP code (12345 or 12345-6789)';
        } else {
          delete errors.zipCode;
        }
        break;
      }
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateForm = () => {
    const fieldsToValidate = ['companyName', 'contactName', 'email', 'phone', 'address', 'city', 'zipCode'];
    let isValid = true;
    
    fieldsToValidate.forEach(field => {
      const fieldValue = formData[field as keyof KitchenFormData] as string || '';
      if (!validateField(field, fieldValue)) {
        isValid = false;
      }
    });
    
    return isValid;
  };
  
  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  const handleCleaningAreaToggle = (areaId: string) => {
    const currentAreas = formData.cleaningAreas || [];
    if (currentAreas.includes(areaId)) {
      setFormData({
        ...formData,
        cleaningAreas: currentAreas.filter(id => id !== areaId)
      });
    } else {
      setFormData({
        ...formData,
        cleaningAreas: [...currentAreas, areaId]
      });
    }
  };

  const handleEquipmentToggle = (equipmentId: string) => {
    const currentEquipment = formData.equipmentTypes || [];
    if (currentEquipment.includes(equipmentId)) {
      setFormData({
        ...formData,
        equipmentTypes: currentEquipment.filter(id => id !== equipmentId)
      });
    } else {
      setFormData({
        ...formData,
        equipmentTypes: [...currentEquipment, equipmentId]
      });
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      // Simulate API call to save the estimate and schedule
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Clear form data from localStorage
      localStorage.removeItem('kitchenFormData');
      
      // Navigate to user dashboard to view booking
      navigate('/accountdashboard', { 
        state: { 
          newBooking: {
            id: Date.now(), // Temporary ID until backend assigns real ID
            serviceType: 'Commercial Kitchen Cleaning',
            estimate: calculatePrice(),
            status: 'Pending Confirmation',
            date: formData.preferredDate,
            time: formData.preferredTime,
            facility: formData.kitchenType,
            package: formData.servicePackage,
            address: `${formData.address}, ${formData.city}, ${formData.zipCode}`,
            contact: {
              company: formData.companyName,
              name: formData.contactName,
              email: formData.email,
              phone: formData.phone
            },
            createdAt: new Date().toISOString(),
            message: 'Your kitchen cleaning service has been scheduled! We will contact you within 24 hours to confirm the appointment details.'
          }
        } 
      });
    } catch (error) {
      console.error('Error submitting form:', error);
      setValidationErrors({ submit: 'There was an error submitting your request. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2">
              Commercial Kitchen Cleaning
            </h1>
            <p className="text-gray-200">Professional kitchen cleaning and sanitation for commercial food service.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= step.id ? 'bg-green-500 text-white' : 'bg-white/20 text-gray-400'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-white' : 'text-gray-400'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-white/20 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-green-500 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 sm:p-8" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-white mb-6">Select Service Package</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {servicePackages.map(pkg => (
                      <motion.button 
                        key={pkg.id} 
                        onClick={() => setFormData({...formData, servicePackage: pkg.id})} 
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-300 ${
                          formData.servicePackage === pkg.id 
                            ? 'bg-green-500/20 border-green-400 shadow-lg shadow-green-400/20' 
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center gap-4">
                          <div className="text-green-300">{pkg.icon}</div>
                          <div>
                            <h3 className="font-semibold text-white">{pkg.name}</h3>
                            <p className="text-sm text-gray-300">{pkg.description}</p>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      onClick={() => setCurrentStep(2)} 
                      disabled={!formData.servicePackage}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-white mb-6">Kitchen & Scope Details</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <GlassmorphismSelect
                      options={kitchenTypes}
                      value={formData.kitchenType}
                      onChange={value => setFormData({...formData, kitchenType: value})}
                      placeholder="Kitchen Type"
                    />
                    <GlassmorphismSelect
                      options={kitchenSizes}
                      value={formData.kitchenSize}
                      onChange={value => setFormData({...formData, kitchenSize: value})}
                      placeholder="Kitchen Size"
                    />
                    <GlassmorphismSelect
                      options={cleaningLevels}
                      value={formData.cleaningLevel}
                      onChange={value => setFormData({...formData, cleaningLevel: value})}
                      placeholder="Cleaning Level"
                    />
                    <GlassmorphismSelect
                      options={serviceFrequencies}
                      value={formData.serviceFrequency}
                      onChange={value => setFormData({...formData, serviceFrequency: value})}
                      placeholder="Service Frequency"
                    />
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-white mb-3">Cleaning Areas (Select all that apply)</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                      {cleaningAreaOptions.map(area => (
                        <motion.button
                          key={area.id}
                          onClick={() => handleCleaningAreaToggle(area.id)}
                          className={`p-3 rounded-lg border-2 text-sm transition-all duration-300 ${
                            (formData.cleaningAreas || []).includes(area.id)
                              ? 'bg-green-500/20 border-green-400 text-white'
                              : 'bg-white/5 border-white/20 text-gray-300 hover:border-white/40'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          {area.name}
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-white mb-3">Equipment Types (Select all that apply)</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                      {equipmentTypeOptions.map(equipment => (
                        <motion.button
                          key={equipment.id}
                          onClick={() => handleEquipmentToggle(equipment.id)}
                          className={`p-3 rounded-lg border-2 text-sm transition-all duration-300 ${
                            (formData.equipmentTypes || []).includes(equipment.id)
                              ? 'bg-green-500/20 border-green-400 text-white'
                              : 'bg-white/5 border-white/20 text-gray-300 hover:border-white/40'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          {equipment.name}
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={e => setFormData({...formData, preferredDate: e.target.value})} 
                      className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all" 
                      min={new Date().toISOString().split('T')[0]}
                    />
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={value => setFormData({...formData, preferredTime: value})}
                      placeholder="Preferred Time"
                    />
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)} 
                      disabled={!formData.kitchenType || !formData.kitchenSize || !formData.cleaningLevel || !formData.serviceFrequency || !formData.preferredDate || !formData.preferredTime}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-white mb-6">Add-on Services</h2>
                  <p className="text-gray-300 mb-6">Select any additional services you need:</p>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map(addon => (
                      <motion.button 
                        key={addon.id} 
                        onClick={() => handleAddOnToggle(addon.id)} 
                        className={`p-4 rounded-xl border-2 text-left transition-all duration-300 ${
                          (formData.addOns || []).includes(addon.id) 
                            ? 'bg-green-500/20 border-green-400 shadow-lg shadow-green-400/20' 
                            : 'bg-white/5 border-white/20 hover:border-white/40'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-white">{addon.name}</h3>
                          <span className="text-green-400 font-medium">+${addon.price}</span>
                        </div>
                        <p className="text-sm text-gray-300">{addon.description}</p>
                      </motion.button>
                    ))}
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-white mb-6">Contact Information</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Company Name" 
                        value={formData.companyName || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, companyName: value});
                          validateField('companyName', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.companyName 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.companyName && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.companyName}</p>
                      )}
                    </div>
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Contact Name" 
                        value={formData.contactName || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, contactName: value});
                          validateField('contactName', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.contactName 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.contactName && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.contactName}</p>
                      )}
                    </div>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, email: value});
                          validateField('email', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.email 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, phone: value});
                          validateField('phone', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.phone 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    <div className="relative sm:col-span-2">
                      <Home className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Kitchen Address" 
                        value={formData.address || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, address: value});
                          validateField('address', value);
                        }}
                        className={`w-full bg-white/10 p-3 pl-12 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.address 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                      )}
                    </div>
                    <div>
                      <input 
                        type="text" 
                        placeholder="City" 
                        value={formData.city || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, city: value});
                          validateField('city', value);
                        }}
                        className={`w-full bg-white/10 p-3 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.city 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.city && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                      )}
                    </div>
                    <div>
                      <input 
                        type="text" 
                        placeholder="ZIP Code" 
                        value={formData.zipCode || ''} 
                        onChange={e => {
                          const value = e.target.value;
                          setFormData({...formData, zipCode: value});
                          validateField('zipCode', value);
                        }}
                        className={`w-full bg-white/10 p-3 rounded-xl border text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ${
                          validationErrors.zipCode 
                            ? 'border-red-400 focus:ring-red-400' 
                            : 'border-white/20 focus:ring-green-400'
                        }`}
                      />
                      {validationErrors.zipCode && (
                        <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                      )}
                    </div>
                  </div>

                  <textarea 
                    placeholder="Special Instructions (health department requirements, equipment access, etc.)" 
                    value={formData.specialInstructions || ''} 
                    onChange={e => setFormData({...formData, specialInstructions: e.target.value})} 
                    className="w-full bg-white/10 p-3 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-400 transition-all resize-none" 
                    rows={4} 
                  />

                  {/* Service Estimate Display */}
                  <div className="mb-6 p-4 bg-green-500/10 border border-green-400/30 rounded-xl">
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-white mb-2">Service Estimate</h3>
                      <div className="text-3xl font-bold text-green-400 mb-2">
                        ${calculatePrice()}
                      </div>
                      <p className="text-white/70 text-sm">
                        Final price confirmed after kitchen inspection
                      </p>
                    </div>
                  </div>

                  {validationErrors.submit && (
                    <div className="mb-4 p-3 bg-red-500/10 border border-red-400/30 rounded-xl">
                      <p className="text-red-400 text-sm">{validationErrors.submit}</p>
                    </div>
                  )}

                  <div className="flex justify-between mt-6">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!formData.companyName || !formData.contactName || !formData.email || !formData.phone || !formData.address || !formData.city || !formData.zipCode || isSubmitting || Object.keys(validationErrors).length > 0}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Schedule Service <CheckCircle className="ml-2 h-5 w-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedKitchenForm; 
