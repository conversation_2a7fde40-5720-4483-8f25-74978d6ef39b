import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Layers, Zap, Sparkles, Wind, Droplets, Building, CheckCircle, AlertCircle
} from 'lucide-react';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';

// Data Interfaces
interface FloorCareFormData {
  servicePackage?: string;
  propertyType?: string;
  floorTypes?: string[];
  squareFootage?: number;
  currentCondition?: string;
  propertyAddress?: string;
  accessHours?: string;
  securityRequirements?: string;
  parkingAvailable?: boolean;
  serviceFrequency?: string;
  preferredTime?: string;
  priorityAreas?: string[];
  additionalServices?: string[];
  specialInstructions?: string;
  startDate?: string;
  wantsRecurringContract?: boolean;
  contractLength?: string;
  budgetRange?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  jobTitle?: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  propertyAddress?: string;
  propertyType?: string;
  floorTypes?: string;
  squareFootage?: string;
  serviceFrequency?: string;
  preferredTime?: string;
}

// Validation Utilities
const validateEmail = (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
const validatePhoneNumber = (phone: string) => /^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/.test(phone);
const formatPhoneNumber = (value: string) => {
  if (!value) return value;
  const phoneNumber = value.replace(/[^\d]/g, '');
  const phoneNumberLength = phoneNumber.length;
  if (phoneNumberLength < 4) return phoneNumber;
  if (phoneNumberLength < 7) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
  }
  return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
};

// Form Constants
const steps = [
  { id: 1, name: 'Service' },
  { id: 2, name: 'Property' },
  { id: 3, name: 'Scope' },
  { id: 4, name: 'Add-ons' },
  { id: 5, name: 'Schedule' },
  { id: 6, name: 'Contact' },
];

const servicePackages = [
  { id: 'maintenance', name: 'Maintenance Program', description: 'Scheduled cleaning & buffing.', icon: <Layers /> },
  { id: 'deep-clean', name: 'Deep Clean & Restoration', description: 'Intensive cleaning & restoration.', icon: <Droplets /> },
  { id: 'strip-wax', name: 'Strip & Wax Service', description: 'Full strip and new wax application.', icon: <Sparkles /> },
  { id: 'concrete-polish', name: 'Concrete Polishing', description: 'High-gloss concrete finish.', icon: <Zap /> },
  { id: 'specialty-care', name: 'Specialty Floor Care', description: 'Care for marble, stone, etc.', icon: <Wind /> },
];

const propertyTypes = [
  { id: 'office-building', name: 'Office Building' },
  { id: 'retail-store', name: 'Retail Store' },
  { id: 'restaurant', name: 'Restaurant/Cafe' },
  { id: 'warehouse', name: 'Warehouse/Industrial' },
  { id: 'medical-facility', name: 'Medical Facility' },
  { id: 'school-university', name: 'School/University' },
  { id: 'hotel-hospitality', name: 'Hotel/Hospitality Venue' },
  { id: 'showroom', name: 'Vehicle Showroom' },
  { id: 'other', name: 'Other' },
];

const floorTypes = [
  { id: 'vct', name: 'Vinyl Composition Tile (VCT)' },
  { id: 'lvt', name: 'Luxury Vinyl Tile (LVT)' },
  { id: 'concrete', name: 'Concrete (Sealed/Polished)' },
  { id: 'terrazzo', name: 'Terrazzo' },
  { id: 'marble-stone', name: 'Marble/Natural Stone' },
  { id: 'ceramic-porcelain', name: 'Ceramic/Porcelain Tile' },
  { id: 'hardwood', name: 'Hardwood' },
  { id: 'laminate', name: 'Laminate' },
  { id: 'other', name: 'Other' },
];

const serviceFrequencies = [
  { id: 'one-time', name: 'One-Time Project' },
  { id: 'daily', name: 'Daily' },
  { id: 'weekly', name: 'Weekly' },
  { id: 'bi-weekly', name: 'Bi-Weekly' },
  { id: 'monthly', name: 'Monthly' },
  { id: 'quarterly', name: 'Quarterly' },
];

const preferredTimes = [
  { id: 'after-hours', name: 'After Business Hours (Recommended)' },
  { id: 'morning', name: 'Morning (8AM - 12PM)' },
  { id: 'afternoon', name: 'Afternoon (12PM - 5PM)' },
  { id: 'weekend', name: 'Weekends' },
];

const priorityAreas = [
  { id: 'lobby', name: 'Lobby/Entrance' },
  { id: 'hallways', name: 'Main Hallways/Corridors' },
  { id: 'restrooms', name: 'Restrooms' },
  { id: 'kitchen', name: 'Cafeteria/Kitchen Areas' },
  { id: 'offices', name: 'Individual Offices' },
  { id: 'cubicles', name: 'Open-Plan/Cubicle Areas' },
  { id: 'showroom-floor', name: 'Showroom Floor' },
  { id: 'warehouse-paths', name: 'Warehouse Paths' },
];

const additionalServices = [
  { id: 'grout-cleaning', name: 'Tile & Grout Deep Cleaning' },
  { id: 'stain-removal', name: 'Targeted Stain Removal' },
  { id: 'high-speed-burnishing', name: 'High-Speed Burnishing/Buffing' },
  { id: 'anti-static-treatment', name: 'Anti-Static Floor Treatment' },
  { id: 'matting-service', name: 'Walk-Off Matting Service' },
  { id: 'emergency-cleanup', name: 'Emergency Spill/Water Cleanup' },
];


const BrandAlignedFloorCareForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<FloorCareFormData>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState<'success' | 'error' | null>(null);

  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        firstName: prev.firstName || user.user_metadata?.first_name,
        lastName: prev.lastName || user.user_metadata?.last_name,
        email: prev.email || user.email,
        phone: prev.phone || user.phone,
        companyName: prev.companyName || user.user_metadata?.company_name,
      }));
    }
  }, [user]);

  const validateField = (fieldName: keyof FormErrors, value: any): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value) return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
      case 'lastName':
      case 'companyName':
      case 'propertyAddress':
        if (!value || value.trim().length < 2) return `${fieldName.replace(/([A-Z])/g, ' $1')} must be at least 2 characters`;
        break;
      case 'propertyType':
        if (!value) return 'Please select a property type';
        break;
      case 'floorTypes':
        if (!value || value.length === 0) return 'Please select at least one floor type';
        break;
      case 'squareFootage':
        if (!value || +value < 100) return 'Square footage must be at least 100 sq ft';
        break;
      case 'serviceFrequency':
        if (!value) return 'Please select a service frequency';
        break;
      case 'preferredTime':
        if (!value) return 'Please select a preferred time';
        break;
    }
    return undefined;
  };

  const handleInputChange = (field: keyof FloorCareFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (Object.keys(formErrors).includes(field)) {
      const error = validateField(field as keyof FormErrors, value);
      setFormErrors(prev => ({ ...prev, [field]: error }));
    }
  };
  
  const handleNextStep = () => {
    let isValid = true;
    const newErrors: FormErrors = {};

    // Validate fields for the current step
    if (currentStep === 1) {
      if (!formData.servicePackage) {
        // This is a soft validation, just for UI state, not blocking
      }
    } else if (currentStep === 2) {
        ['propertyType', 'floorTypes', 'squareFootage', 'propertyAddress'].forEach(field => {
            const error = validateField(field as keyof FormErrors, formData[field as keyof FloorCareFormData]);
            if (error) {
                newErrors[field as keyof FormErrors] = error;
                isValid = false;
            }
        });
    } else if (currentStep === 3) {
        ['serviceFrequency', 'preferredTime'].forEach(field => {
            const error = validateField(field as keyof FormErrors, formData[field as keyof FloorCareFormData]);
            if (error) {
                newErrors[field as keyof FormErrors] = error;
                isValid = false;
            }
        });
    } else if (currentStep === 6) {
        ['firstName', 'lastName', 'email', 'phone', 'companyName'].forEach(field => {
            const error = validateField(field as keyof FormErrors, formData[field as keyof FloorCareFormData]);
            if (error) {
                newErrors[field as keyof FormErrors] = error;
                isValid = false;
            }
        });
    }

    setFormErrors(newErrors);

    if (isValid) {
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      navigate('/commercial');
    }
  };
  
  const handleSubmit = async () => {
    setIsSubmitting(true);
    // Final validation check
     const newErrors: FormErrors = {};
    ['firstName', 'lastName', 'email', 'phone', 'companyName', 'propertyAddress', 'propertyType', 'floorTypes', 'squareFootage', 'serviceFrequency', 'preferredTime'].forEach(field => {
        const error = validateField(field as keyof FormErrors, formData[field as keyof FloorCareFormData]);
        if (error) {
            newErrors[field as keyof FormErrors] = error;
        }
    });
     if (Object.keys(newErrors).length > 0) {
        setFormErrors(newErrors);
        setIsSubmitting(false);
        // Maybe jump to the first step with an error
        if (newErrors.propertyType || newErrors.floorTypes || newErrors.squareFootage || newErrors.propertyAddress) setCurrentStep(2);
        else if (newErrors.serviceFrequency || newErrors.preferredTime) setCurrentStep(3);
        else if (newErrors.firstName || newErrors.lastName || newErrors.email || newErrors.phone || newErrors.companyName) setCurrentStep(6);
        return;
    }

    try {
      if (!supabase) {
        throw new Error('Database connection not available');
      }

      // Prepare the submission data to match the expected database schema
      const submissionData = {
        user_id: user?.id || null,
        service_type: 'floor',
        status: 'pending',
        property_details: {
          propertyType: formData.propertyType,
          propertyAddress: formData.propertyAddress,
          squareFootage: formData.squareFootage,
          floorTypes: formData.floorTypes,
          currentCondition: formData.currentCondition,
          accessHours: formData.accessHours,
          securityRequirements: formData.securityRequirements,
          parkingAvailable: formData.parkingAvailable,
        },
        service_details: {
          servicePackage: formData.servicePackage,
          serviceFrequency: formData.serviceFrequency,
          preferredTime: formData.preferredTime,
          priorityAreas: formData.priorityAreas,
          additionalServices: formData.additionalServices,
          specialInstructions: formData.specialInstructions,
          budgetRange: formData.budgetRange,
        },
        schedule: {
          startDate: formData.startDate,
          wantsRecurringContract: formData.wantsRecurringContract,
          contractLength: formData.contractLength,
        },
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle,
        },
      };

      console.log('Submitting floor care request:', submissionData);

      const { data, error } = await supabase
        .from('booking_forms')
        .insert([submissionData])
        .select();

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Successfully submitted:', data);
      setSubmissionStatus('success');
      
      // Clear form data from localStorage after successful submission
      localStorage.removeItem('pending_floor_care_request');
      
      // Navigate to appropriate page based on authentication status
      setTimeout(() => {
        if (user) {
          navigate('/accountdashboard');
        } else {
          // Store the intent to redirect to dashboard after login
          localStorage.setItem('redirectAfterLogin', '/accountdashboard');
          navigate('/auth/login');
        }
      }, 2000);
    } catch (error) {
      console.error('Submission Error:', error);
      
      // Fallback: Save to localStorage for later retry
      try {
        const fallbackData = {
          timestamp: new Date().toISOString(),
          formData: formData,
          type: 'floor_care_request'
        };
        localStorage.setItem('pending_floor_care_request', JSON.stringify(fallbackData));
        console.log('Saved request to localStorage for later retry');
      } catch (storageError) {
        console.error('Failed to save to localStorage:', storageError);
      }
      
      setSubmissionStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div key="step1" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Select Your Floor Care Service</h2>
            <p className="text-gray-300 mb-6">Choose a package that best fits your needs.</p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {servicePackages.map((pkg) => (
                <motion.div
                  key={pkg.id}
                  whileHover={{ scale: 1.05, boxShadow: '0px 10px 30px rgba(0, 255, 135, 0.15)' }}
                  className={`p-6 rounded-2xl border transition-all duration-300 cursor-pointer
                    ${formData.servicePackage === pkg.id
                      ? 'bg-green-500/10 border-green-400'
                      : 'bg-white/5 border-white/20 hover:border-green-400/50'
                    }`}
                  onClick={() => handleInputChange('servicePackage', pkg.id)}
                >
                  <div className="flex items-center gap-4 mb-3">
                    <div className="text-green-400">{pkg.icon}</div>
                    <h3 className="font-semibold text-white text-lg">{pkg.name}</h3>
                  </div>
                  <p className="text-gray-300 text-sm">{pkg.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Property & Floor Details</h2>
            <p className="text-gray-300 mb-6">Tell us about the space we'll be servicing.</p>
            
            <div className="space-y-8">
              {/* Property Type Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Property Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Property Type *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {propertyTypes.slice(0, 6).map((type) => (
                        <motion.div
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.propertyType === type.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('propertyType', type.id)}
                        >
                          <Building className="w-4 h-4 mx-auto mb-1 text-green-400" />
                          <span className="text-xs font-medium text-white">{type.name}</span>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.propertyType && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.propertyType}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Property Address *</label>
                      <input
                        type="text"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.propertyAddress ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="Enter property address"
                        value={formData.propertyAddress || ''}
                        onChange={(e) => handleInputChange('propertyAddress', e.target.value)}
                      />
                      {formErrors.propertyAddress && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.propertyAddress}
                        </p>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Total Square Footage *</label>
                      <input
                        type="number"
                        className={`w-full p-3 rounded-xl bg-white/5 border text-white placeholder-gray-400
                          ${formErrors.squareFootage ? 'border-red-400' : 'border-white/20'}
                          focus:border-green-400 focus:outline-none transition-colors`}
                        placeholder="e.g., 5000"
                        min="100"
                        value={formData.squareFootage || ''}
                        onChange={(e) => handleInputChange('squareFootage', +e.target.value)}
                      />
                      {formErrors.squareFootage && (
                        <p className="text-red-400 text-sm mt-1 flex items-center gap-2">
                          <AlertCircle className="w-3 h-3" />
                          {formErrors.squareFootage}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Floor Types Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Floor Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Floor Types (Select all that apply) *</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {floorTypes.map((floor) => (
                        <motion.div
                          key={floor.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.floorTypes?.includes(floor.id)
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => {
                            const currentFloors = formData.floorTypes || [];
                            const newFloors = currentFloors.includes(floor.id)
                              ? currentFloors.filter(f => f !== floor.id)
                              : [...currentFloors, floor.id];
                            handleInputChange('floorTypes', newFloors);
                          }}
                        >
                          <div className="flex items-center gap-3">
                            <div className={`w-4 h-4 rounded border-2 flex items-center justify-center
                              ${formData.floorTypes?.includes(floor.id)
                                ? 'bg-green-400 border-green-400'
                                : 'border-white/30'
                              }`}>
                              {formData.floorTypes?.includes(floor.id) && (
                                <CheckCircle className="w-2 h-2 text-white" />
                              )}
                            </div>
                            <span className="text-sm font-medium text-white">{floor.name}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.floorTypes && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.floorTypes}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Current Floor Condition</label>
                    <select
                      className="w-full p-3 rounded-xl bg-slate-900 border border-white/20 text-white
                        focus:border-green-400 focus:outline-none transition-colors
                        hover:bg-slate-800 hover:border-slate-600"
                      value={formData.currentCondition || ''}
                      onChange={(e) => handleInputChange('currentCondition', e.target.value)}
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.5rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em',
                        paddingRight: '2.5rem'
                      }}
                    >
                      <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                        Select condition
                      </option>
                      <option value="excellent" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        Excellent - Well maintained
                      </option>
                      <option value="good" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        Good - Minor wear
                      </option>
                      <option value="fair" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        Fair - Moderate wear/staining
                      </option>
                      <option value="poor" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        Poor - Heavy wear/damage
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div key="step3" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Service Requirements</h2>
            <p className="text-gray-300 mb-6">Let us know your scheduling and access requirements.</p>
            
            <div className="space-y-8">
              {/* Service Schedule Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Service Schedule</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Service Frequency *</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {serviceFrequencies.map((freq) => (
                        <motion.div
                          key={freq.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200 text-center
                            ${formData.serviceFrequency === freq.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('serviceFrequency', freq.id)}
                        >
                          <div className="text-sm font-medium text-white">{freq.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.serviceFrequency && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.serviceFrequency}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Preferred Service Time *</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {preferredTimes.map((time) => (
                        <motion.div
                          key={time.id}
                          whileHover={{ scale: 1.02 }}
                          className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                            ${formData.preferredTime === time.id
                              ? 'bg-green-500/10 border-green-400'
                              : 'bg-white/5 border-white/20 hover:border-green-400/50'
                            }`}
                          onClick={() => handleInputChange('preferredTime', time.id)}
                        >
                          <div className="text-sm font-medium text-white">{time.name}</div>
                        </motion.div>
                      ))}
                    </div>
                    {formErrors.preferredTime && (
                      <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                        <AlertCircle className="w-4 h-4" />
                        {formErrors.preferredTime}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Priority Areas Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Priority Areas</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-3">Focus Areas (Optional)</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {priorityAreas.slice(0, 6).map((area) => (
                      <motion.div
                        key={area.id}
                        whileHover={{ scale: 1.02 }}
                        className={`p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.priorityAreas?.includes(area.id)
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => {
                          const currentAreas = formData.priorityAreas || [];
                          const newAreas = currentAreas.includes(area.id)
                            ? currentAreas.filter(a => a !== area.id)
                            : [...currentAreas, area.id];
                          handleInputChange('priorityAreas', newAreas);
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-4 h-4 rounded border-2 flex items-center justify-center
                            ${formData.priorityAreas?.includes(area.id)
                              ? 'bg-green-400 border-green-400'
                              : 'border-white/30'
                            }`}>
                            {formData.priorityAreas?.includes(area.id) && (
                              <CheckCircle className="w-2 h-2 text-white" />
                            )}
                          </div>
                          <span className="text-sm font-medium text-white">{area.name}</span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Access & Logistics Section */}
              <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                <h3 className="text-lg font-semibold text-white mb-4">Access & Logistics</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Access Hours</label>
                      <input
                        type="text"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., 6 PM - 6 AM weekdays"
                        value={formData.accessHours || ''}
                        onChange={(e) => handleInputChange('accessHours', e.target.value)}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">Security Requirements</label>
                      <input
                        type="text"
                        className="w-full p-3 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                          focus:border-green-400 focus:outline-none transition-colors"
                        placeholder="e.g., Badge access required"
                        value={formData.securityRequirements || ''}
                        onChange={(e) => handleInputChange('securityRequirements', e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-3">Parking Available for Service Vehicles?</label>
                    <div className="flex gap-4">
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.parkingAvailable === true
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => handleInputChange('parkingAvailable', true)}
                      >
                        <div className="text-center text-sm font-medium text-white">Yes</div>
                      </motion.div>
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className={`flex-1 p-3 rounded-xl border cursor-pointer transition-all duration-200
                          ${formData.parkingAvailable === false
                            ? 'bg-green-500/10 border-green-400'
                            : 'bg-white/5 border-white/20 hover:border-green-400/50'
                          }`}
                        onClick={() => handleInputChange('parkingAvailable', false)}
                      >
                        <div className="text-center text-sm font-medium text-white">No</div>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div key="step4" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Additional Services</h2>
            <p className="text-gray-300 mb-6">Select any additional services you'd like to include.</p>
            
            <div className="space-y-6">
              {/* Additional Services */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Add-On Services (Optional)</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {additionalServices.map((service) => (
                    <motion.div
                      key={service.id}
                      whileHover={{ scale: 1.02 }}
                      className={`p-4 rounded-xl border cursor-pointer transition-all duration-200
                        ${formData.additionalServices?.includes(service.id)
                          ? 'bg-green-500/10 border-green-400'
                          : 'bg-white/5 border-white/20 hover:border-green-400/50'
                        }`}
                      onClick={() => {
                        const currentServices = formData.additionalServices || [];
                        const newServices = currentServices.includes(service.id)
                          ? currentServices.filter(s => s !== service.id)
                          : [...currentServices, service.id];
                        handleInputChange('additionalServices', newServices);
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-5 h-5 rounded border-2 flex items-center justify-center
                          ${formData.additionalServices?.includes(service.id)
                            ? 'bg-green-400 border-green-400'
                            : 'border-white/30'
                          }`}>
                          {formData.additionalServices?.includes(service.id) && (
                            <CheckCircle className="w-3 h-3 text-white" />
                          )}
                        </div>
                        <span className="text-sm font-medium text-white">{service.name}</span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Special Instructions */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Special Instructions or Requirements</label>
                <textarea
                  className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                    focus:border-green-400 focus:outline-none transition-colors h-24 resize-none"
                  placeholder="Any specific requirements, concerns, or instructions for our team..."
                  value={formData.specialInstructions || ''}
                  onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                />
              </div>

              {/* Budget Range */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Budget Range (Optional)</label>
                <select
                  className="w-full p-4 rounded-xl bg-slate-900 border border-white/20 text-white
                    focus:border-green-400 focus:outline-none transition-colors
                    hover:bg-slate-800 hover:border-slate-600"
                  value={formData.budgetRange || ''}
                  onChange={(e) => handleInputChange('budgetRange', e.target.value)}
                  style={{
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: 'right 0.5rem center',
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '1.5em 1.5em',
                    paddingRight: '2.5rem'
                  }}
                >
                  <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                    Select budget range
                  </option>
                  <option value="under-1000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    Under $1,000
                  </option>
                  <option value="1000-2500" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    $1,000 - $2,500
                  </option>
                  <option value="2500-5000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    $2,500 - $5,000
                  </option>
                  <option value="5000-10000" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    $5,000 - $10,000
                  </option>
                  <option value="10000-plus" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    $10,000+
                  </option>
                  <option value="monthly-contract" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                    Monthly Contract
                  </option>
                </select>
              </div>
            </div>
          </motion.div>
        );

      case 5:
        return (
          <motion.div key="step5" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Project Timeline</h2>
            <p className="text-gray-300 mb-6">When would you like to start your floor care service?</p>
            
            <div className="space-y-6">
              {/* Start Date */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Start Date</label>
                <input
                  type="date"
                  className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white
                    focus:border-green-400 focus:outline-none transition-colors"
                  value={formData.startDate || ''}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              {/* Recurring Contract */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">Are you interested in a recurring service contract?</label>
                <div className="flex gap-4 mb-4">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className={`flex-1 p-4 rounded-xl border cursor-pointer transition-all duration-200
                      ${formData.wantsRecurringContract === true
                        ? 'bg-green-500/10 border-green-400'
                        : 'bg-white/5 border-white/20 hover:border-green-400/50'
                      }`}
                    onClick={() => handleInputChange('wantsRecurringContract', true)}
                  >
                    <div className="text-center text-sm font-medium text-white">Yes</div>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className={`flex-1 p-4 rounded-xl border cursor-pointer transition-all duration-200
                      ${formData.wantsRecurringContract === false
                        ? 'bg-green-500/10 border-green-400'
                        : 'bg-white/5 border-white/20 hover:border-green-400/50'
                      }`}
                    onClick={() => handleInputChange('wantsRecurringContract', false)}
                  >
                    <div className="text-center text-sm font-medium text-white">No</div>
                  </motion.div>
                </div>

                {/* Contract Length - only show if they want recurring */}
                {formData.wantsRecurringContract && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Preferred Contract Length</label>
                    <select
                      className="w-full p-4 rounded-xl bg-slate-900 border border-white/20 text-white
                        focus:border-green-400 focus:outline-none transition-colors
                        hover:bg-slate-800 hover:border-slate-600"
                      value={formData.contractLength || ''}
                      onChange={(e) => handleInputChange('contractLength', e.target.value)}
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                        backgroundPosition: 'right 0.5rem center',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: '1.5em 1.5em',
                        paddingRight: '2.5rem'
                      }}
                    >
                      <option value="" style={{ backgroundColor: '#0f172a', color: '#9ca3af' }}>
                        Select contract length
                      </option>
                      <option value="3-months" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        3 Months
                      </option>
                      <option value="6-months" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        6 Months
                      </option>
                      <option value="1-year" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        1 Year
                      </option>
                      <option value="2-years" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        2 Years
                      </option>
                      <option value="flexible" style={{ backgroundColor: '#0f172a', color: '#ffffff' }}>
                        Month-to-Month
                      </option>
                    </select>
                  </div>
                )}
              </div>

              {/* Summary Card */}
              <div className="bg-white/5 border border-white/20 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Service Summary</h3>
                <div className="space-y-3">
                  {formData.servicePackage && (
                    <div className="flex justify-between">
                      <span className="text-gray-300">Service Package:</span>
                      <span className="text-white font-medium">
                        {servicePackages.find(p => p.id === formData.servicePackage)?.name}
                      </span>
                    </div>
                  )}
                  {formData.propertyType && (
                    <div className="flex justify-between">
                      <span className="text-gray-300">Property Type:</span>
                      <span className="text-white font-medium">
                        {propertyTypes.find(p => p.id === formData.propertyType)?.name}
                      </span>
                    </div>
                  )}
                  {formData.squareFootage && (
                    <div className="flex justify-between">
                      <span className="text-gray-300">Square Footage:</span>
                      <span className="text-white font-medium">{formData.squareFootage.toLocaleString()} sq ft</span>
                    </div>
                  )}
                  {formData.serviceFrequency && (
                    <div className="flex justify-between">
                      <span className="text-gray-300">Service Frequency:</span>
                      <span className="text-white font-medium">
                        {serviceFrequencies.find(f => f.id === formData.serviceFrequency)?.name}
                      </span>
                    </div>
                  )}
                  {formData.floorTypes && formData.floorTypes.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-300">Floor Types:</span>
                      <span className="text-white font-medium">
                        {formData.floorTypes.length} type{formData.floorTypes.length > 1 ? 's' : ''} selected
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 6:
        return (
          <motion.div key="step6" initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
            <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
            <p className="text-gray-300 mb-6">Please provide your contact details so we can prepare your quote.</p>
            
            <div className="space-y-6">
              {/* Name Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">First Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.firstName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your first name"
                    value={formData.firstName || ''}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                  />
                  {formErrors.firstName && (
                    <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                      <AlertCircle className="w-4 h-4" />
                      {formErrors.firstName}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Last Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.lastName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your last name"
                    value={formData.lastName || ''}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                  />
                  {formErrors.lastName && (
                    <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                      <AlertCircle className="w-4 h-4" />
                      {formErrors.lastName}
                    </p>
                  )}
                </div>
              </div>

              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Email Address *</label>
                <input
                  type="email"
                  className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                    ${formErrors.email ? 'border-red-400' : 'border-white/20'}
                    focus:border-green-400 focus:outline-none transition-colors`}
                  placeholder="Enter your email address"
                  value={formData.email || ''}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
                {formErrors.email && (
                  <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    {formErrors.email}
                  </p>
                )}
              </div>

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                <input
                  type="tel"
                  className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                    ${formErrors.phone ? 'border-red-400' : 'border-white/20'}
                    focus:border-green-400 focus:outline-none transition-colors`}
                  placeholder="(*************"
                  value={formData.phone || ''}
                  onChange={(e) => {
                    const formatted = formatPhoneNumber(e.target.value);
                    handleInputChange('phone', formatted);
                  }}
                />
                {formErrors.phone && (
                  <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                    <AlertCircle className="w-4 h-4" />
                    {formErrors.phone}
                  </p>
                )}
              </div>

              {/* Company Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Company Name *</label>
                  <input
                    type="text"
                    className={`w-full p-4 rounded-xl bg-white/5 border text-white placeholder-gray-400
                      ${formErrors.companyName ? 'border-red-400' : 'border-white/20'}
                      focus:border-green-400 focus:outline-none transition-colors`}
                    placeholder="Enter your company name"
                    value={formData.companyName || ''}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                  />
                  {formErrors.companyName && (
                    <p className="text-red-400 text-sm mt-2 flex items-center gap-2">
                      <AlertCircle className="w-4 h-4" />
                      {formErrors.companyName}
                    </p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Job Title</label>
                  <input
                    type="text"
                    className="w-full p-4 rounded-xl bg-white/5 border border-white/20 text-white placeholder-gray-400
                      focus:border-green-400 focus:outline-none transition-colors"
                    placeholder="Your job title"
                    value={formData.jobTitle || ''}
                    onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                  />
                </div>
              </div>

              {/* Final Note */}
              <div className="bg-green-500/10 border border-green-400/30 rounded-2xl p-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-green-400 font-medium mb-1">Ready to Submit</h4>
                    <p className="text-gray-300 text-sm">
                      We'll review your request and provide a detailed quote within 24 hours. 
                      Our team will contact you to schedule a site visit if needed.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress Bar */}
          <div className="mb-12">
            {/* Step Counter */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">
                  Step {currentStep} of {steps.length}
                </span>
              </div>
            </div>
            
            {/* Progress Line */}
            <div className="relative">
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"
                  initial={{ width: '16.67%' }}
                  animate={{ width: `${(currentStep / steps.length) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                />
              </div>
              
              {/* Step Dots */}
              <div className="absolute inset-0 flex items-center justify-between px-1">
                {steps.map((step) => (
                  <motion.div
                    key={step.id}
                    className={`w-4 h-4 rounded-full border-2 ${
                      currentStep >= step.id 
                        ? 'bg-green-400 border-green-400' 
                        : 'bg-white/10 border-white/30'
                    }`}
                    animate={{
                      scale: currentStep === step.id ? 1.2 : 1,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </div>
            
            {/* Current Step Label */}
            <div className="text-center mt-4">
              <h1 className="text-xl font-semibold text-white">
                {steps[currentStep - 1]?.name}
              </h1>
            </div>
          </div>

          {/* Form Content */}
          <motion.div 
            className="rounded-3xl shadow-2xl p-8 md:p-12"
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
              backdropFilter: 'blur(16px)',
              WebkitBackdropFilter: 'blur(16px)',
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              {submissionStatus === 'success' ? (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="text-center"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4"
                  >
                    <CheckCircle className="w-8 h-8 text-white" />
                  </motion.div>
                  <h2 className="text-2xl font-bold text-white mb-2">Request Submitted!</h2>
                  <p className="text-gray-300 mb-4">
                    Thank you for your floor care request. We'll review your information and 
                    provide a detailed quote within 24 hours.
                  </p>
                  <div className="text-sm text-gray-400">
                    Redirecting you to confirmation page...
                  </div>
                </motion.div>
              ) : submissionStatus === 'error' ? (
                <motion.div
                  key="error"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="text-center"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4"
                  >
                    <AlertCircle className="w-8 h-8 text-white" />
                  </motion.div>
                  <h2 className="text-2xl font-bold text-white mb-2">Submission Error</h2>
                  <p className="text-gray-300 mb-6">
                    We encountered an issue submitting your request. Please try again or contact us directly.
                  </p>
                  <button
                    onClick={() => setSubmissionStatus(null)}
                    className="px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-xl font-medium transition-colors"
                  >
                    Try Again
                  </button>
                </motion.div>
              ) : (
                <>
                  {renderStepContent()}
                  
                  {/* Navigation Buttons */}
                  <div className="flex justify-between items-center mt-12">
                    <button
                      onClick={handlePrevStep}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10 border"
                    >
                      {currentStep === 1 ? 'Cancel' : 'Back'}
                    </button>

                    {currentStep < steps.length ? (
                      <button
                        onClick={handleNextStep}
                        className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                      >
                        Continue →
                      </button>
                    ) : (
                      <button
                        onClick={handleSubmit}
                        disabled={isSubmitting}
                        className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white font-semibold shadow-lg"
                      >
                        {isSubmitting ? (
                          <>
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                              className="w-5 h-5 border-2 border-white border-t-transparent rounded-full inline-block mr-2"
                            />
                            Submitting...
                          </>
                        ) : (
                          'Submit Request'
                        )}
                      </button>
                    )}
                  </div>
                </>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedFloorCareForm; 
