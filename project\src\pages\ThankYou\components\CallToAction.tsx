import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Phone, MessageSquare, Mail } from 'lucide-react';
import { Button } from '../../../components/ui/Button';

export function CallToAction() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.6 }}
      className="relative overflow-hidden"
    >
      {/* Background with animated gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-brand-600 to-brand-700">
        <motion.div
          className="absolute inset-0 opacity-30"
          animate={{
            background: [
              'radial-gradient(circle at 0% 0%, transparent 0%, #000 100%)',
              'radial-gradient(circle at 100% 100%, transparent 0%, #000 100%)',
              'radial-gradient(circle at 0% 0%, transparent 0%, #000 100%)',
            ],
          }}
          transition={{ duration: 8, repeat: Infinity }}
        />
      </div>

      {/* Content */}
      <div className="relative rounded-2xl p-8 text-center">
        <h3 className="text-3xl font-bold text-white mb-4">
          Need Assistance?
        </h3>
        <p className="text-xl text-brand-100 mb-8 max-w-2xl mx-auto">
          Our support team is available 24/7 to help you with any questions
        </p>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-3xl mx-auto">
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-white/10 backdrop-blur-sm rounded-xl p-4"
          >
            <Phone className="w-6 h-6 text-white mx-auto mb-2" />
            <p className="text-white font-medium">(*************</p>
            <p className="text-brand-100 text-sm">Call Us</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-white/10 backdrop-blur-sm rounded-xl p-4"
          >
            <MessageSquare className="w-6 h-6 text-white mx-auto mb-2" />
            <p className="text-white font-medium">Live Chat</p>
            <p className="text-brand-100 text-sm">Chat with Support</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-white/10 backdrop-blur-sm rounded-xl p-4"
          >
            <Mail className="w-6 h-6 text-white mx-auto mb-2" />
            <p className="text-white font-medium">Email Us</p>
            <p className="text-brand-100 text-sm">24/7 Response</p>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-8"
        >
          <Button
            size="lg"
            className="bg-white text-brand-600 hover:bg-brand-50"
            onClick={() => window.location.href = '/contact'}
          >
            Contact Support
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
}
