import React from 'react';
import { Building2, MapPin, Building, Layers, Users, Clock, Car, Shield } from 'lucide-react';

interface PropertyDetailsProps {
  details: {
    propertyType: string;  // Changed from facilityType to propertyType
    industryType: string;
    squareFootage: number;
    floors: number;
    propertyAddress: string;
    occupancy: string;
    operatingHours: string;
    parkingAvailable: boolean;
    securityRequirements: string;
  };
  onChange: (details: any) => void;
}

export function PropertyDetails({ details = {
  propertyType: '',  // Changed from facilityType to propertyType
  industryType: '',
  squareFootage: 0,
  floors: 1,
  propertyAddress: '',
  occupancy: '',
  operatingHours: '',
  parkingAvailable: false,
  securityRequirements: ''
}, onChange }: PropertyDetailsProps) {
  const propertyTypes = [  // Changed from facilityTypes to propertyTypes
    'Commercial Office',
    'Medical Facility',
    'Educational Institution',
    'Retail Space',
    'Restaurant',
    'Hotel/Hospitality',
    'Industrial Facility',
    'Mixed-Use Building',
    'Other'
  ];

  const industryTypes = [
    'Corporate',
    'Healthcare',
    'Education',
    'Retail',
    'Hospitality',
    'Manufacturing',
    'Government',
    'Other'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-8">
        <div className="p-3 rounded-full bg-brand-100">
          <Building2 className="w-6 h-6 text-brand-600" />
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900">Property Details</h3>
          <p className="text-gray-600">Tell us about your facility</p>
        </div>
      </div>

      {/* Basic Property Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Property Type <span className="text-red-500">*</span>
          </label>
          <select
            value={details.propertyType}  // Changed from facilityType to propertyType
            onChange={(e) => onChange({ ...details, propertyType: e.target.value })}  // Changed from facilityType to propertyType
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select property type</option>
            {propertyTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Industry Type <span className="text-red-500">*</span>
          </label>
          <select
            value={details.industryType}
            onChange={(e) => onChange({ ...details, industryType: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            required
          >
            <option value="">Select industry type</option>
            {industryTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Space Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Square Footage <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.squareFootage || ''}
              onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Total area"
              min="1"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Number of Floors <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Layers className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.floors || ''}
              onChange={(e) => onChange({ ...details, floors: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              min="1"
              required
            />
          </div>
        </div>
      </div>

      {/* Occupancy & Hours */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Daily Occupancy <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Users className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={details.occupancy}
              onChange={(e) => onChange({ ...details, occupancy: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="e.g., 50-100 people"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Operating Hours <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={details.operatingHours}
              onChange={(e) => onChange({ ...details, operatingHours: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="e.g., Mon-Fri 9AM-5PM"
              required
            />
          </div>
        </div>
      </div>

      {/* Location & Access */}
      <div className="space-y-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Property Address <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={details.propertyAddress}
              onChange={(e) => onChange({ ...details, propertyAddress: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Enter complete address"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Security Requirements
          </label>
          <div className="relative">
            <Shield className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
            <textarea
              value={details.securityRequirements}
              onChange={(e) => onChange({ ...details, securityRequirements: e.target.value })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              rows={3}
              placeholder="Any security protocols or requirements?"
            />
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            checked={details.parkingAvailable}
            onChange={(e) => onChange({ ...details, parkingAvailable: e.target.checked })}
            className="rounded border-gray-300 text-brand-600 focus:ring-brand-500"
          />
          <div className="flex items-center space-x-2">
            <Car className="w-5 h-5 text-gray-400" />
            <span className="text-gray-700">Parking available for cleaning staff</span>
          </div>
        </div>
      </div>
    </div>
  );
}
