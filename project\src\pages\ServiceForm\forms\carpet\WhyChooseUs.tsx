import React from 'react';
import { <PERSON>, <PERSON>, Leaf, Users } from 'lucide-react';

const features = [
  {
    icon: Shield,
    title: 'Professional Team',
    description: 'Trained & vetted cleaning experts'
  },
  {
    icon: Clock,
    title: 'Flexible Scheduling',
    description: 'Available 24/7 for your needs'
  },
  {
    icon: Leaf,
    title: 'Eco-Friendly',
    description: '100% green cleaning products'
  },
  {
    icon: Users,
    title: 'Dedicated Support',
    description: 'Personal account manager'
  }
];

export function WhyChooseUs() {
  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-6">
        Why Choose Our Service
      </h3>

      <div className="space-y-6">
        {features.map((feature) => {
          const Icon = feature.icon;
          
          return (
            <div key={feature.title} className="flex items-start space-x-4">
              <div className="rounded-lg bg-brand-50 p-2">
                <Icon className="w-5 h-5 text-brand-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{feature.title}</h4>
                <p className="text-sm text-gray-600">{feature.description}</p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
