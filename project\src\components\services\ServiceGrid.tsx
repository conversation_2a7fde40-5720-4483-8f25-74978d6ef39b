import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building2, Sparkles, GlassWater, Brush, Construction, 
  Sprout, Grid, Waves, Hammer, ArrowRight 
} from 'lucide-react';

const services = [
  {
    id: 'office',
    icon: Building2,
    title: 'Office Cleaning',
    description: 'Professional cleaning solutions for offices of all sizes',
    features: ['Daily/Weekly Service', 'Eco-Friendly Products', 'Trained Staff'],
    color: 'from-blue-500 to-blue-600',
    link: '/service-form/office'
  },
  {
    id: 'deep',
    icon: Sparkles,
    title: 'Deep Cleaning',
    description: 'Thorough cleaning and sanitization services',
    features: ['Complete Sanitization', 'Deep Scrubbing', 'Air Purification'],
    color: 'from-purple-500 to-purple-600',
    link: '/service-form/deep'
  },
  {
    id: 'window',
    icon: GlassWater,
    title: 'Window Cleaning',
    description: 'Professional window cleaning for commercial buildings',
    features: ['Interior & Exterior', 'High-Rise Capable', 'Streak-Free Results'],
    color: 'from-cyan-500 to-cyan-600',
    link: '/service-form/window'
  },
  {
    id: 'carpet',
    icon: Brush,
    title: 'Carpet Cleaning',
    description: 'Deep carpet cleaning and stain removal',
    features: ['Deep Extraction', 'Stain Treatment', 'Deodorizing'],
    color: 'from-amber-500 to-amber-600',
    link: '/service-form/carpet'
  },
  {
    id: 'construction',
    icon: Construction,
    title: 'Post-Construction',
    description: 'Detailed cleaning after construction or renovation',
    features: ['Debris Removal', 'Dust Control', 'Final Inspection'],
    color: 'from-orange-500 to-orange-600',
    link: '/residential/postconstruction'
  },
  {
    id: 'industrial',
    icon: Grid,
    title: 'Industrial & Warehouses',
    description: 'Specialized cleaning for manufacturing and storage facilities',
    features: ['Equipment Cleaning', 'Safety Compliance', 'OSHA Standards'],
    color: 'from-teal-500 to-teal-600',
    link: '/industrial'
  },
  {
    id: 'sanitization',
    icon: Sprout,
    title: 'Sanitization',
    description: 'Commercial-grade sanitization and disinfection',
    features: ['Medical-Grade Products', 'High-Touch Surfaces', 'EPA Approved'],
    color: 'from-green-500 to-green-600',
    link: '/service-form/sanitization'
  },
  {
    id: 'pressure',
    icon: Waves,
    title: 'Pressure Washing',
    description: 'High-pressure cleaning for exterior surfaces',
    features: ['Building Exterior', 'Concrete & Pavement', 'Graffiti Removal'],
    color: 'from-sky-500 to-sky-600',
    link: '/service-form/pressure'
  },
  {
    id: 'floor',
    icon: Hammer,
    title: 'Floor Restoration',
    description: 'Stripping, sealing, waxing, or refinishing floors',
    features: ['Stripping & Waxing', 'Buffing & Polishing', 'Sealing'],
    color: 'from-rose-500 to-rose-600',
    link: '/service-form/floor'
  }
];

interface ServiceGridProps {
  onServiceSelect: (serviceId: string) => void;
}

export function ServiceGrid({ onServiceSelect }: ServiceGridProps) {
  const navigate = useNavigate();

  const handleServiceSelect = (service: typeof services[0]) => {
    if (service.id === 'industrial') {
      navigate(service.link);
    } else {
      onServiceSelect(service.id);
    }
  };

  return (
    <section id="services" className="py-8 sm:py-12 md:py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <span className="text-brand-600 font-semibold">Our Services</span>
          <h2 className="mt-2 text-3xl font-bold text-gray-900">
            Professional Cleaning Solutions
          </h2>
          <p className="mt-4 text-lg text-gray-600">
            Choose from our comprehensive range of cleaning services
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                className="group"
              >
                <button
                  onClick={() => handleServiceSelect(service)}
                  className="w-full text-left"
                >
                  <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 h-full">
                    {/* Gradient Header */}
                    <div className={`p-4 sm:p-6 bg-gradient-to-r ${service.color}`}>
                      <div className="flex items-center justify-between">
                        <Icon className="w-8 h-8 text-white" />
                        <motion.div 
                          className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center"
                          whileHover={{ rotate: 90 }}
                        >
                          <ArrowRight className="w-4 h-4 text-white" />
                        </motion.div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-4 sm:p-6 flex-1 flex flex-col">
                      <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
                        {service.title}
                      </h3>
                      <p className="text-sm sm:text-base text-gray-600 mb-4 flex-1">
                        {service.description}
                      </p>

                      {/* Features */}
                      <div className="space-y-2 mt-auto">
                        {service.features.map((feature, i) => (
                          <motion.div 
                            key={i}
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.2 + (i * 0.1) }}
                            className="flex items-center text-gray-600"
                          >
                            <div className="w-1.5 h-1.5 rounded-full bg-brand-500 mr-2" />
                            <span className="text-sm">{feature}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 group-hover:bg-brand-50 transition-colors">
                      <div className="flex items-center justify-between text-brand-600">
                        <span className="font-medium">Learn More</span>
                        <motion.div
                          whileHover={{ x: 4 }}
                          className="transition-transform"
                        >
                          <ArrowRight className="w-5 h-5" />
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </button>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
