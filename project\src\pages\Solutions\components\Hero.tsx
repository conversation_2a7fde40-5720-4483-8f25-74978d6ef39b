import React, { useState } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { 
  Shield, Star, Clock, ArrowRight, Box, Package, 
  PackageCheck, PackageOpen, PackagePlus, PackageSearch,
  Sparkles, Zap, Leaf
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { ServiceMenu } from '../../../components/services/ServiceMenu';

export function Hero() {
  const [showServiceMenu, setShowServiceMenu] = useState(false);
  const controls = useAnimation();

  const handleExploreClick = () => {
    const solutionsSection = document.getElementById('solutions-grid');
    solutionsSection?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleGetStarted = () => {
    setShowServiceMenu(true);
  };

  // Box icons animation variants
  const boxIconVariants = {
    initial: { opacity: 0, scale: 0 },
    animate: (index: number) => ({
      opacity: [0.3, 0.6, 0.3],
      scale: [1, 1.1, 1],
      y: [0, -20, 0],
      transition: {
        duration: 4,
        delay: index * 0.3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    })
  };

  // Floating icons data with enhanced positioning
  const floatingIcons = [
    { Icon: Box, position: 'top-1/4 left-1/4', size: 'w-12 h-12', delay: 0 },
    { Icon: Package, position: 'top-1/3 right-1/4', size: 'w-16 h-16', delay: 0.2 },
    { Icon: PackageCheck, position: 'bottom-1/3 left-1/3', size: 'w-14 h-14', delay: 0.4 },
    { Icon: PackageOpen, position: 'top-1/2 right-1/3', size: 'w-10 h-10', delay: 0.6 },
    { Icon: PackagePlus, position: 'bottom-1/4 right-1/4', size: 'w-12 h-12', delay: 0.8 },
    { Icon: PackageSearch, position: 'bottom-1/2 left-1/4', size: 'w-16 h-16', delay: 1 },
    { Icon: Sparkles, position: 'top-1/3 left-1/2', size: 'w-10 h-10', delay: 1.2 },
    { Icon: Zap, position: 'bottom-1/3 right-1/2', size: 'w-8 h-8', delay: 1.4 },
    { Icon: Leaf, position: 'top-1/2 left-2/3', size: 'w-12 h-12', delay: 1.6 }
  ];

  return (
    <>
      <section className="relative min-h-[90vh] flex items-center mt-16 sm:mt-24">
        {/* Enhanced Background with Multiple Gradients */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500 via-brand-600 to-brand-700 overflow-hidden">
          {/* Animated Wave Patterns */}
          <div className="absolute bottom-0 left-0 right-0 h-64">
            <div className="absolute inset-0 w-[200%] animate-wave">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-brand-200/20">
                <path d="M0,50 Q25,60 50,50 Q75,40 100,50 L100,100 L0,100 Z" />
              </svg>
            </div>
            <div className="absolute inset-0 w-[200%] animate-wave-slow">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-brand-300/20">
                <path d="M0,60 Q25,50 50,60 Q75,70 100,60 L100,100 L0,100 Z" />
              </svg>
            </div>
            <div className="absolute inset-0 w-[200%] animate-wave-slower">
              <svg viewBox="0 0 100 100" preserveAspectRatio="none" className="w-full h-full fill-current text-brand-400/20">
                <path d="M0,70 Q25,60 50,70 Q75,80 100,70 L100,100 L0,100 Z" />
              </svg>
            </div>
          </div>

          {/* Enhanced Floating Icons with Glow Effect */}
          {floatingIcons.map(({ Icon, position, size, delay }, index) => (
            <motion.div
              key={index}
              custom={index}
              variants={boxIconVariants}
              initial="initial"
              animate="animate"
              className={`absolute ${position} ${size} text-brand-100/20`}
              style={{
                filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.3))',
                transformStyle: 'preserve-3d',
                perspective: '1000px'
              }}
            >
              <Icon className="w-full h-full" />
            </motion.div>
          ))}

          {/* Radial Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-brand-500/20 via-transparent to-brand-700/20" />
        </div>

        {/* Main Content */}
        <div className="relative z-10 w-full">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-20 md:py-28">
            <div className="text-center max-w-3xl mx-auto">
              {/* Enhanced Title Animation */}
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ 
                  duration: 0.8,
                  ease: [0.4, 0, 0.2, 1]
                }}
                className="text-4xl sm:text-5xl md:text-6xl font-bold text-white mb-6 leading-tight"
              >
                Professional Cleaning
                <motion.span
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="block text-brand-100"
                >
                  Solutions
                </motion.span>
              </motion.h1>
              
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-xl text-brand-100 mb-8"
              >
                Comprehensive cleaning services tailored to your business needs
              </motion.p>

              {/* Enhanced Trust Badges */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex flex-wrap justify-center gap-4 mb-12"
              >
                {[
                  { icon: Shield, text: 'Licensed & Insured' },
                  { icon: Star, text: '4.9/5 Rating' },
                  { icon: Clock, text: '24/7 Service' }
                ].map((badge, index) => (
                  <motion.div
                    key={badge.text}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 + (index * 0.1) }}
                    whileHover={{ scale: 1.05 }}
                    className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 flex items-center space-x-2
                             border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <badge.icon className="w-4 h-4 text-brand-100" />
                    <span className="text-sm text-white">{badge.text}</span>
                  </motion.div>
                ))}
              </motion.div>

              {/* Enhanced Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="flex flex-col sm:flex-row items-center justify-center gap-4"
              >
                <Button
                  size="lg"
                  onClick={handleExploreClick}
                  className="bg-white text-brand-600 hover:bg-brand-50 w-full sm:w-auto
                           shadow-lg hover:shadow-xl transition-all duration-300
                           transform hover:-translate-y-1"
                >
                  Explore Solutions
                  <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <Button
                  size="lg"
                  onClick={handleGetStarted}
                  variant="outline"
                  className="border-white text-white hover:bg-white/10 w-full sm:w-auto
                           backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300
                           transform hover:-translate-y-1"
                >
                  Get Started
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Menu */}
      <ServiceMenu
        isOpen={showServiceMenu}
        onClose={() => setShowServiceMenu(false)}
        onServiceSelect={(serviceId) => {
          setShowServiceMenu(false);
          // Navigation will be handled by the ServiceMenu component
        }}
      />
    </>
  );
}
